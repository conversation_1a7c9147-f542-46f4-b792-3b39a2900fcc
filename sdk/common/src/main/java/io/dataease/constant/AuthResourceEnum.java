package io.dataease.constant;

public enum AuthResourceEnum {

    PANEL(2, 1), SCRE<PERSON>(3, 2), DATASET(5, 3), DATASOURCE(6, 4), SYSTEM(7, 0), <PERSON>ER(8, 5), R<PERSON><PERSON>(8, 6),  <PERSON><PERSON>(9, 7),  SYNC_DATASOURCE(23, 9),  <PERSON><PERSON><PERSON>(24, 9), SUMMARY(22, 9), DATA_FILLING(60, 8);

    private long menuId;

    private int flag;

    public long getMenuId() {
        return menuId;
    }

    public void setMenuId(long menuId) {
        this.menuId = menuId;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    AuthResourceEnum(long menuId, int flag) {
        this.menuId = menuId;
        this.flag = flag;
    }
}
