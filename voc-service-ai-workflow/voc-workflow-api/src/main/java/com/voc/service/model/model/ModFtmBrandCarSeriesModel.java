package com.voc.service.model.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: MergingResltData
 * @Package: com.voc.service.model.entity
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/30 16:38
 * @Version:1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModFtmBrandCarSeriesModel implements Serializable {

    private String newId; // 主键
    private String voiceId; // 声音ID
    private String originalId; // 原文ID
    private String workId; // 接收处理标识
    private String brand; // 品牌
    private String brandCode; // 品牌编码
    private String carSeries; // 车系
    private String carSeriesCode; // 车系编码
    private LocalDateTime createTime; // 接收时间
    private String channelId;
    private String contentType;
    private Integer modelType;
    private Object extFields;
}
