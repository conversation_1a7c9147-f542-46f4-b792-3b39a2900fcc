package com.voc.service.model.api;

import com.voc.service.model.vo.ModLlmBatchInfoRecordVo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【mod_llm_batch_info_record(大模型批次信息记录表)】的数据库操作Service
 * @createDate 2024-08-01 13:45:36
 */
public interface IModLlmBatchInfoRecordService {

    List<ModLlmBatchInfoRecordVo> findBatchIdList(List<String> statusList);

    String findOutFileId(List<String> statusList, String batchId);

    Long findRecordByBatchId(String batchId);

    int modifyAIModelBatchStatus(String batchId, String status, String inputFileId, String errorFileId);

    int modifyToDone(String batchId);

    ModLlmBatchInfoRecordVo getByBatchId(String batchId);
}
