package com.voc.service.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 智谱apikeywg记录表
 *
 * @TableName mod_llm_api_token_record
 */
@TableName(value = "mod_llm_api_token_record")
@Data
public class ModLlmApiTokenRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "new_id", type = IdType.ASSIGN_UUID)
    private String newId;

    /**
     * 客户标识
     */
    private String clientId;

    /**
     * 已经上传文件数量
     */
    private Integer uploadFileNum;

    /**
     * api状态 0可用，1不可用
     */
    private Integer apiKeyStatus;

    /**
     * apikey
     */
    private String apiKey;

    /**
     * apikey所属人
     */
    private String apiKeyUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 接收处理标识
     */
    private String workId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
