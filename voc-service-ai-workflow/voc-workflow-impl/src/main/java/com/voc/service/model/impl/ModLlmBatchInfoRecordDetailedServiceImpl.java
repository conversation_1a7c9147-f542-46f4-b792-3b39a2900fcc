package com.voc.service.model.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.model.api.IModLlmBatchInfoRecordDetailedService;
import com.voc.service.model.entity.ModLlmBatchInfoRecordDetailed;
import com.voc.service.model.impl.cenvert.ModelConvertMapperService;
import com.voc.service.model.mapper.ModLlmBatchInfoRecordDetailedMapper;
import com.voc.service.model.model.ModLlmBatchInfoRecordDetailedModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ModLlmBatchInfoRecordDetailedServiceImpl extends ServiceImpl<ModLlmBatchInfoRecordDetailedMapper, ModLlmBatchInfoRecordDetailed>
        implements IModLlmBatchInfoRecordDetailedService {

    @Autowired
    ModelConvertMapperService modelConvertMapperService;

    @Override
    public boolean saveBatchInfoRecordDetailed(List<ModLlmBatchInfoRecordDetailedModel> list) {
        return super.saveBatch(modelConvertMapperService.cenvertToModLlmBatchInfoRecordDetailedModelList(list), 2000);
    }

    @Override
    public long count(String id) {
        QueryWrapper<ModLlmBatchInfoRecordDetailed> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ModLlmBatchInfoRecordDetailed::getNewId, id);
        return count(queryWrapper);
    }
}




