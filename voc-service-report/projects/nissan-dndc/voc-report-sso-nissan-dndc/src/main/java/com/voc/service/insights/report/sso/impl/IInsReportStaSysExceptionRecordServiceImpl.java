package com.voc.service.insights.report.sso.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.components.mybatis.annotation.SwitchClientDS;
import com.voc.service.insights.report.api.api.IInsReportStaSysExceptionRecordService;
import com.voc.service.insights.report.api.model.StaSysExceptionRecordModel;
import com.voc.service.insights.report.sso.entity.StaSysExceptionRecordEntity;
import com.voc.service.insights.report.sso.mapper.InsReportStaSysExceptionRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IInsReportStaSysExceptionRecordServiceImpl extends ServiceImpl<InsReportStaSysExceptionRecordMapper, StaSysExceptionRecordEntity>
        implements IInsReportStaSysExceptionRecordService {


    @Override
    @SwitchClientDS
    public Boolean saveOrUpdateDepart(StaSysExceptionRecordModel staSysExceptionRecordModel, String clientId) {
        if (ObjectUtil.isNotNull(staSysExceptionRecordModel)) {
            StaSysExceptionRecordEntity staSysExceptionRecordEntity = new StaSysExceptionRecordEntity();
            BeanUtil.copyProperties(staSysExceptionRecordModel, staSysExceptionRecordEntity);
            return this.save(staSysExceptionRecordEntity);
        }
        return false;
    }


}
