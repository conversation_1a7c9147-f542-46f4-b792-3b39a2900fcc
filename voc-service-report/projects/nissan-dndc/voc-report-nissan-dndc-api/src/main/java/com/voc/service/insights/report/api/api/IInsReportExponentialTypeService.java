package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.exponential.GranularityDetailModel;
import com.voc.service.insights.report.api.vo.GranularityDetailVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:38
 * @描述:
 **/
public interface IInsReportExponentialTypeService {
    void saveBatchExponentialType(String clientId, List<GranularityDetailModel> exponentialType, String brandCode);
    List<GranularityDetailVo> findExponentialType(String clientId, String brandCode);

    void deleteBatchExponentialType(String clientId, List<GranularityDetailModel> exponentialType, String brandCode);
}
