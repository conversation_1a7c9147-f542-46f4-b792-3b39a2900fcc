package com.voc.service.insights.report.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaSysExceptionRecordModel implements Serializable {

    private String id;

    private String data;

    private String exceptionType;

    private LocalDateTime createTime;
}
