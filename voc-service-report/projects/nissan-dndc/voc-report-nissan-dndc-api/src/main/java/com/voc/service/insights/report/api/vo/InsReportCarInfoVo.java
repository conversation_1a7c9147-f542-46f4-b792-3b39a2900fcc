package com.voc.service.insights.report.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2024/8/27 下午4:16
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsReportCarInfoVo implements Serializable {
    /**
     * 车系名称
     */
    private String name;
    /**
     * 车系编码
     */
    private String code;
    /**
     * 车系id
     */
    private String id;
}
