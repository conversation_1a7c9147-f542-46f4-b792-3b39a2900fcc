package com.voc.service.insights.report.api.api;

import com.voc.service.insights.report.api.model.demo.params.OverviewParamsModel;
import com.voc.service.insights.report.api.vo.RiskDetailVo;
import com.voc.service.insights.report.api.vo.RiskEarlyEventWarningVo;
import com.voc.service.insights.report.api.vo.UserVoiceVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/4 上午10:35
 * @描述:
 **/
public interface IInsReportRiskWarningBroadcastService {

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/4 上午11:03
     * @描述   获取投诉用户预警播报
     * @param overviewParamsModel
     * @return com.voc.service.insights.report.api.vo.RiskEarlyEventWarningVo
     **/
    RiskEarlyEventWarningVo getComplainingUser(OverviewParamsModel overviewParamsModel);

    List<RiskDetailVo> complaintsDetail(OverviewParamsModel overviewParamsModel);

    UserVoiceVo getUserDetail(OverviewParamsModel overviewParamsModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/5 下午2:14
     * @描述   获取数据轨迹
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    List<UserVoiceVo> getDataTrail(OverviewParamsModel overviewParamsModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/11 上午10:00
     * @描述   获取数据轨迹渠道
     * @param overviewParamsModel
     * @return java.util.List<com.voc.service.insights.report.api.vo.UserVoiceVo>
     **/
    List<UserVoiceVo> getDataTrailChannel(OverviewParamsModel overviewParamsModel);
}
