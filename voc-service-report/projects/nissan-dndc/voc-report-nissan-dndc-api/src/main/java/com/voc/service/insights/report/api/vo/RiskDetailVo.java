package com.voc.service.insights.report.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voc.service.insights.report.api.serializers.BigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/30 下午6:08
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskDetailVo {
    private String date;
    /**
     * 风险标题
     */
    @Schema(description = "风险标题")
    private String title;
    /**
     * 风险等级
     */
    @Schema(description = "风险等级")
    private String riskLevelS;
    @Schema(description = "风险值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskC;
    /**
     * 风险名称
     */
    @Schema(description = "风险名称")
    private String risk;
    @Schema(description = "风险总数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskSumC;
    /**
     * 负面观点值
     */
    @Schema(description = "负面观点值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal negativeC;
    /**
     * 焦点问题
     */
    @Schema(description = "焦点问题")
    private String focusName;
    @Schema(description = "用户数值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal userC;
    @Schema(description = "涉及车系")
    private String carSeriesName;
    @Schema(description = "投诉数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal complainC;
    @Schema(description = "情感值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal emotionR;
    private String labelType;
    @Schema(description = "风险Id")
    private String riskId;
    @Schema(description = "体验指数")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskNsrC;
    @Schema(description = "风险值环比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskRp;
    @Schema(description = "风险值占比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskP;
    @Schema(description = "风险值同比百分比")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal riskYp;
    @Schema(description = "焦点问题")
    private List<FocusVo> focusList;
    @Schema(description = "风险类型")
    private String riskType;

}
