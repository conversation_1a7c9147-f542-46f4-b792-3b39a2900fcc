package com.voc.service.insights.report.impl;

import cn.hutool.core.lang.Assert;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.common.exception.BussinessException;
import com.voc.service.common.util.IdWorker;
import com.voc.service.components.mybatis.annotation.SwitchClientDS;
import com.voc.service.insights.report.api.api.IInsReportThresholdService;
import com.voc.service.insights.report.api.model.indicators.IndicatorsEmojiParamModel;
import com.voc.service.insights.report.api.vo.IndicatorsEmojiParamVo;
import com.voc.service.insights.report.commons.enums.InsCommonErrorEnum;
import com.voc.service.insights.report.entity.InsReportThresholdEntity;
import com.voc.service.insights.report.impl.converts.InsReportDataConvertMapperService;
import com.voc.service.insights.report.mapper.InsReportExponentialThresholMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:38
 * @描述:
 **/
@Service
@Slf4j
public class InsReportThresholdServiceImpl extends ServiceImpl<InsReportExponentialThresholMapper, InsReportThresholdEntity> implements IInsReportThresholdService {
    @Autowired
    InsReportDataConvertMapperService mapperService;

    @Override
    @SwitchClientDS(datasource = "mysql_client_db")
    @CacheInvalidate(area="VDP" ,name = ":", key = "':C{appId}:findThreshold:'+#clientId + ':' + #brandCode")
    public void saveThreshold(String clientId, IndicatorsEmojiParamModel model, String brandCode) {
        Assert.isTrue(ObjectUtils.isNotEmpty(clientId), "客户ID不能为空");
        InsReportThresholdEntity insReportThresholdEntity = mapperService.insThresholdConvert(model);
        insReportThresholdEntity.setId(IdWorker.getId());
        insReportThresholdEntity.setCreateTime(LocalDateTime.now());
        insReportThresholdEntity.setBrandCode(brandCode);
        QueryWrapper<InsReportThresholdEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InsReportThresholdEntity::getBrandCode, brandCode);
        this.remove(queryWrapper);
        boolean save = this.save(insReportThresholdEntity);
        if(save){
            log.info("保存阈值成功");
        }else{
            throw new BussinessException(InsCommonErrorEnum.SAVE_THRESHOLD_ERROR);
        }
    }

    @Override
    @SwitchClientDS(datasource = "mysql_client_db")
    @Cached(area="VDP" ,name = ":", key = "':C{appId}:findThreshold:'+#clientId + ':' + #brandCode", expire = 60 * 60, cacheType = CacheType.REMOTE )
    public IndicatorsEmojiParamVo findThreshold(String clientId,String brandCode) {
        Assert.isTrue(ObjectUtils.isNotEmpty(brandCode), "品牌编码不允许为空");
        QueryWrapper<InsReportThresholdEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InsReportThresholdEntity::getBrandCode, brandCode);
        InsReportThresholdEntity reportThresholdEntity = this.getOne(queryWrapper);
        if(ObjectUtils.isNotEmpty(reportThresholdEntity)){
            return mapperService.insThresholdEntityConvert(reportThresholdEntity);
        }
        return null;
    }
}
