package com.voc.service.insights.report.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voc.service.components.mybatis.annotation.SwitchClientDS;
import com.voc.service.insights.report.api.api.IInsReportSysRoleBusinessTagService;
import com.voc.service.insights.report.api.api.IInsReportSysRoleServiceTagService;
import com.voc.service.insights.report.api.enums.LabelTypeEnum;
import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.RoleReportAuthModel;
import com.voc.service.insights.report.entity.ReportSysRoleBusinessTagEntity;
import com.voc.service.insights.report.mapper.InsReportRoleBusinessTagMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class InsReportSysRoleServiceTagServiceImpl extends ServiceImpl<InsReportRoleBusinessTagMapper, ReportSysRoleBusinessTagEntity> implements IInsReportSysRoleServiceTagService {

    @Override
    @SwitchClientDS(objectAttribute = "sysRoleModel.clientId")
    public void saveOrUpdateData(String roleId, RoleReportAuthModel sysRoleModel) {
        QueryWrapper<ReportSysRoleBusinessTagEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ReportSysRoleBusinessTagEntity::getRoleId, roleId);
        List<ReportSysRoleBusinessTagEntity> list = this.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            QueryWrapper<ReportSysRoleBusinessTagEntity> query = new QueryWrapper<>();
            query.lambda().eq(ReportSysRoleBusinessTagEntity::getRoleId, roleId);
            query.lambda().eq(ReportSysRoleBusinessTagEntity::getTagType, LabelTypeEnum.SERVICE.getCode());
            query.lambda().eq(ReportSysRoleBusinessTagEntity::getBrandCode, sysRoleModel.getBrandCode());
            this.remove(query);
        }
        if (CollectionUtils.isNotEmpty(sysRoleModel.getServiceTagIds())) {
            List<ReportSysRoleBusinessTagEntity> tags = new ArrayList<>();
            sysRoleModel.getServiceTagIds().stream().forEach(tagCode -> {
                ReportSysRoleBusinessTagEntity sysRoleBusinessTag = new ReportSysRoleBusinessTagEntity();
                sysRoleBusinessTag.setTagCode(tagCode);
                sysRoleBusinessTag.setRoleId(roleId);
                sysRoleBusinessTag.setTagType(LabelTypeEnum.SERVICE.getCode());
                sysRoleBusinessTag.setBrandCode(sysRoleModel.getBrandCode());
                tags.add(sysRoleBusinessTag);
            });
            this.saveBatch(tags);
        }
    }


    @Override
    @SwitchClientDS(datasource = "mysql_client_db")
    public Map<String, List<String>> getRoleServiceTagList(InsReportRoleQueryModel model) {
        QueryWrapper<ReportSysRoleBusinessTagEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ReportSysRoleBusinessTagEntity::getRoleId, model.getRoleId());
        queryWrapper.lambda().eq(ReportSysRoleBusinessTagEntity::getTagType, LabelTypeEnum.SERVICE.getCode());
        List<ReportSysRoleBusinessTagEntity> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, List<String>> collect = list.stream()
                .collect(Collectors.groupingBy(
                        ReportSysRoleBusinessTagEntity::getBrandCode,
                        Collectors.mapping(ReportSysRoleBusinessTagEntity::getTagCode, Collectors.toList())
                ));
        return collect;
    }
}
