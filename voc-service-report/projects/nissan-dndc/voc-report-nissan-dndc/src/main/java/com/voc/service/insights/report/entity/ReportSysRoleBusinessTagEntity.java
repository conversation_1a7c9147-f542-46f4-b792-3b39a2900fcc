package com.voc.service.insights.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 角色业务标签
 * @TableName SYS_ROLE_BUSINESS_TAG
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sta_sys_role_business_tag")
public class ReportSysRoleBusinessTagEntity implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private String roleId;

    /**
     *
     */
    private String tagCode;

    private String brandCode;

    private String tagType;

    private static final long serialVersionUID = 1L;

}
