package com.voc.service.insights.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.vo.RoleReportListVo;
import com.voc.service.insights.report.entity.InsReportRoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface InsReportRoleMapper extends BaseMapper<InsReportRoleEntity> {

    List<RoleReportListVo> queryRoleList(InsReportRoleQueryModel model);

}
