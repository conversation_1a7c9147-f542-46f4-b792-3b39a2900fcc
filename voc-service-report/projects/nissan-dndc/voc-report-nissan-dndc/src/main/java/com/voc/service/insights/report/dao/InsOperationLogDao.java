package com.voc.service.insights.report.dao;

import com.github.pagehelper.PageInfo;
import com.voc.service.insights.report.api.model.InsReportOperationLogModel;
import com.voc.service.logs.model.OpsLogModel;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2024/10/11 下午3:18
 * @描述:
 **/
public interface InsOperationLogDao {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/10/11 下午3:19
     * @描述  获取操作日志列表
     * @param reportOperationLogModel
     * @return com.github.pagehelper.PageInfo
     **/
    PageInfo findOperationLog(InsReportOperationLogModel reportOperationLogModel);

    List<OpsLogModel> downLoadOperationLog(InsReportOperationLogModel reportOperationLogModel);
}
