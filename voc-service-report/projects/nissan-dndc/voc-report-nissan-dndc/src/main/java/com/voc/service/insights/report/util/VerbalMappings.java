package com.voc.service.insights.report.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/15 下午3:53
 * @描述:
 **/
@Configuration
@Component
@ConfigurationProperties(prefix = "verbal.cue")
@Data
public class VerbalMappings {
    Map<String, String> mappings = new HashMap<>();
}
