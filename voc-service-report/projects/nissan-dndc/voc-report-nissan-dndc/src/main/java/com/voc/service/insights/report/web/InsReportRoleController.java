package com.voc.service.insights.report.web;

import com.github.pagehelper.PageInfo;
import com.voc.service.common.exception.CommonErrorEnum;
import com.voc.service.common.response.Result;
import com.voc.service.insights.report.api.api.IInsReportRoleService;
import com.voc.service.insights.report.api.model.InsReportRoleQueryModel;
import com.voc.service.insights.report.api.model.RoleReportAuthModel;
import com.voc.service.insights.report.api.vo.RoleReportAuthVo;
import com.voc.service.insights.report.api.vo.RoleReportUserVo;
import com.voc.service.insights.report.api.vo.UserReportRoleInfoVo;
import com.voc.service.logs.annotation.AutoLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RestController
@Tag(name = "角色信息", description = "角色信息")
@RequestMapping("/role")
public class InsReportRoleController {


    @Resource
    private IInsReportRoleService iInsReportRoleService;

    @AutoLog(value = "角色信息-分页查询")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "分页查询")
    @PostMapping("/list")
    public Result<?> queryRoleList(@RequestBody @Validated InsReportRoleQueryModel model) {
        try {
            PageInfo roleList = iInsReportRoleService.queryRoleList(model);
            return Result.OK(roleList);
        } catch (Exception e) {
            log.error("角色信息-分页查询异常:", e);
            return Result.error(CommonErrorEnum.UNKNOW_EXECPTION);
        }
    }


    @AutoLog(value = "角色信息-根据 RoleId 获取编辑回显的数据")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "根据 RoleId 获取编辑回显的数据")
    @RequestMapping(value = "/getListByRoleId", method = RequestMethod.POST)
    public Result<List<RoleReportAuthVo>> getListByRoleId(@RequestBody InsReportRoleQueryModel model) {
        List<RoleReportAuthVo> roleReportAuthVoList = iInsReportRoleService.getListByRoleId(model);
        return Result.OK(roleReportAuthVoList);
    }


    @AutoLog(value = "角色信息-获取权限菜单下拉")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "获取权限菜单下拉")
    @PostMapping("/queryMenuPermissionList")
    public Result<?> queryMenuPermissionList(@RequestBody InsReportRoleQueryModel model) {
        try {
            List<RoleReportAuthVo> roleList = iInsReportRoleService.queryMenuPermissionListNotCache(model);
            return Result.OK(roleList);
        } catch (Exception e) {
            log.error("角色信息-获取权限菜单下拉异常:", e);
            return Result.error(CommonErrorEnum.UNKNOW_EXECPTION);
        }
    }


    @AutoLog(value = "角色信息-新增信息")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "新增信息")
    @PostMapping("/saveOrUpdateRole")
    public Result<?> saveOrUpdateRole(@RequestBody @Validated List<RoleReportAuthModel> roleModelList) {
        try {
            RoleReportAuthModel roleReportAuthModel = roleModelList.get(0);
            return iInsReportRoleService.saveOrUpdateRole(roleReportAuthModel.getClientId(),roleModelList);
        } catch (Exception e) {
            log.error("角色信息-新增信息:", e);
            return Result.error(CommonErrorEnum.UNKNOW_EXECPTION);
        }
    }


    @AutoLog(value = "角色信息-根据Id查询单条信息")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "根据Id查询单条信息")
    @PostMapping("/queryUserPermission")
    public Result<?> queryRoleInfo(@RequestBody RoleReportUserVo model) {
        try {
            UserReportRoleInfoVo userRoleInfoVo = iInsReportRoleService.queryUserPermission(model);
            return Result.OK(userRoleInfoVo);
        } catch (Exception e) {
            log.error("角色信息-根据Id查询单条信息异常:", e);
            return Result.error(CommonErrorEnum.UNKNOW_EXECPTION);
        }
    }
}
