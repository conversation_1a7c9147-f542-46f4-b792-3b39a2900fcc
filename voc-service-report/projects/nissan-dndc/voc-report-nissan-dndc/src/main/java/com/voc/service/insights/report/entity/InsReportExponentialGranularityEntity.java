package com.voc.service.insights.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2024/11/28 上午11:29
 * @描述:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sta_sys_granularity")
public class InsReportExponentialGranularityEntity implements Serializable {
    private String id;
    private String exponentialTypeId;
    /**
     * 年份
     */
    private String year;
    /**
     * 粒度类型 w:周 m:月
     */
    private String granularityUtil;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
