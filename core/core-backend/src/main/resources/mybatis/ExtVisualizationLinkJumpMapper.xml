<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.dataease.visualization.dao.ext.mapper.ExtVisualizationLinkJumpMapper">
    <resultMap id="LinkJumpBaseResultMap" type="io.dataease.api.visualization.vo.VisualizationLinkJumpVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="source_dv_id" jdbcType="BIGINT" property="sourceDvId"/>
        <result column="source_view_id" jdbcType="BIGINT" property="sourceViewId"/>
        <result column="link_jump_info" jdbcType="VARCHAR" property="linkJumpInfo"/>
        <result column="checked" jdbcType="BIT" property="checked"/>
        <result column="copy_from" jdbcType="BIGINT" property="copyFrom"/>
        <result column="copy_id" jdbcType="BIGINT" property="copyId"/>
    </resultMap>

    <resultMap id="LinkJumpInfoBaseResultMap" type="io.dataease.api.visualization.vo.VisualizationLinkJumpInfoVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="link_jump_id" jdbcType="BIGINT" property="linkJumpId"/>
        <result column="link_type" jdbcType="VARCHAR" property="linkType"/>
        <result column="jump_type" jdbcType="VARCHAR" property="jumpType"/>
        <result column="window_size" jdbcType="VARCHAR" property="windowSize"/>
        <result column="target_dv_id" jdbcType="BIGINT" property="targetDvId"/>
        <result column="source_field_id" jdbcType="BIGINT" property="sourceFieldId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="checked" jdbcType="BIT" property="checked"/>
        <result column="attach_params" jdbcType="BIT" property="attachParams"/>
        <result column="copy_from" jdbcType="BIGINT" property="copyFrom"/>
        <result column="copy_id" jdbcType="BIGINT" property="copyId"/>
    </resultMap>


    <resultMap id="BaseResultMapDTO" type="io.dataease.api.visualization.dto.VisualizationLinkJumpDTO"
               extends="LinkJumpBaseResultMap">
        <collection property="linkJumpInfoArray" ofType="io.dataease.api.visualization.dto.VisualizationLinkJumpInfoDTO"
                    column="{id=id,source_view_id=source_view_id,uid=queryUid, isDesktop=isDesktop}"
                    select="getLinkJumpInfo">
        </collection>
    </resultMap>

    <resultMap id="LinkJumpInfoMap" type="io.dataease.api.visualization.dto.VisualizationLinkJumpInfoDTO"
               extends="LinkJumpInfoBaseResultMap">
        <result column="source_field_id" jdbcType="BIGINT" property="sourceFieldId"/>
        <result column="source_de_type" jdbcType="INTEGER" property="sourceDeType"/>
        <result column="source_field_name" jdbcType="VARCHAR" property="sourceFieldName"/>
        <result column="publicJumpId" jdbcType="VARCHAR" property="publicJumpId"/>
        <collection property="targetViewInfoList"
                    ofType="io.dataease.api.visualization.vo.VisualizationLinkJumpTargetViewInfoVO">
            <result column="target_id" jdbcType="BIGINT" property="targetId"/>
            <result column="target_view_id" jdbcType="BIGINT" property="targetViewId"/>
            <result column="target_field_id" jdbcType="BIGINT" property="targetFieldId"/>
            <result column="source_field_active_id" jdbcType="VARCHAR" property="sourceFieldActiveId"/>
            <result column="target_type" jdbcType="VARCHAR" property="targetType"/>
            <result column="outer_params_name" jdbcType="VARCHAR" property="outerParamsName"/>
        </collection>
    </resultMap>
    <resultMap id="ViewTableFieldDetailsMap" type="io.dataease.api.visualization.vo.VisualizationViewTableVO">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="dv_id" jdbcType="BIGINT" property="dvId"/>
        <collection property="tableFields"
                    ofType="io.dataease.extensions.datasource.dto.DatasetTableFieldDTO">
            <result column="field_id" jdbcType="BIGINT" property="id"/>
            <result column="origin_name" jdbcType="VARCHAR" property="originName"/>
            <result column="field_name" jdbcType="VARCHAR" property="name"/>
            <result column="field_type" jdbcType="VARCHAR" property="type"/>
            <result column="de_type" jdbcType="VARCHAR" property="deType"/>
        </collection>
    </resultMap>

    <resultMap id="AllJumpMap" type="io.dataease.api.visualization.dto.VisualizationLinkJumpDTO">
        <result column="sourceInfo" jdbcType="VARCHAR" property="sourceInfo"/>
        <collection property="targetInfoList" ofType="String">
            <result column="targetInfo" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getLinkJumpInfo" resultMap="LinkJumpInfoMap">
        SELECT
            core_dataset_table_field.id AS source_field_id,
            core_dataset_table_field.de_type AS source_de_type,
            core_dataset_table_field.NAME AS source_field_name,
            visualization_link_jump_info.id,
            visualization_link_jump_info.link_jump_id,
            visualization_link_jump_info.link_type,
            visualization_link_jump_info.jump_type,
            visualization_link_jump_info.window_size,
            visualization_link_jump_info.target_dv_id,
            visualization_link_jump_info.content,
            <if test="!isDesktop">
            xpack_share.uuid AS publicJumpId,
            </if>
            ifnull( visualization_link_jump_info.checked, 0 ) AS checked,
            ifnull( visualization_link_jump_info.attach_params, 0 ) AS attach_params,
            visualization_link_jump_target_view_info.target_id,
            visualization_link_jump_target_view_info.target_view_id,
            visualization_link_jump_target_view_info.target_field_id,
            visualization_link_jump_target_view_info.target_type,
            visualization_link_jump_target_view_info.source_field_active_id,
            visualization_outer_params_info.param_name as outer_params_name
        FROM
            core_chart_view
                LEFT JOIN core_dataset_table_field ON core_chart_view.table_id = core_dataset_table_field.dataset_group_id
                LEFT JOIN visualization_link_jump ON core_chart_view.id = visualization_link_jump.source_view_id
                AND visualization_link_jump.id = #{id}
                LEFT JOIN visualization_link_jump_info ON visualization_link_jump.id = visualization_link_jump_info.link_jump_id
                AND core_dataset_table_field.id = visualization_link_jump_info.source_field_id
                LEFT JOIN visualization_link_jump_target_view_info ON visualization_link_jump_info.id = visualization_link_jump_target_view_info.link_jump_info_id
                <if test="!isDesktop">
                LEFT JOIN xpack_share ON xpack_share.creator = #{uid}
                AND visualization_link_jump_info.target_dv_id = xpack_share.resource_id
                </if>
                left join visualization_outer_params_info on visualization_outer_params_info.params_info_id = visualization_link_jump_target_view_info.target_view_id
        WHERE
            core_chart_view.id = #{source_view_id}
          AND core_chart_view.type != 'VQuery'
          <if test="!isDesktop">
           ORDER BY
                      CONVERT (
                      core_dataset_table_field.NAME USING gbk)
          </if>
          <if test="isDesktop">
          ORDER BY core_dataset_table_field.name;
         </if>

    </select>

    <select id="queryWithDvId" resultMap="BaseResultMapDTO">
        SELECT core_chart_view.id                     AS source_view_id,
               ${uid}                                 as queryUid,
               ${isDesktop}                                 as isDesktop,
               visualization_link_jump.id,
               #{dvId} as source_dv_id, visualization_link_jump.link_jump_info,
               ifnull(core_chart_view.jump_active, 0) AS checked
        FROM core_chart_view
                 LEFT JOIN visualization_link_jump ON core_chart_view.id = visualization_link_jump.source_view_id
        WHERE visualization_link_jump.source_dv_id = #{dvId}
          and core_chart_view.jump_active = 1
    </select>

    <select id="getViewTableDetails" resultMap="ViewTableFieldDetailsMap">
        SELECT
        	core_chart_view.id,
        	core_chart_view.title,
        	core_chart_view.type,
        	core_chart_view.scene_id AS dv_id,
        	core_dataset_table_field.id AS field_id,
        	core_dataset_table_field.origin_name,
        	core_dataset_table_field.`name` AS field_name,
        	core_dataset_table_field.type AS field_type,
        	core_dataset_table_field.de_type
        FROM
        	core_chart_view
        	LEFT JOIN core_dataset_table_field ON core_chart_view.table_id = core_dataset_table_field.dataset_group_id
        	INNER JOIN data_visualization_info dvi ON core_chart_view.scene_id = dvi.id
        WHERE
        	core_chart_view.scene_id = #{dvId}
        	AND core_chart_view.type != 'VQuery'
        	AND core_chart_view.table_id IS NOT NULL
        	AND dvi.id = #{dvId}
        	AND LOCATE(
        	core_chart_view.id,
        	dvi.component_data)
    </select>

    <select id="queryWithViewId" resultMap="BaseResultMapDTO">
        SELECT core_chart_view.id                         AS source_view_id,
               ${uid}                                     as queryUid,
               ${isDesktop}                                 as isDesktop,
               visualization_link_jump.id,
               #{dvId} as source_dv_id, visualization_link_jump.link_jump_info,
               ifnull(visualization_link_jump.checked, 0) AS checked
        FROM core_chart_view
                 LEFT JOIN visualization_link_jump ON core_chart_view.id = visualization_link_jump.source_view_id
            AND visualization_link_jump.source_dv_id = #{dvId}
        WHERE core_chart_view.id = #{viewId}
    </select>

        <select id="queryOutParamsTargetWithDvId" resultType="io.dataease.api.visualization.vo.VisualizationOutParamsJumpVO">
            SELECT
            	vopi.params_info_id as id,
            	vopi.param_name as name,
            	vopi.param_name as title,
            	'outerParams' as type
            FROM
            	visualization_outer_params_info vopi
            	LEFT JOIN visualization_outer_params vop ON vopi.params_id = vop.params_id
            WHERE
            	vop.visualization_id = #{dvId}
        </select>

    <delete id="deleteJumpTargetViewInfo">
        DELETE FROM visualization_link_jump_target_view_info
        WHERE link_jump_info_id IN (
            SELECT lji.id
            FROM visualization_link_jump_info lji
            JOIN visualization_link_jump lj ON lji.link_jump_id = lj.id
            WHERE lj.source_dv_id = #{dvId}
              AND lj.source_view_id = #{viewId}
        );
    </delete>

    <delete id="deleteJumpInfo">
        DELETE FROM visualization_link_jump_info
        WHERE link_jump_id IN (
            SELECT lj.id
            FROM visualization_link_jump lj
            WHERE lj.source_dv_id = #{dvId}
              AND lj.source_view_id = #{viewId}
        );
    </delete>

    <delete id="deleteJump">
        DELETE FROM visualization_link_jump
        WHERE source_dv_id = #{dvId}
          AND source_view_id = #{viewId}
    </delete>


    <delete id="deleteJumpTargetViewInfoWithVisualization">
        DELETE FROM visualization_link_jump_target_view_info
        WHERE link_jump_info_id IN (
            SELECT lji.id
            FROM visualization_link_jump_info lji
            JOIN visualization_link_jump lj ON lji.link_jump_id = lj.id
            WHERE lj.source_dv_id = #{dvId}
               OR lji.target_dv_id = #{dvId}
        )
    </delete>

    <delete id="deleteJumpInfoWithVisualization">
        DELETE FROM visualization_link_jump_info
        WHERE link_jump_id IN (
            SELECT lj.id
            FROM visualization_link_jump lj
            WHERE lj.source_dv_id = #{dvId}
               OR lj.target_dv_id = #{dvId}
        )
    </delete>

   <delete id="deleteJumpWithVisualization">
       DELETE FROM visualization_link_jump
       WHERE source_dv_id = #{dvId}
   </delete>

    <select id="getTargetVisualizationJumpInfo" resultMap="AllJumpMap">
        SELECT DISTINCT
        concat( lj.source_view_id, '#', jtvi.source_field_active_id ) AS sourceInfo,
        concat( jtvi.target_view_id, '#', jtvi.target_field_id ) AS targetInfo
        FROM
        visualization_link_jump_target_view_info jtvi
        LEFT JOIN visualization_link_jump_info lji ON jtvi.link_jump_info_id = lji.id
        LEFT JOIN visualization_link_jump lj ON lji.link_jump_id = lj.id
        WHERE
        lji.checked = 1
        and lj.source_dv_id = #{request.sourceDvId}
        AND lj.source_view_id = #{request.sourceViewId}
        AND lji.target_dv_id = #{request.targetDvId}
        <if test="request.sourceFieldId != null">
            AND lji.source_field_id = #{request.sourceFieldId}
        </if>
    </select>

    <insert id="copyLinkJump">
        insert into visualization_link_jump (id, source_dv_id, source_view_id, link_jump_info, `checked`, copy_from,
                                             copy_id)
        select UUID()                                       as id,
               visualization_view_copy.t_dv_id              as source_dv_id,
               visualization_view_copy.t_core_chart_view_id as source_view_id,
               visualization_link_jump.link_jump_info,
               visualization_link_jump.checked,
               visualization_link_jump.id                   as copy_from,
               #{copyId}                                    as copy_id
        from visualization_link_jump
                 inner join
             (SELECT pvs.dv_id              as s_dv_id,
                     pvs.core_chart_view_id as s_core_chart_view_id,
                     pvt.dv_id              as t_dv_id,
                     pvt.core_chart_view_id as t_core_chart_view_id
              FROM visualization_view pvt
                       inner JOIN visualization_view pvs ON pvt.copy_from = pvs.id
              WHERE pvt.copy_id = #{copyId}) visualization_view_copy
             on visualization_link_jump.source_dv_id = visualization_view_copy.s_dv_id
                 and visualization_link_jump.source_view_id = visualization_view_copy.s_core_chart_view_id
    </insert>

    <insert id="copyLinkJumpInfo">
        INSERT INTO visualization_link_jump_info (id,
                                                  link_jump_id,
                                                  link_type,
                                                  jump_type,
                                                  window_size,
                                                  target_dv_id,
                                                  source_field_id,
                                                  content,
                                                  `checked`,
                                                  `attach_params`,
                                                  copy_from,
                                                  copy_id)
        SELECT uuid() AS id,
               plj_copy.t_id,
               link_type,
               jump_type,
               window_size,
               target_dv_id,
               source_field_id,
               content,
               `checked`,
               `attach_params`,
               id     AS copy_from,
               #{copyId}
        FROM visualization_link_jump_info
                 inner JOIN (SELECT id        AS t_id,
                                    copy_from AS s_id
                             FROM visualization_link_jump
                             WHERE copy_id = #{copyId}) plj_copy
                            ON visualization_link_jump_info.link_jump_id = plj_copy.s_id
    </insert>

    <insert id="copyLinkJumpTarget">
        INSERT INTO visualization_link_jump_target_view_info (target_id,
                                                              link_jump_info_id,
                                                              target_view_id,
                                                              target_field_id,
                                                              copy_from,
                                                              copy_id)
        SELECT uuid()         AS id,
               plji_copy.t_id AS link_jump_info_id,
               target_view_id,
               target_field_id,
               copy_from,
               #{copyId}      AS copy_id
        FROM visualization_link_jump_target_view_info
                 inner JOIN (SELECT id        AS t_id,
                                    copy_from AS s_id
                             FROM visualization_link_jump_info
                             WHERE copy_id = #{copyId}) plji_copy
                            ON visualization_link_jump_target_view_info.link_jump_info_id = plji_copy.s_id
    </insert>

    <select id="findLinkJumpWithDvId" resultType="io.dataease.api.visualization.vo.VisualizationLinkJumpVO">
        SELECT *
        FROM visualization_link_jump
        WHERE source_dv_id = #{dvId}
    </select>

    <select id="findLinkJumpInfoWithDvId" resultType="io.dataease.api.visualization.vo.VisualizationLinkJumpInfoVO">
        SELECT *
        FROM visualization_link_jump_info
        WHERE link_type = 'outer'
          AND link_jump_id IN (SELECT id
                               FROM visualization_link_jump
                               WHERE source_dv_id = #{dvId})
    </select>
</mapper>
