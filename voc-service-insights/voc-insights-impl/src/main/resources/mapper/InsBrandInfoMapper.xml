<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voc.service.insights.engine.mapper.InsBrandInfoMapper">

    <sql id="insBrandInfoEntityDbMap">
        t.id id,
		t.name name,
		t.name_en nameEn,
		t.alias alias,
		t.exclusion_words exclusionWords,
		t.code code,
		t.order_by orderBy,
		t.operator operator,
		t.create_time createTime,
		t.update_time updateTime,
		t.del_flag delFlag,
		t.img img,
		t.app_id appId
    </sql>

    <insert id="insertInsBrandInfoEntity" parameterType="com.voc.service.insights.engine.entity.InsBrandInfoEntity"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_brand_info
        (id, name, name_en, alias, exclusion_words, code, order_by, operator, create_time, update_time, del_flag, img,
         app_id)
        VALUES (#{id}, #{name}, #{nameEn}, #{alias}, #{exclusionWords}, #{code}, #{orderBy}, #{operator}, #{createTime},
                #{updateTime}, #{delFlag}, #{img}, #{appId})
    </insert>

    <update id="updateInsBrandInfoEntity" parameterType="com.voc.service.insights.engine.entity.InsBrandInfoEntity">
        <if test="id != null">
            UPDATE
            ins_brand_info t
            <set>
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="nameEn != null">
                    t.name_en = #{nameEn},
                </if>
                <if test="alias != null">
                    t.alias = #{alias},
                </if>
                <if test="exclusionWords != null">
                    t.exclusion_words = #{exclusionWords},
                </if>
                <if test="code != null">
                    t.code = #{code},
                </if>
                <if test="orderBy != null">
                    t.order_by = #{orderBy},
                </if>
                <if test="operator != null">
                    t.operator = #{operator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="delFlag != null">
                    t.del_flag = #{delFlag},
                </if>
                <if test="img != null">
                    t.img = #{img},
                </if>
                <if test="appId != null">
                    t.app_id = #{appId},
                </if>
            </set>
            WHERE
            t.id = #{id}
        </if>
    </update>

    <update id="delInsBrandInfoEntity" parameterType="String">
        <if test="id != null">
            UPDATE
            ins_brand_info t
            <set>
                t.del_flag = 1
            </set>
            WHERE
            t.id = #{id}
        </if>
    </update>

    <select id="selectMultiPagingInsBrandInfoEntity"
            resultType="com.voc.service.insights.engine.entity.InsBrandInfoEntity">
        SELECT
        <include refid="insBrandInfoEntityDbMap"/>
        FROM
        ins_brand_info t
        where 1
        <if test="name != null and name != '' ">
            and t.name = #{name}
        </if>
        <if test="nameEn != null and nameEn != '' ">
            and t.name_en = #{nameEn}
        </if>
        <if test="alias != null and alias != '' ">
            and t.alias = #{alias}
        </if>
        <if test="exclusionWords != null and exclusionWords != '' ">
            and t.exclusion_words = #{exclusionWords}
        </if>
        <if test="code != null and code != '' ">
            and t.code = #{code}
        </if>
        <if test="orderBy != null and orderBy != '' ">
            and t.order_by = #{orderBy}
        </if>
        <if test="operator != null and operator != '' ">
            and t.operator = #{operator}
        </if>
        <if test="createTime != null and createTime != '' ">
            and t.create_time = #{createTime}
        </if>
        <if test="updateTime != null and updateTime != '' ">
            and t.update_time = #{updateTime}
        </if>
        <if test="delFlag != null and delFlag != '' ">
            and t.del_flag = #{delFlag}
        </if>
        <if test="img != null and img != '' ">
            and t.img = #{img}
        </if>
        <if test="appId != null and appId != '' ">
            and t.app_id = #{appId}
        </if>
        order by update_time desc
        limit #{start},#{pagesize}
    </select>

    <select id="selectMultiInsBrandInfoEntity"
            resultType="com.voc.service.insights.engine.entity.InsBrandInfoEntity">
        SELECT
        <include refid="insBrandInfoEntityDbMap"/>
        FROM
        ins_brand_info t
        inner join ins_car_series_info car on t.code = car.brand_id
        where 1
        <if test="brandFilters != null and brandFilters.size() > 0 ">
            and t.code in
            <foreach collection="brandFilters" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="carTypeFilter != null and carTypeFilter.size() > 0 ">
            and (car.car_level1 in
            <foreach collection="carTypeFilter" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            or car.car_level2 in
            <foreach collection="carTypeFilter" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="energyTypeFilter != null and energyTypeFilter.size() > 0 ">
            and (car.energy_type1 in
            <foreach collection="energyTypeFilter" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            or car.energy_type2 in
            <foreach collection="energyTypeFilter" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="nameFilter != null and nameFilter != '' ">
            and car.name like concat('%',#{nameFilter},'%')
        </if>
        <if test="name != null and name != '' ">
            and t.name = #{name}
        </if>
        <if test="nameEn != null and nameEn != '' ">
            and t.name_en = #{nameEn}
        </if>
        <if test="alias != null and alias != '' ">
            and t.alias = #{alias}
        </if>
        <if test="exclusionWords != null and exclusionWords != '' ">
            and t.exclusion_words = #{exclusionWords}
        </if>
        <if test="code != null and code != '' ">
            and t.code = #{code}
        </if>
        <if test="orderBy != null and orderBy != '' ">
            and t.order_by = #{orderBy}
        </if>
        <if test="operator != null and operator != '' ">
            and t.operator = #{operator}
        </if>
        <if test="createTime != null and createTime != '' ">
            and t.create_time = #{createTime}
        </if>
        <if test="updateTime != null and updateTime != '' ">
            and t.update_time = #{updateTime}
        </if>
        <if test="delFlag != null and delFlag != '' ">
            and t.del_flag = #{delFlag}
        </if>
        <if test="img != null and img != '' ">
            and t.img = #{img}
        </if>
        <if test="appId != null and appId != '' ">
            and t.app_id = #{appId}
        </if>
        group by t.id
    </select>

    <select id="findAllBrandAndCarSeries" resultType="com.voc.service.insights.engine.vo.InsALlBrandAndCarSeriesVo">
        select
            b.id as brandId,
            b.code as brandCode,
            b.name as brandName,
            b.alias as brandAlias,
            b.exclusion_words as brandExclusionWords,
            c.code as carSeriesCode,
            c.name as carSeriesName,
            c.alias as carSeriesAlias,
            c.exclusion_words as carSeriesExclusionWords,
            c.car_level1 as carLevel1,
            c.car_level2 as carLevel2,
            c.energy_type1 as energyType1,
            c.energy_type2 as energyType2
        from ins_brand_info b
                 left join ins_car_series_info c on b.id = c.brand_id
        where b.del_flag = 0
    </select>
</mapper>
