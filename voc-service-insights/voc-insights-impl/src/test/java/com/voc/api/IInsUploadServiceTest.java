package com.voc.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.URLUtil;
import com.voc.VocInsImplApplication;
import com.voc.service.common.util.IdWorker;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.common.api.IUploadFileService;
import lombok.Cleanup;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName
 * @Description 车系测试类
 * @createTime 2024/3/20 15:59
 */
@SpringBootTest(
        classes = {VocInsImplApplication.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class IInsUploadServiceTest extends AbstractTestNGSpringContextTests {

    @Autowired
    private IUploadFileService uploadFileService;


    @Test(priority = 1)
    public void test_upload() throws Exception {
        String fileName = this.getFileName(IdWorker.getId().concat(".png"));
        @Cleanup
        InputStream file = URLUtil.url("http://************:30305/ins/assets/login-Cn7fVP3a.png").openStream();
        boolean rs = uploadFileService.putObject(fileName, file);
        Assert.assertEquals(true, rs);
    }

    @Test(priority = 2)
    public void test_get() throws Exception {

        String rs = uploadFileService.getObjectUrl(this.getFileName("51b2cc3210d88d7fb2518c8bdd3f42c1.png"));
        System.out.println(rs);
        Assert.assertNotNull(true, rs);

    }

    @Test(priority = 3)
    public void test_delete() throws Exception {

        uploadFileService.removeObject(this.getFileName("4220343513.pdf"));

        Assert.assertNotNull(true);

    }

    @Test(priority = 3)
    public void test_getList() throws Exception {

        Map<String, String> map = uploadFileService.getObjectUrls(CollUtil.newHashSet(
                this.getFileName("e9ea3d71a346d7ff4f4cba688475621f.png"),
                this.getFileName("70a8888b2a23607267bf6ac4aeef4a3a.png"),
                this.getFileName("51b2cc3210d88d7fb2518c8bdd3f42c1.png"),
                this.getFileName("6c99f5d0e78e42db3410522ff6ba7665.png"),
                this.getFileName("51b2cc3210d88d7fb2518c8bdd3f42c1.png"),
                this.getFileName("1.txt")
        ));
        System.out.println(map);
        Assert.assertNotNull(true);

    }

    /**
     * 拼装文件路径  insights/car-series/51b2cc3210d88d7fb2518c8bdd3f42c1.png
     *
     * @param name
     * @return
     */
    private String getFileName(String name) {
        return ServiceContextHolder.getSystemId().concat("/").concat(name);
    }
}
