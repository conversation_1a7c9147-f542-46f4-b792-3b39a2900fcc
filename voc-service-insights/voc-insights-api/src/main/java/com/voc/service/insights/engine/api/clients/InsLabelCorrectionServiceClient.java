package com.voc.service.insights.engine.api.clients;

import com.voc.service.common.response.Result;
import com.voc.service.insights.engine.model.InsLabelCorrectionRecordQueryModel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "service.ins.labelCorrection", url = "${service.ins.v1}/label", configuration = InsDataServiceClientConfig.class)
//@FeignClient(name = "service.ins.project", url = "http://172.16.8.132:8060/api/insights/insProjectInfo", configuration = InsDataServiceClientConfig.class)
public interface InsLabelCorrectionServiceClient {

    @PostMapping("/queryLabelCorrectionList")
    Result<?> queryLabelCorrectionList(@RequestBody InsLabelCorrectionRecordQueryModel model);

}
