package com.voc.service.security.service;

import com.voc.service.security.api.ICustomInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Title: CustomInfoService
 * @Package: com.voc.service.security.service
 * @Description:
 * @Author: cuick
 * @Date: 2024/7/1 10:22
 * @Version:1.0
 */
@Service("defaultCustomInfoService")
@Slf4j
public class CustomInfoService implements ICustomInfoService {
    @Override
    public Object getUserInfo() {
        log.info("使用默认服务用户信息接口");
        return null;
    }

    @Override
    public Object getUserPermissions() {
        log.info("使用默认服务权限接口");
        return null;
    }
}
