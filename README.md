# 美云智数长安VOC 3.0系统

[![Java](https://img.shields.io/badge/Java-1.8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.5.15-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)
[![Elasticsearch](https://img.shields.io/badge/Elasticsearch-7.17.14-yellow.svg)](https://www.elastic.co/)

## 项目简介

美云智数长安VOC 3.0是一个专为汽车行业设计的用户声音（Voice of Customer）分析系统。该系统集成了数据采集、清洗、分析、可视化等功能，帮助企业深入了解用户反馈，提升产品质量和用户体验。

## 核心功能

### 🚗 汽车业务管理
- **品牌管理**: 汽车品牌、车系、车型的完整管理体系
- **产品管理**: 产品信息维护和分类管理
- **企业管理**: 多企业、多客户端数据隔离

### 📊 数据分析引擎
- **智能分析**: 基于NLP的文本情感分析
- **标准关键词**: 支持关键词库管理和自动标注
- **自助分析**: 用户自定义分析维度和指标
- **调研报告**: 自动生成分析报告和洞察

### 🔍 搜索与导出
- **全文搜索**: 基于Elasticsearch的高性能搜索
- **数据导出**: 支持Excel、PDF等多种格式导出
- **模板管理**: 可配置的导出模板系统

### 👥 用户权限管理
- **CAS单点登录**: 集成企业级认证系统
- **角色权限**: 细粒度的权限控制
- **多租户**: 支持多客户端数据隔离

### 📱 消息推送
- **微信推送**: 集成企业微信消息推送
- **订阅服务**: 支持个性化数据订阅
- **实时通知**: 重要数据变化实时提醒

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security + CAS
- **数据库**: MySQL 8.0+ (主库) + 动态数据源
- **ORM**: MyBatis Plus 3.5.3.2
- **缓存**: Redis 6.0+ (会话管理)
- **搜索**: Elasticsearch 7.17.14
- **连接池**: Druid
- **文档**: Knife4j (Swagger)

### 工具库
- **工具类**: Hutool 5.8.21
- **JSON**: FastJSON 2.0.7
- **Excel**: EasyExcel 3.0.5
- **模板引擎**: Velocity 2.3
- **文件存储**: 阿里云OSS
- **加密**: Jasypt

### 前端技术
- **模板引擎**: Thymeleaf
- **UI框架**: Bootstrap + jQuery
- **图表**: ECharts

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Elasticsearch 7.17.14

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd server-master
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE db_meicloud_data_changan_voc3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_meicloud_base_changan_voc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **配置环境**
```bash
# 复制配置文件
cp src/main/resources/application-local.properties.example src/main/resources/application-local.properties

# 修改数据库连接信息
vim src/main/resources/application-local.properties
```

4. **启动服务**
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/meicloud-changan-voc3.0.jar
```

5. **访问系统**
- 应用地址: http://localhost:8686
- API文档: http://localhost:8686/doc.html
- 数据库监控: http://localhost:8686/druid

## 配置说明

### 环境配置
项目支持多环境配置：
- `local`: 本地开发环境
- `sit`: 系统集成测试环境
- `uat`: 用户验收测试环境
- `prod`: 生产环境

### 关键配置项

#### 数据库配置
```properties
# 主数据源
spring.datasource.url=*********************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# CAS数据源
spring.datasource.dynamic.datasource.cas.url=*********************************************************
```

#### Redis配置
```properties
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=6
spring.redis.password=your_redis_password
```

#### CAS认证配置
```properties
cas.server.url.prefix=https://your-cas-server.com/cas
cas.logout.url=https://your-cas-server.com/cas/logout?service=
```

## 项目结构

```
src/main/java/com/meicloud/
├── AppStarter.java                 # 主启动类
└── voc/                           # 核心业务包
    ├── aspect/                    # AOP切面
    ├── car/                       # 汽车相关模块
    ├── cas/                       # CAS认证
    ├── category/                  # 分类管理
    ├── common/                    # 通用组件
    ├── company/                   # 公司管理
    ├── config/                    # 配置类
    ├── data/                      # 数据处理
    ├── es/                        # Elasticsearch
    ├── export/                    # 导出功能
    ├── group/                     # 分组管理
    ├── log/                       # 日志管理
    ├── manage/                    # 系统管理
    ├── menu/                      # 菜单管理
    ├── ops/                       # 运维相关
    ├── product/                   # 产品管理
    ├── research/                  # 调研分析
    ├── security/                  # 安全配置
    ├── selfAnalysis/              # 自助分析
    ├── user/                      # 用户管理
    ├── utils/                     # 工具类
    └── wechat/                    # 微信相关
```

## API文档

项目集成了Knife4j，启动后可访问：
- Swagger UI: http://localhost:8686/doc.html
- 默认用户名: `voc`
- 默认密码: `voc#24658`

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t voc3.0:latest .

# 运行容器
docker run -d \
  --name voc3.0 \
  -p 8686:8686 \
  -e SPRING_PROFILES_ACTIVE=prod \
  voc3.0:latest
```

### 生产环境配置
1. 修改 `application-prod.properties` 配置
2. 配置外部化配置文件
3. 设置JVM参数：
```bash
java -Xms2g -Xmx4g -jar meicloud-changan-voc3.0.jar --spring.profiles.active=prod
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 接口文档完整

### 数据库规范
- 表名使用下划线命名
- 字段注释完整
- 索引合理设计
- 支持软删除

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 监控与运维

### 健康检查
- 应用健康检查: `/actuator/health`
- 数据库连接池监控: `/druid`
- 系统指标: `/actuator/metrics`

### 日志管理
- 日志级别可动态调整
- 支持按日期滚动
- 错误日志自动告警

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查数据库配置和网络连接，确保数据库服务正常运行。

### Q: CAS认证失败？
A: 检查CAS服务器配置和网络连通性，确保证书有效。

### Q: Elasticsearch连接超时？
A: 检查ES服务状态和网络配置，调整连接超时参数。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- 项目维护者: 美云智数团队
- 邮箱: <EMAIL>
- 文档: [项目文档地址]

---

**注意**: 本项目为企业内部系统，请确保在授权范围内使用。
