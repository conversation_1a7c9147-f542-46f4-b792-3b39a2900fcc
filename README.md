# 美云智数长安VOC 3.0系统

[![Java](https://img.shields.io/badge/Java-1.8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.5.15-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)
[![Elasticsearch](https://img.shields.io/badge/Elasticsearch-7.17.14-yellow.svg)](https://www.elastic.co/)

## 项目简介

美云智数长安VOC 3.0是一个专为汽车行业设计的用户声音（Voice of Customer）分析系统。该系统集成了数据采集、清洗、分析、可视化等功能，帮助企业深入了解用户反馈，提升产品质量和用户体验。

## 核心功能

### 🚗 汽车业务管理
- **品牌管理**: 汽车品牌、车系、车型的完整管理体系
- **产品管理**: 产品信息维护和分类管理
- **企业管理**: 多企业、多客户端数据隔离

### 📊 数据分析引擎
- **智能分析**: 基于NLP的文本情感分析
- **标准关键词**: 支持关键词库管理和自动标注
- **自助分析**: 用户自定义分析维度和指标
- **调研报告**: 自动生成分析报告和洞察

### 🔍 搜索与导出
- **全文搜索**: 基于Elasticsearch的高性能搜索
- **数据导出**: 支持Excel、PDF等多种格式导出
- **模板管理**: 可配置的导出模板系统

### 👥 用户权限管理
- **CAS单点登录**: 集成企业级认证系统
- **角色权限**: 细粒度的权限控制
- **多租户**: 支持多客户端数据隔离

### 📱 消息推送
- **微信推送**: 集成企业微信消息推送
- **订阅服务**: 支持个性化数据订阅
- **实时通知**: 重要数据变化实时提醒

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security + CAS
- **数据库**: MySQL 8.0+ (主库) + 动态数据源
- **ORM**: MyBatis Plus 3.5.3.2
- **缓存**: Redis 6.0+ (会话管理)
- **搜索**: Elasticsearch 7.17.14
- **连接池**: Druid
- **文档**: Knife4j (Swagger)

### 工具库
- **工具类**: Hutool 5.8.21
- **JSON**: FastJSON 2.0.7
- **Excel**: EasyExcel 3.0.5
- **模板引擎**: Velocity 2.3
- **文件存储**: 阿里云OSS
- **加密**: Jasypt

### 前端技术
- **模板引擎**: Thymeleaf
- **UI框架**: Bootstrap + jQuery
- **图表**: ECharts

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Elasticsearch 7.17.14

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd server-master
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE db_meicloud_data_changan_voc3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_meicloud_base_changan_voc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **配置环境**
```bash
# 复制配置文件
cp src/main/resources/application-local.properties.example src/main/resources/application-local.properties

# 修改数据库连接信息
vim src/main/resources/application-local.properties
```

4. **启动服务**
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/meicloud-changan-voc3.0.jar
```

5. **访问系统**
- 应用地址: http://localhost:8686
- API文档: http://localhost:8686/doc.html
- 数据库监控: http://localhost:8686/druid

## 配置说明

### 环境配置
项目支持多环境配置：
- `local`: 本地开发环境
- `sit`: 系统集成测试环境
- `uat`: 用户验收测试环境
- `prod`: 生产环境

### 关键配置项

#### 数据库配置
```properties
# 主数据源
spring.datasource.url=*********************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# CAS数据源
spring.datasource.dynamic.datasource.cas.url=*********************************************************
```

#### Redis配置
```properties
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=6
spring.redis.password=your_redis_password
```

#### CAS认证配置
```properties
cas.server.url.prefix=https://your-cas-server.com/cas
cas.logout.url=https://your-cas-server.com/cas/logout?service=
```

## 项目结构

```
src/main/java/com/meicloud/
├── AppStarter.java                 # 主启动类
└── voc/                           # 核心业务包
    ├── aspect/                    # AOP切面
    ├── car/                       # 汽车相关模块
    ├── cas/                       # CAS认证
    ├── category/                  # 分类管理
    ├── common/                    # 通用组件
    ├── company/                   # 公司管理
    ├── config/                    # 配置类
    ├── data/                      # 数据处理
    ├── es/                        # Elasticsearch
    ├── export/                    # 导出功能
    ├── group/                     # 分组管理
    ├── log/                       # 日志管理
    ├── manage/                    # 系统管理
    ├── menu/                      # 菜单管理
    ├── ops/                       # 运维相关
    ├── product/                   # 产品管理
    ├── research/                  # 调研分析
    ├── security/                  # 安全配置
    ├── selfAnalysis/              # 自助分析
    ├── user/                      # 用户管理
    ├── utils/                     # 工具类
    └── wechat/                    # 微信相关
```

## 模块接口功能

### 🚗 汽车管理模块 (`/car`)
- **品牌管理**
  - `GET /car/brand` - 获取汽车品牌列表
  - `GET /car/brandGroup` - 获取品牌分组
  - `GET /car/series` - 获取车系信息
  - `GET /car/model` - 获取车型信息
  - `GET /car/refresh` - 刷新配置列表

- **地区管理**
  - `GET /car/provinceList` - 获取省份列表
  - `GET /car/cityList` - 获取城市列表
  - `GET /car/regionList` - 获取地区列表

- **数据源管理**
  - `GET /car/dataSource` - 获取数据源列表
  - `GET /car/charts` - 获取图表配置

### 🏢 企业管理模块 (`/company`)
- **企业信息**
  - `POST /company/list` - 获取租户列表
  - `POST /company/listAll` - 获取全部租户列表
  - `POST /company/save` - 保存租户信息
  - `GET /company/get/{companyId}` - 获取租户详情

- **菜单权限**
  - `GET /company/menu/get/{companyId}` - 获取租户菜单列表
  - `POST /company/menu/save/{companyId}` - 保存菜单列表
  - `GET /company/menu/get` - 获取当前登录租户菜单

- **用户管理**
  - `POST /company/user/get/{companyId}` - 获取租户用户列表
  - `POST /company/saveCategory/{companyId}` - 保存租户品类

### 📊 数据处理模块 (`/data`)
- **ES数据管理**
  - `GET /data/es/changeInsertVersion/{oldV}/{newV}` - 切换写数别名版本号
  - `POST /data/es/overrideDWD/before` - DWD层刷数前准备
  - `POST /data/es/overrideDM/before` - DM层刷数前准备
  - `POST /data/es/overrideDM/after` - DM层刷数后处理

- **数据同步**
  - `POST /data/syncTableData` - 同步表数据
  - `GET /data/refreshDict` - 刷新字典数据

### 📤 导出管理模块 (`/exportTask`)
- **任务管理**
  - `POST /exportTask/listTasks` - 获取任务列表
  - `GET /exportTask/deleteTask` - 删除导数任务
  - `GET /exportTask/download` - 下载导数文件
  - `GET /exportTask/doExportTask` - 执行导数任务

### 👥 分组管理模块 (`/group`)
- **分组操作**
  - `POST /group/list` - 获取分组列表
  - `POST /group/save` - 保存分组信息
  - `GET /group/get/{groupId}` - 获取分组详情
  - `POST /group/delete` - 删除分组

- **分组权限**
  - `POST /group/menu/save/{groupId}` - 保存分组菜单
  - `POST /group/category/save/{groupId}` - 保存分组品类
  - `POST /group/user/save/{groupId}` - 保存分组用户

### 📝 日志管理模块 (`/log`)
- **操作日志**
  - `POST /log/operation/list` - 获取操作日志列表
  - `POST /log/operation/export` - 导出操作日志

- **访问日志**
  - `POST /log/visit/list` - 获取访问日志列表
  - `POST /log/visit/export` - 导出访问日志

### ⚙️ 系统管理模块 (`/manage`)
- **数据源管理**
  - `POST /manage/dataSource/list` - 获取数据源列表
  - `POST /manage/dataSource/save` - 保存数据源
  - `POST /manage/dataSource/test` - 测试数据源连接

- **关键词管理**
  - `POST /manage/keyword/list` - 获取关键词列表
  - `POST /manage/keyword/save` - 保存关键词
  - `POST /manage/keyword/import` - 导入关键词
  - `POST /manage/keyword/export` - 导出关键词

- **标准关键词管理**
  - `POST /manage/stKeyword/list` - 获取标准关键词列表
  - `POST /manage/stKeyword/save` - 保存标准关键词
  - `POST /manage/stKeyword/upload` - 上传标准关键词文件
  - `POST /manage/stKeyword/download` - 下载标准关键词模板

- **指标体系管理**
  - `POST /manage/indexSystem/list` - 获取指标体系列表
  - `POST /manage/indexSystem/save` - 保存指标体系
  - `POST /manage/indexSystem/item/list` - 获取指标项列表

### 🧩 菜单管理模块 (`/menu`)
- **菜单操作**
  - `POST /menu/list` - 获取菜单列表
  - `POST /menu/save` - 保存菜单
  - `GET /menu/get/{menuId}` - 获取菜单详情
  - `POST /menu/delete` - 删除菜单

- **菜单权限**
  - `POST /menu/api/list` - 获取菜单API列表
  - `POST /menu/api/save` - 保存菜单API权限

### 🔧 运维管理模块 (`/ops`)
- **系统监控**
  - `GET /ops/health` - 系统健康检查
  - `GET /ops/metrics` - 系统指标监控
  - `POST /ops/cache/clear` - 清理缓存

- **任务调度**
  - `POST /ops/task/list` - 获取任务列表
  - `POST /ops/task/execute` - 执行任务
  - `POST /ops/task/stop` - 停止任务

### 🚀 产品分析模块 (`/product`)
- **概览仪表板**
  - `POST /product/overview/dashboard` - VOC概览仪表板数据
  - `POST /product/overview/trend` - 趋势分析数据

- **属性分析**
  - `POST /product/attribute/analysis` - 属性分析
  - `POST /product/attribute/compare` - 属性对比分析

- **问题定位**
  - `POST /product/problem/location` - 问题定位分析
  - `POST /product/problem/detail` - 问题详情分析

- **品牌对比**
  - `POST /product/brand/compare` - 自主品牌对比分析
  - `POST /product/brand/ranking` - 品牌排名分析

### 📊 调研分析模块 (`/research`)
- **调研报告**
  - `POST /research/report/list` - 获取调研报告列表
  - `POST /research/report/save` - 保存调研报告
  - `GET /research/report/get/{reportId}` - 获取调研报告详情
  - `POST /research/report/export` - 导出调研报告

- **问卷管理**
  - `POST /research/questionnaire/list` - 获取问卷列表
  - `POST /research/questionnaire/import` - 导入问卷数据
  - `POST /research/questionnaire/export` - 导出问卷数据

### 🔍 自助分析模块 (`/selfAnalysis`)
- **自定义分析**
  - `POST /selfAnalysis/create` - 创建自助分析
  - `POST /selfAnalysis/list` - 获取分析列表
  - `POST /selfAnalysis/execute` - 执行分析
  - `POST /selfAnalysis/export` - 导出分析结果

- **模板管理**
  - `POST /selfAnalysis/template/list` - 获取分析模板列表
  - `POST /selfAnalysis/template/save` - 保存分析模板
  - `POST /selfAnalysis/template/download` - 下载模板文件

### 👤 用户管理模块 (`/user`)
- **用户信息**
  - `POST /user/list` - 获取用户列表
  - `POST /user/save` - 保存用户信息
  - `GET /user/get/{userId}` - 获取用户详情
  - `POST /user/delete` - 删除用户

- **用户权限**
  - `POST /user/group/save/{userId}` - 保存用户分组
  - `POST /user/role/save/{userId}` - 保存用户角色
  - `POST /user/password/change` - 修改密码

### 💬 微信推送模块 (`/wechat`)
- **消息推送**
  - `POST /wechat/send` - 发送微信消息
  - `POST /wechat/subscribe/list` - 获取订阅列表
  - `POST /wechat/subscribe/save` - 保存订阅配置

- **推送结果**
  - `POST /wechat/result/list` - 获取推送结果列表
  - `POST /wechat/result/retry` - 重试推送

### 🏷️ 分类管理模块 (`/category`)
- **分类操作**
  - `POST /category/list` - 获取分类列表
  - `POST /category/save` - 保存分类信息
  - `GET /category/tree` - 获取分类树结构
  - `POST /category/sort` - 分类排序

### 🔍 Elasticsearch搜索模块 (`/es`)
- **索引管理**
  - `POST /es/index/create` - 创建索引
  - `POST /es/index/delete` - 删除索引
  - `GET /es/index/mapping` - 获取索引映射
  - `POST /es/index/reindex` - 重建索引

- **数据搜索**
  - `POST /es/search` - 全文搜索
  - `POST /es/search/aggregate` - 聚合搜索
  - `POST /es/search/suggest` - 搜索建议
  - `POST /es/search/scroll` - 滚动搜索

- **数据操作**
  - `POST /es/document/bulk` - 批量操作文档
  - `POST /es/document/update` - 更新文档
  - `GET /es/document/{id}` - 获取文档详情

### 🔐 安全认证模块 (`/security`)
- **用户认证**
  - `POST /security/login` - 用户登录
  - `POST /security/logout` - 用户登出
  - `POST /security/register` - 用户注册
  - `POST /security/password/reset` - 重置密码

- **Token管理**
  - `POST /token/generate` - 生成Token
  - `POST /token/validate` - 验证Token
  - `POST /token/refresh` - 刷新Token
  - `POST /token/revoke` - 撤销Token

- **CAS单点登录**
  - `GET /cas/login` - CAS登录入口
  - `GET /cas/logout` - CAS登出
  - `POST /cas/validate` - CAS票据验证

### 📈 统计分析模块 (`/statistics`)
- **数据统计**
  - `POST /statistics/overview` - 概览统计
  - `POST /statistics/trend` - 趋势统计
  - `POST /statistics/ranking` - 排名统计
  - `POST /statistics/distribution` - 分布统计

- **报表生成**
  - `POST /statistics/report/generate` - 生成报表
  - `GET /statistics/report/download/{id}` - 下载报表
  - `POST /statistics/report/schedule` - 定时报表

### 🔔 消息通知模块 (`/notification`)
- **消息管理**
  - `POST /notification/send` - 发送通知
  - `POST /notification/list` - 获取通知列表
  - `POST /notification/read` - 标记已读
  - `POST /notification/delete` - 删除通知

- **订阅管理**
  - `POST /notification/subscribe` - 订阅通知
  - `POST /notification/unsubscribe` - 取消订阅
  - `GET /notification/subscription/list` - 获取订阅列表

## API文档

项目集成了Knife4j，启动后可访问：
- Swagger UI: http://localhost:8686/doc.html
- 默认用户名: `voc`
- 默认密码: `voc#24658`

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t voc3.0:latest .

# 运行容器
docker run -d \
  --name voc3.0 \
  -p 8686:8686 \
  -e SPRING_PROFILES_ACTIVE=prod \
  voc3.0:latest
```

### 生产环境配置
1. 修改 `application-prod.properties` 配置
2. 配置外部化配置文件
3. 设置JVM参数：
```bash
java -Xms2g -Xmx4g -jar meicloud-changan-voc3.0.jar --spring.profiles.active=prod
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 接口文档完整

### 数据库规范
- 表名使用下划线命名
- 字段注释完整
- 索引合理设计
- 支持软删除

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 监控与运维

### 健康检查
- 应用健康检查: `/actuator/health`
- 数据库连接池监控: `/druid`
- 系统指标: `/actuator/metrics`

### 日志管理
- 日志级别可动态调整
- 支持按日期滚动
- 错误日志自动告警

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查数据库配置和网络连接，确保数据库服务正常运行。

### Q: CAS认证失败？
A: 检查CAS服务器配置和网络连通性，确保证书有效。

### Q: Elasticsearch连接超时？
A: 检查ES服务状态和网络配置，调整连接超时参数。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- 项目维护者: 美云智数团队
- 邮箱: <EMAIL>
- 文档: [项目文档地址]

---

**注意**: 本项目为企业内部系统，请确保在授权范围内使用。
