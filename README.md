# 美云智数长安VOC 3.0系统

[![Java](https://img.shields.io/badge/Java-1.8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.5.15-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-6.0+-red.svg)](https://redis.io/)
[![Elasticsearch](https://img.shields.io/badge/Elasticsearch-7.17.14-yellow.svg)](https://www.elastic.co/)

## 项目简介

美云智数长安VOC 3.0是一个专为汽车行业设计的用户声音（Voice of Customer）分析系统。该系统集成了数据采集、清洗、分析、可视化等功能，帮助企业深入了解用户反馈，提升产品质量和用户体验。

## 核心功能

### 🚗 汽车业务管理
- **品牌管理**: 汽车品牌、车系、车型的完整管理体系
- **产品管理**: 产品信息维护和分类管理
- **企业管理**: 多企业、多客户端数据隔离

### 📊 数据分析引擎
- **智能分析**: 基于NLP的文本情感分析
- **标准关键词**: 支持关键词库管理和自动标注
- **自助分析**: 用户自定义分析维度和指标
- **调研报告**: 自动生成分析报告和洞察

### 🔍 搜索与导出
- **全文搜索**: 基于Elasticsearch的高性能搜索
- **数据导出**: 支持Excel、PDF等多种格式导出
- **模板管理**: 可配置的导出模板系统

### 👥 用户权限管理
- **CAS单点登录**: 集成企业级认证系统
- **角色权限**: 细粒度的权限控制
- **多租户**: 支持多客户端数据隔离

### 📱 消息推送
- **微信推送**: 集成企业微信消息推送
- **订阅服务**: 支持个性化数据订阅
- **实时通知**: 重要数据变化实时提醒

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security + CAS
- **数据库**: MySQL 8.0+ (主库) + 动态数据源
- **ORM**: MyBatis Plus 3.5.3.2
- **缓存**: Redis 6.0+ (会话管理)
- **搜索**: Elasticsearch 7.17.14
- **连接池**: Druid
- **文档**: Knife4j (Swagger)

### 工具库
- **工具类**: Hutool 5.8.21
- **JSON**: FastJSON 2.0.7
- **Excel**: EasyExcel 3.0.5
- **模板引擎**: Velocity 2.3
- **文件存储**: 阿里云OSS
- **加密**: Jasypt

### 前端技术
- **模板引擎**: Thymeleaf
- **UI框架**: Bootstrap + jQuery
- **图表**: ECharts

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Elasticsearch 7.17.14

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd server-master
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE db_meicloud_data_changan_voc3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_meicloud_base_changan_voc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **配置环境**
```bash
# 复制配置文件
cp src/main/resources/application-local.properties.example src/main/resources/application-local.properties

# 修改数据库连接信息
vim src/main/resources/application-local.properties
```

4. **启动服务**
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/meicloud-changan-voc3.0.jar
```

5. **访问系统**
- 应用地址: http://localhost:8686
- API文档: http://localhost:8686/doc.html
- 数据库监控: http://localhost:8686/druid

## 配置说明

### 环境配置
项目支持多环境配置：
- `local`: 本地开发环境
- `sit`: 系统集成测试环境
- `uat`: 用户验收测试环境
- `prod`: 生产环境

### 关键配置项

#### 数据库配置
```properties
# 主数据源
spring.datasource.url=*********************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# CAS数据源
spring.datasource.dynamic.datasource.cas.url=*********************************************************
```

#### Redis配置
```properties
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=6
spring.redis.password=your_redis_password
```

#### CAS认证配置
```properties
cas.server.url.prefix=https://your-cas-server.com/cas
cas.logout.url=https://your-cas-server.com/cas/logout?service=
```

## 项目结构

```
src/main/java/com/meicloud/
├── AppStarter.java                 # 主启动类
└── voc/                           # 核心业务包
    ├── aspect/                    # AOP切面
    ├── car/                       # 汽车相关模块
    ├── cas/                       # CAS认证
    ├── category/                  # 分类管理
    ├── common/                    # 通用组件
    ├── company/                   # 公司管理
    ├── config/                    # 配置类
    ├── data/                      # 数据处理
    ├── es/                        # Elasticsearch
    ├── export/                    # 导出功能
    ├── group/                     # 分组管理
    ├── log/                       # 日志管理
    ├── manage/                    # 系统管理
    ├── menu/                      # 菜单管理
    ├── ops/                       # 运维相关
    ├── product/                   # 产品管理
    ├── research/                  # 调研分析
    ├── security/                  # 安全配置
    ├── selfAnalysis/              # 自助分析
    ├── user/                      # 用户管理
    ├── utils/                     # 工具类
    └── wechat/                    # 微信相关
```

## 模块接口功能

### 🚗 汽车管理模块 (`/car`)
- **品牌管理**
  - `GET /car/brand` - 获取汽车品牌列表
  - `GET /car/brandGroup` - 获取品牌分组
  - `GET /car/series` - 获取车系信息
  - `GET /car/model` - 获取车型信息
  - `GET /car/refresh` - 刷新配置列表

- **地区管理**
  - `GET /car/provinceList` - 获取省份列表
  - `GET /car/cityList` - 获取城市列表
  - `GET /car/regionList` - 获取地区列表

- **数据源管理**
  - `GET /car/dataSource` - 获取数据源列表
  - `GET /car/charts` - 获取图表配置

### 🏢 企业管理模块 (`/company`)
- **企业信息**
  - `POST /company/list` - 获取租户列表
  - `POST /company/listAll` - 获取全部租户列表
  - `POST /company/save` - 保存租户信息
  - `GET /company/get/{companyId}` - 获取租户详情

- **菜单权限**
  - `GET /company/menu/get/{companyId}` - 获取租户菜单列表
  - `POST /company/menu/save/{companyId}` - 保存菜单列表
  - `GET /company/menu/get` - 获取当前登录租户菜单

- **用户管理**
  - `POST /company/user/get/{companyId}` - 获取租户用户列表
  - `POST /company/saveCategory/{companyId}` - 保存租户品类

### 📊 数据处理模块 (`/data`)
- **ES数据管理**
  - `GET /data/es/changeInsertVersion/{oldV}/{newV}` - 切换写数别名版本号
  - `POST /data/es/overrideDWD/before` - DWD层刷数前准备
  - `POST /data/es/overrideDM/before` - DM层刷数前准备
  - `POST /data/es/overrideDM/after` - DM层刷数后处理

- **数据同步**
  - `POST /data/syncTableData` - 同步表数据
  - `GET /data/refreshDict` - 刷新字典数据

### 📤 导出管理模块 (`/exportTask`)
- **任务管理**
  - `POST /exportTask/listTasks` - 获取任务列表
  - `GET /exportTask/deleteTask` - 删除导数任务
  - `GET /exportTask/download` - 下载导数文件
  - `GET /exportTask/doExportTask` - 执行导数任务

### 👥 分组管理模块 (`/group`)
- **分组操作**
  - `POST /group/list` - 获取分组列表
  - `POST /group/save` - 保存分组信息
  - `GET /group/get/{groupId}` - 获取分组详情
  - `POST /group/delete` - 删除分组

- **分组权限**
  - `POST /group/menu/save/{groupId}` - 保存分组菜单
  - `POST /group/category/save/{groupId}` - 保存分组品类
  - `POST /group/user/save/{groupId}` - 保存分组用户

### 📝 日志管理模块 (`/log`)
- **操作日志**
  - `POST /log/operation/list` - 获取操作日志列表
  - `POST /log/operation/export` - 导出操作日志

- **访问日志**
  - `POST /log/visit/list` - 获取访问日志列表
  - `POST /log/visit/export` - 导出访问日志

### ⚙️ 系统管理模块 (`/manage`)
- **数据源管理** (`/dataSourceDetail`)
  - `POST /dataSourceDetail/list` - 获取数据源列表
  - `POST /dataSourceDetail/save` - 保存数据源
  - `POST /dataSourceDetail/test` - 测试数据源连接
  - `GET /dataSourceDetail/get/{id}` - 获取数据源详情

- **数据源评分管理** (`/dataSourceScore`)
  - `POST /dataSourceScore/list` - 获取数据源评分列表
  - `POST /dataSourceScore/save` - 保存数据源评分
  - `POST /dataSourceScore/import` - 导入评分数据
  - `GET /dataSourceScore/export` - 导出评分数据

- **关键词管理** (`/keyword`)
  - `POST /keyword/list` - 获取关键词列表
  - `POST /keyword/save` - 保存关键词
  - `POST /keyword/import` - 导入关键词
  - `POST /keyword/export` - 导出关键词
  - `POST /keyword/delete` - 删除关键词

- **标准关键词管理** (`/indexManage/stKeyword`)
  - `POST /indexManage/stKeyword/list` - 获取标准关键词列表
  - `POST /indexManage/stKeyword/save` - 保存标准关键词
  - `POST /indexManage/stKeyword/upload` - 上传标准关键词文件
  - `GET /indexManage/stKeyword/downloadTemplate` - 下载标准关键词模板
  - `GET /indexManage/stKeyword/getDepartmentList` - 获取责任部门列表
  - `GET /indexManage/stKeyword/getChargeList` - 获取责任人列表

- **指标体系管理** (`/index-system-keyword`)
  - `GET /index-system-keyword/find-by-standard-keyword-id` - 根据标准关键词ID查询关键词信息

- **问卷导入管理** (`/questionnaireImport`)
  - `POST /questionnaireImport/list` - 获取问卷导入列表
  - `POST /questionnaireImport/import` - 导入问卷数据
  - `GET /questionnaireImport/downloadTemplate` - 下载问卷模板
  - `POST /questionnaireImport/detail/list` - 获取问卷导入详情

- **文件导入管理** (`/fileImport`)
  - `POST /fileImport/list` - 获取文件导入列表
  - `POST /fileImport/upload` - 上传文件
  - `GET /fileImport/download/{id}` - 下载文件
  - `POST /fileImport/delete` - 删除导入文件

- **错误检查管理** (`/errorCheck`)
  - `POST /errorCheck/list` - 获取错误检查列表
  - `POST /errorCheck/check` - 执行错误检查
  - `POST /errorCheck/fix` - 修复错误

- **任务管理** (`/task`)
  - `POST /task/list` - 获取任务列表
  - `POST /task/create` - 创建任务
  - `POST /task/execute` - 执行任务
  - `POST /task/stop` - 停止任务
  - `GET /task/status/{id}` - 获取任务状态

- **初始化管理** (`/initManage`)
  - `POST /initManage/init` - 系统初始化
  - `POST /initManage/refresh` - 刷新配置
  - `GET /initManage/status` - 获取初始化状态

### 🧩 菜单管理模块 (`/menu`)
- **菜单操作**
  - `POST /menu/list` - 获取菜单列表
  - `POST /menu/save` - 保存菜单
  - `GET /menu/get/{menuId}` - 获取菜单详情
  - `POST /menu/delete` - 删除菜单

- **菜单权限**
  - `POST /menu/api/list` - 获取菜单API列表
  - `POST /menu/api/save` - 保存菜单API权限

### 🔧 运维管理模块 (`/ops`)
- **系统监控**
  - `GET /ops/health` - 系统健康检查
  - `GET /ops/metrics` - 系统指标监控
  - `POST /ops/cache/clear` - 清理缓存

- **任务调度**
  - `POST /ops/task/list` - 获取任务列表
  - `POST /ops/task/execute` - 执行任务
  - `POST /ops/task/stop` - 停止任务

### 🚀 产品分析模块 (`/product`)
- **概览仪表板**
  - `POST /product/overview/dashboard` - VOC概览仪表板数据
  - `POST /product/overview/trend` - 趋势分析数据

- **属性分析**
  - `POST /product/attribute/analysis` - 属性分析
  - `POST /product/attribute/compare` - 属性对比分析

- **问题定位**
  - `POST /product/problem/location` - 问题定位分析
  - `POST /product/problem/detail` - 问题详情分析

- **品牌对比**
  - `POST /product/brand/compare` - 自主品牌对比分析
  - `POST /product/brand/ranking` - 品牌排名分析

### 📊 调研分析模块 (`/research`)
- **调研报告**
  - `POST /research/report/list` - 获取调研报告列表
  - `POST /research/report/save` - 保存调研报告
  - `GET /research/report/get/{reportId}` - 获取调研报告详情
  - `POST /research/report/export` - 导出调研报告

- **问卷管理**
  - `POST /research/questionnaire/list` - 获取问卷列表
  - `POST /research/questionnaire/import` - 导入问卷数据
  - `POST /research/questionnaire/export` - 导出问卷数据

### 🔍 自助分析模块 (`/selfAnalysis`)
- **明细查询** (`/selfAnalysis/detail`)
  - `POST /selfAnalysis/detail/originalDetails` - 客户声音明细列表
  - `POST /selfAnalysis/detail/standardKeywordDetails` - 标准关键词明细列表
  - `POST /selfAnalysis/detail/keywordCorpusDetails` - 关键词语料明细列表
  - `GET /selfAnalysis/detail/getDepartmentList` - 获取责任部门下拉框
  - `GET /selfAnalysis/detail/getChargeList` - 获取责任人下拉框
  - `GET /selfAnalysis/detail/queryKwByCorpus` - 根据语料查询标准关键词
  - `POST /selfAnalysis/detail/stKeywordSearch` - 标准关键词搜索

- **自助导入分析** (`/selfAnalysis/selfExportAnalysis`)
  - `GET /selfAnalysis/selfExportAnalysis/downloadTemplate` - 下载模板
  - `GET /selfAnalysis/selfExportAnalysis/downloadResults` - 下载分析结果
  - `POST /selfAnalysis/selfExportAnalysis/upload` - 上传分析文件
  - `POST /selfAnalysis/selfExportAnalysis/analyze` - 执行分析

- **批量分析**
  - `POST /selfAnalysis/batch/create` - 创建批量分析任务
  - `POST /selfAnalysis/batch/list` - 获取批量分析列表
  - `GET /selfAnalysis/batch/status/{batchId}` - 获取批量分析状态
  - `POST /selfAnalysis/batch/cancel` - 取消批量分析

### 👤 用户管理模块 (`/user`)
- **用户信息**
  - `POST /user/list` - 获取用户列表
  - `POST /user/save` - 保存用户信息
  - `GET /user/get/{userId}` - 获取用户详情
  - `POST /user/delete` - 删除用户

- **用户权限**
  - `POST /user/group/save/{userId}` - 保存用户分组
  - `POST /user/role/save/{userId}` - 保存用户角色
  - `POST /user/password/change` - 修改密码

### 💬 微信推送模块 (`/wechat`)
- **消息推送**
  - `POST /wechat/send` - 发送微信消息
  - `POST /wechat/subscribe/list` - 获取订阅列表
  - `POST /wechat/subscribe/save` - 保存订阅配置

- **推送结果**
  - `POST /wechat/result/list` - 获取推送结果列表
  - `POST /wechat/result/retry` - 重试推送

### 🏷️ 分类管理模块 (`/category`)
- **分类操作**
  - `POST /category/list` - 获取分类列表
  - `POST /category/save` - 保存分类信息
  - `GET /category/tree` - 获取分类树结构
  - `POST /category/sort` - 分类排序

### 🔍 洞察报告模块 (`/insight`)
- **体验值分析**
  - `POST /insight/experience` - 获取体验值数据
  - `POST /insight/experienceRank` - 体验值排名
  - `POST /insight/experienceTree` - 体验值树结构
  - `POST /insight/experienceDetail` - 体验值明细

- **提及量分析**
  - `POST /insight/mention` - 获取提及量数据
  - `POST /insight/mentionRank` - 提及量排名
  - `POST /insight/mentionTrend` - 提及量趋势
  - `POST /insight/mentionDetail` - 提及量明细

- **关键词分析**
  - `POST /insight/keyword` - 关键词分析
  - `POST /insight/keywordRank` - 关键词排名
  - `POST /insight/keywordCloud` - 关键词云图

### 🏁 竞品分析模块 (`/rivalAnalysis`)
- **深度分析**
  - `POST /rivalAnalysis/analysis/voc` - VOC体验值分析
  - `POST /rivalAnalysis/analysis/dataSource` - 数据源分析-渠道构成
  - `POST /rivalAnalysis/analysis/dataSource/keyword` - 数据源分析-关键词
  - `POST /rivalAnalysis/analysis/index/rank` - 指标分析-指标排名
  - `POST /rivalAnalysis/analysis/content/list` - 原文明细

- **竞品对比**
  - `POST /rivalAnalysis/compare/brand` - 品牌对比分析
  - `POST /rivalAnalysis/compare/model` - 车型对比分析
  - `POST /rivalAnalysis/compare/experience` - 体验对比分析
  - `POST /rivalAnalysis/compare/trend` - 趋势对比分析

- **市场分析**
  - `POST /rivalAnalysis/market/share` - 市场份额分析
  - `POST /rivalAnalysis/market/position` - 市场定位分析
  - `POST /rivalAnalysis/market/opportunity` - 市场机会分析

### 📊 产品属性分析模块 (`/product`)
- **属性分析**
  - `POST /product/attributeAnalysis` - 属性分析
  - `POST /product/indexAnalysis` - 指标分析
  - `POST /product/attributeRank` - 属性排名
  - `POST /product/attributeTrend` - 属性趋势

- **问题定位**
  - `POST /product/problemLocation` - 问题定位分析
  - `POST /product/problemDetail` - 问题详情分析
  - `POST /product/problemTrend` - 问题趋势分析

- **自主品牌对比**
  - `POST /product/selfBrandCompare` - 自主品牌对比
  - `POST /product/brandRanking` - 品牌排名分析

### 📋 信息订阅模块 (`/subscribe`)
- **订阅管理**
  - `POST /subscribe/addSubscribe` - 增加订阅
  - `POST /subscribe/updSubscribe` - 修改订阅
  - `POST /subscribe/copySubscribe` - 复制订阅
  - `POST /subscribe/delSubscribe` - 删除订阅

- **订阅查询**
  - `POST /subscribe/getSubscribeList` - 获取订阅列表
  - `POST /subscribe/getSubscribeDetail` - 获取订阅详情
  - `POST /subscribe/getSubscribeResult` - 获取订阅结果

### 🖥️ 系统监控模块 (`/monitor`)
- **状态监控**
  - `GET /monitor/webStatus` - 获取Web状态
  - `GET /monitor/esStatus` - 获取ES状态
  - `GET /monitor/redisStatus` - 获取Redis状态
  - `GET /monitor/mysqlStatus` - 获取MySQL状态
  - `GET /monitor/nlpJavaStatus` - 获取NLP状态

### 📝 调研报告模块 (`/research-report`)
- **报告管理**
  - `POST /research-report/search` - 搜索报告列表
  - `GET /research-report/{dataId}` - 获取报告详情
  - `POST /research-report/create` - 创建报告
  - `POST /research-report/update` - 更新报告

- **文件上传**
  - `POST /research-report/upload` - 上传研究报告
  - `POST /research-report/uploadMulti` - 批量上传报告
  - `GET /research-report/download/{id}` - 下载报告

### 🔍 Elasticsearch搜索模块 (`/es`)
- **索引管理**
  - `POST /es/index/create` - 创建索引
  - `POST /es/index/delete` - 删除索引
  - `GET /es/index/mapping` - 获取索引映射
  - `POST /es/index/reindex` - 重建索引

- **数据搜索**
  - `POST /es/search` - 全文搜索
  - `POST /es/search/aggregate` - 聚合搜索
  - `POST /es/search/suggest` - 搜索建议
  - `POST /es/search/scroll` - 滚动搜索

- **数据操作**
  - `POST /es/document/bulk` - 批量操作文档
  - `POST /es/document/update` - 更新文档
  - `GET /es/document/{id}` - 获取文档详情

### 🔐 安全认证模块 (`/security`)
- **用户认证**
  - `POST /security/login` - 用户登录
  - `POST /security/logout` - 用户登出
  - `POST /security/register` - 用户注册
  - `POST /security/password/reset` - 重置密码

- **Token管理**
  - `POST /token/generate` - 生成Token
  - `POST /token/validate` - 验证Token
  - `POST /token/refresh` - 刷新Token
  - `POST /token/revoke` - 撤销Token

- **CAS单点登录**
  - `GET /cas/login` - CAS登录入口
  - `GET /cas/logout` - CAS登出
  - `POST /cas/validate` - CAS票据验证

### 📈 统计分析模块 (`/statistics`)
- **数据统计**
  - `POST /statistics/overview` - 概览统计
  - `POST /statistics/trend` - 趋势统计
  - `POST /statistics/ranking` - 排名统计
  - `POST /statistics/distribution` - 分布统计

- **报表生成**
  - `POST /statistics/report/generate` - 生成报表
  - `GET /statistics/report/download/{id}` - 下载报表
  - `POST /statistics/report/schedule` - 定时报表

### ⚠️ 风险预警模块 (`/risk`)
- **风险数据分析**
  - `POST /getRiskResultList` - 获取风险列表
  - `POST /risk/emotion` - 情感风险分析
  - `POST /risk/quality` - 质量风险分析
  - `POST /risk/user` - 用户风险分析

- **风险预警任务**
  - `POST /risk/warning/execute` - 执行风险预警
  - `POST /risk/warning/schedule` - 定时风险预警
  - `GET /risk/warning/status` - 获取预警状态

### 🤖 模型服务模块 (`/model`)
- **模型调用**
  - `POST /model/getMissDataList` - 获取未打标数据集合
  - `POST /model/process1` - 数据分析处理
  - `POST /model/predict` - 模型预测
  - `POST /model/train` - 模型训练

- **模型管理**
  - `POST /model/list` - 获取模型列表
  - `POST /model/deploy` - 部署模型
  - `POST /model/version` - 模型版本管理

### 🔄 数据分析核心模块 (`/analysis`)
- **数据处理**
  - `POST /process` - 数据分析入口（样板间）
  - `POST /analysis/batch` - 批量数据处理
  - `POST /analysis/validate` - 数据验证

- **数据列表**
  - `POST /getMetaDataList` - 获取元数据列表
  - `POST /getProcessedDataList` - 获取处理后数据列表
  - `POST /getAnalysisResultList` - 获取分析结果列表

### 📊 报告中心模块 (`/reportCenter`)
- **报告管理**
  - `POST /reportCenter/list` - 获取报告列表
  - `POST /reportCenter/create` - 创建报告
  - `POST /reportCenter/update` - 更新报告
  - `GET /reportCenter/get/{id}` - 获取报告详情

- **报告数据**
  - `POST /reportCenter/data` - 获取报告数据
  - `POST /reportCenter/export` - 导出报告
  - `GET /reportCenter/conditions` - 获取查询条件

### 🏠 首页模块 (`/homePage`)
- **数据简报**
  - `POST /homePage/getDataPresentation` - 获取数据简报
  - `POST /homePage/getOverview` - 获取概览数据
  - `POST /homePage/getTrend` - 获取趋势数据

### 📈 体验指数检测模块 (`/experienceIndex`)
- **指数分析**
  - `POST /experienceIndex/getIndexedPerformance` - 获取体验指数
  - `POST /experienceIndex/getThreshold` - 获取阈值配置
  - `POST /experienceIndex/findGranularityDetail` - 获取指标配置

- **检测管理**
  - `POST /experienceIndex/detection` - 执行指数检测
  - `POST /experienceIndex/alert` - 指数预警
  - `GET /experienceIndex/conditions` - 获取检测条件

### 🔔 消息通知模块 (`/notification`)
- **消息管理**
  - `POST /notification/send` - 发送通知
  - `POST /notification/list` - 获取通知列表
  - `POST /notification/read` - 标记已读
  - `POST /notification/delete` - 删除通知

- **订阅管理**
  - `POST /notification/subscribe` - 订阅通知
  - `POST /notification/unsubscribe` - 取消订阅
  - `GET /notification/subscription/list` - 获取订阅列表

## API文档

项目集成了Knife4j，启动后可访问：
- Swagger UI: http://localhost:8686/doc.html
- 默认用户名: `voc`
- 默认密码: `voc#24658`

### API认证说明

所有API接口都需要在请求头中携带认证信息：

```http
Authorization: Bearer [your-token]
Content-Type: application/json
```

### 接口使用示例

#### 1. 获取汽车品牌列表
```bash
curl -X GET "http://localhost:8686/car/brand" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"
```

#### 2. 洞察报告-体验值分析
```bash
curl -X POST "http://localhost:8686/insight/experience" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "brandCodes": ["CA"],
    "indexType": "QY",
    "dateType": "month"
  }'
```

#### 3. 竞品分析-VOC体验值
```bash
curl -X POST "http://localhost:8686/rivalAnalysis/analysis/voc" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "brandCodes": ["CA", "BMW", "AUDI"],
    "indexType": "QY",
    "rivalBrandCodes": ["BMW", "AUDI"]
  }'
```

#### 4. 自助分析-客户声音明细
```bash
curl -X POST "http://localhost:8686/selfAnalysis/detail/originalDetails" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "keyword": "发动机故障",
    "emotion": "负面",
    "pageSize": 20,
    "pageNum": 1
  }'
```

#### 5. 标准关键词管理-上传文件
```bash
curl -X POST "http://localhost:8686/indexManage/stKeyword/upload" \
  -H "Authorization: Bearer your-token" \
  -F "file=@standard_keywords.xlsx" \
  -F "departmentId=1" \
  -F "department=质量部"
```

#### 6. 信息订阅-增加订阅
```bash
curl -X POST "http://localhost:8686/subscribe/addSubscribe" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "subscribeName": "质量问题监控",
    "subscribeType": "keyword",
    "keywords": ["发动机", "变速箱"],
    "brandCodes": ["CA"],
    "frequency": "daily",
    "recipients": ["<EMAIL>"]
  }'
```

#### 7. 风险预警-获取风险列表
```bash
curl -X POST "http://localhost:8686/getRiskResultList" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "client001",
    "riskType": "quality",
    "level": "high",
    "pageSize": 20,
    "pageNum": 1
  }'
```

### 响应格式

所有API接口统一返回格式：

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t voc3.0:latest .

# 运行容器
docker run -d \
  --name voc3.0 \
  -p 8686:8686 \
  -e SPRING_PROFILES_ACTIVE=prod \
  voc3.0:latest
```

### 生产环境配置
1. 修改 `application-prod.properties` 配置
2. 配置外部化配置文件
3. 设置JVM参数：
```bash
java -Xms2g -Xmx4g -jar meicloud-changan-voc3.0.jar --spring.profiles.active=prod
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 接口文档完整

### 数据库规范
- 表名使用下划线命名
- 字段注释完整
- 索引合理设计
- 支持软删除

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 监控与运维

### 健康检查
- 应用健康检查: `/actuator/health`
- 数据库连接池监控: `/druid`
- 系统指标: `/actuator/metrics`

### 日志管理
- 日志级别可动态调整
- 支持按日期滚动
- 错误日志自动告警

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查数据库配置和网络连接，确保数据库服务正常运行。

### Q: CAS认证失败？
A: 检查CAS服务器配置和网络连通性，确保证书有效。

### Q: Elasticsearch连接超时？
A: 检查ES服务状态和网络配置，调整连接超时参数。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- 项目维护者: 美云智数团队
- 邮箱: <EMAIL>
- 文档: [项目文档地址]

---

**注意**: 本项目为企业内部系统，请确保在授权范围内使用。
