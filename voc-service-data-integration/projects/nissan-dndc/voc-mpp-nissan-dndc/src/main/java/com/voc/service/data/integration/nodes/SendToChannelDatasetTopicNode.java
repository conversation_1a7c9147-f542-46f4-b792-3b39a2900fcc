package com.voc.service.data.integration.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.data.integration.api.ChannelExecutionResultService;
import com.voc.service.data.integration.api.IMppInputDataService;
import com.voc.service.data.integration.api.model.ChannelMetaDataModel;
import com.voc.service.data.integration.api.model.DataIntegrationRecordModel;
import com.voc.service.data.integration.nodes.context.ChannelDatasetContext;
import com.voc.service.data.integration.producers.kafka.ChannelDatasetProducer;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: SendToChannelDatasetTopicNode
 * @Package: com.voc.service.data.integration.nodes
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/24 17:14
 * @Version:1.0
 */
@LiteflowComponent(id = "sendToChannelDatasetTopicNode", name = "发送各渠道数据到消息队列节点")
@Slf4j
public class SendToChannelDatasetTopicNode extends NodeComponent {
    @Autowired
    ChannelDatasetProducer channelDatasetProducer;
    @Autowired
    ChannelExecutionResultService dataIntegrationRecordService;
    @Autowired
    IMppInputDataService inputDataService;

    @Override
    public void process() throws Exception {
        ChannelDatasetContext context = this.getRequestData();
        final List<String> result = this.getCurrLoopObj();
        log.info("getCurrLoopObj： {}", result);
        if (CollUtil.isEmpty(result)) {
            log.warn("本次数据为空");
            //是否结束整个流程
            super.setIsEnd(true);
            return;
        }
        //推送队列
        log.info("推送 {} 条数据", result.size());
        List<DataIntegrationRecordModel> list = this.loadData(context.getChannelType(), context.getWorkId(), result);

        channelDatasetProducer.pushChannelData(MessageDTO.builder().source(context.getChannelType()).data(list).build());
        log.info("完成topic推送推送 {}条", list.size());
    }

    private List<DataIntegrationRecordModel> loadData(String channelType, String workId, List<String> ids) {
        List<ChannelMetaDataModel> list = inputDataService.loadData(new HashSet<>(ids));

        if (CollUtil.isNotEmpty(list)) {
            log.info("【{}】本次加载数据量：{}", channelType, list.size());
            return list.stream().map(item -> {
                try {
                    if (ObjUtil.isNotEmpty(item.getExtAttrs())) {
                        String extAttrs = StrUtil.removeAll(String.valueOf(item.getExtAttrs()), "\n");
                        item.setExtAttrs(JSONUtil.parseObj(extAttrs));
                    }
                    if (ObjUtil.isNotEmpty(item.getExtAttrs2())) {
                        String extAttrs = StrUtil.removeAll(String.valueOf(item.getExtAttrs2()), "\n");
                        item.setExtAttrs2(JSONUtil.parseObj(extAttrs));
                    }
                    if (ObjUtil.isNotEmpty(item.getExtAttrs3())) {
                        String extAttrs = StrUtil.removeAll(String.valueOf(item.getExtAttrs3()), "\n");
                        item.setExtAttrs3(JSONUtil.parseObj(extAttrs));
                    }
                }catch (Exception e){
                    log.error("数据解析异常：id:{}",item.getId());
                    log.error(e.getMessage(),e);
                }
                return DataIntegrationRecordModel.builder()
                        .id(item.getId())
                        .data(item)
                        .dataId(item.getDataId())
                        .channelType(item.getChannelBiz())
                        .createTime(item.getCreateTime())
                        .workId(workId)
                        .tid(ServiceContextHolder.traceId())
                        .build();
            }).collect(Collectors.toList());
        } else {
            log.error("加载数据异常：channelType：{} , ids:{}", channelType, ids);
        }
        return Collections.EMPTY_LIST;
    }

    @Override
    public boolean isAccess() {
        ChannelDatasetContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getChannelType()), "getChannelType cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getChannelId cannot be empty");

        return true;
    }
}
