version: '3'
services:

  dataease:
    image: registry.cn-qingdao.aliyuncs.com/dataease/dataease:DE_TAG
    stop_signal: SIGTERM
    stop_grace_period: 15s
    container_name: dataease
    ports:
      - ${DE_PORT}:8100
    volumes:
      - ${DE_BASE}/dataease2.0/conf:/opt/apps/config
      - ${DE_BASE}/dataease2.0/logs:/opt/dataease2.0/logs
      - ${DE_BASE}/dataease2.0/data/static-resource:/opt/dataease2.0/data/static-resource
      - ${DE_BASE}/dataease2.0/cache:/opt/dataease2.0/cache
      - ${DE_BASE}/dataease2.0/data/geo:/opt/dataease2.0/data/geo
      - ${DE_BASE}/dataease2.0/data/appearance:/opt/dataease2.0/data/appearance
      - ${DE_BASE}/dataease2.0/data/exportData:/opt/dataease2.0/data/exportData
      - ${DE_BASE}/dataease2.0/data/plugin:/opt/dataease2.0/data/plugin
      - ${DE_BASE}/dataease2.0/data/font:/opt/dataease2.0/data/font
      - ${DE_BASE}/dataease2.0/data/i18n:/opt/dataease2.0/data/i18n
      - ${DE_BASE}/dataease2.0/logs/dataease/sync-task:/opt/dataease2.0/logs/sync-task
    depends_on:
      DE_MYSQL_HOST:
        condition: service_healthy
    networks:
      - dataease-network

networks:
  dataease-network:
