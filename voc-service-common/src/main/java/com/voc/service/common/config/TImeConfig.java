package com.voc.service.common.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.time.ZoneId;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TImeConfig
 * @createTime 2024年03月05日 17:49
 * @Copyright futong
 */
@Configuration
@Slf4j
public class TImeConfig {

    @PostConstruct
    public void init(){
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneId.of("Asia/Shanghai")));
        log.info("--->> {} {}", this.getClass().getSimpleName(),java.util.TimeZone.getDefault().getID());
    }
}
