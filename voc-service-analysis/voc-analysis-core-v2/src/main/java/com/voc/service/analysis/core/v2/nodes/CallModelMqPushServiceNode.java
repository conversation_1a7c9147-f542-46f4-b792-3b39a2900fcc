package com.voc.service.analysis.core.v2.nodes;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.voc.service.analysis.api.IAysBatchPushRecordV2Service;
import com.voc.service.analysis.api.IAysModelResltService;
import com.voc.service.analysis.core.v2.nodes.abstracts.AbstractNode;
import com.voc.service.analysis.core.v2.nodes.context.AnlysisDefaultContext;
import com.voc.service.analysis.core.v2.producers.kafka.ModelProducer;
import com.voc.service.analysis.dto.MessageDTO;
import com.voc.service.analysis.model.AiRequestDataModel;
import com.voc.service.analysis.model.AysProcessDataModel;
import com.voc.service.insights.engine.enums.RuleContentType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName callModelMqPushServiceNode
 * @createTime 2024年07月23日 10:49
 * @Copyright liuhb
 * @Description 调用模型后，保存原始数据，成功后标记前置处理数据为已完成状态
 */
@LiteflowComponent(id = "callModelMqPushServiceNode", name = "调用模型计算服务节点")
@Slf4j
public class CallModelMqPushServiceNode extends AbstractNode {

    @Autowired
    IAysModelResltService resltDataService;
    @Autowired
    ModelProducer modelProducer;
    @Autowired
    IAysBatchPushRecordV2Service batchPushRecordV2Service;

    @Override
    public void process() throws Exception {
        log.info("{}", this.getClass().getSimpleName());
        AnlysisDefaultContext context = this.getRequestData();
        String clientId = context.getClientId();
        final List<AysProcessDataModel> parsedData = context.getProcessData();
        List<String> idList = new ArrayList<>();
        List<AiRequestDataModel> aiRequestDateModelList = new ArrayList<>();
        try {
            aiRequestDateModelList = convertToParams(parsedData);
            if(log.isDebugEnabled()) {
                log.debug("发送MQ消息参数:{}", JSON.toJSONString(aiRequestDateModelList));
            }
            log.info("发送MQ消息参数:{}", aiRequestDateModelList.size());
            for (AiRequestDataModel aiRequestDateModel : aiRequestDateModelList) {
                aiRequestDateModel.setWorkId(context.getWorkId());
                modelProducer.pushData(MessageDTO.builder().source(clientId).data(aiRequestDateModel).source(clientId).build());
                idList.add(aiRequestDateModel.getId());
            }
        } catch (Exception e) {
            Thread.sleep(1000);
            List<AiRequestDataModel> aiRequestDateModels = aiRequestDateModelList.stream().filter(f -> !idList.contains(f.getId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(aiRequestDateModels)) {
                int maxRetries = 3; // 最大重试次数
                int currentRetry = 0; // 当前重试次数
                boolean conditionMet = false; // 条件是否满足
                while (!conditionMet && currentRetry < maxRetries) {
                    try {
                        List<String> list = new ArrayList<>();
                        for (AiRequestDataModel aiRequestDateModel : aiRequestDateModels) {
                            modelProducer.pushData(MessageDTO.builder().source(clientId).data(aiRequestDateModel).source(clientId).build());
                            list.add(aiRequestDateModel.getId());
                        }
                        aiRequestDateModels = aiRequestDateModelList.stream().filter(f -> !list.contains(f.getId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(aiRequestDateModels)) {
                            conditionMet = Boolean.TRUE;
                        }
                    } catch (Exception ex) {
                        currentRetry++;
                    }
                }
                if (CollectionUtil.isNotEmpty(aiRequestDateModels)) {
                    final Set<String> ids = aiRequestDateModels.stream().map(AiRequestDataModel::getId).collect(Collectors.toSet());
                    log.info("发送MQ消息异常异常数据 ids:{}", ids);
                    batchPushRecordV2Service.modifyStatus(context.getClientId(), ids, "-1", "H");
                }
            } else {
                log.info("失败前都已发送成功");
            }
        }
        //保存原始数据
        resltDataService.saveBatch(context.getClientId(), parsedData);
        //调用模型计算服务后保存的原始数据
        context.setProcessData(parsedData);
    }

    @Override
    public boolean isAccess() {
        AnlysisDefaultContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getWorkId()), "workId cannot be empty");
        return true;
    }

    /**
     * 组装请求模型入参
     *
     * @param parsedData
     * @return
     */
    public List<AiRequestDataModel> convertToParams(List<AysProcessDataModel> parsedData) {
        List<AiRequestDataModel> aiRequestDateModelList = new ArrayList<>();
        for (AysProcessDataModel aysProcessDataModel : parsedData) {
            if (ObjectUtil.isNull(aysProcessDataModel.getData())) {
                continue;
            }
            AiRequestDataModel aiRequestDateModel = new AiRequestDataModel();
            String dataStr = String.valueOf(aysProcessDataModel.getData());
            JSONObject jsonObject = JSON.parseObject(dataStr);
            aiRequestDateModel.setContent(jsonObject.getString("content"));
            aiRequestDateModel.setId(aysProcessDataModel.getNewId());
            aiRequestDateModel.setSource(RuleContentType.getByCode(aysProcessDataModel.getContentType()).getText());
            aiRequestDateModel.setClientId(aysProcessDataModel.getClientId());
            aiRequestDateModelList.add(aiRequestDateModel);
            aiRequestDateModel.setModelType(aysProcessDataModel.getModelType() + "");
            aiRequestDateModel.setChannelId(aysProcessDataModel.getChannelId());
        }
        return aiRequestDateModelList;
    }
}
