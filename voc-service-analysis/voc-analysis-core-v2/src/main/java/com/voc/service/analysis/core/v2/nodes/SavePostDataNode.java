package com.voc.service.analysis.core.v2.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.analysis.api.IAysPostprocessDataService;
import com.voc.service.analysis.core.v2.nodes.abstracts.AbstractNode;
import com.voc.service.analysis.core.v2.nodes.context.AnlysisDefaultContext;
import com.voc.service.analysis.model.AysProcessDataModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryException;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StoreSourceDataNode
 * @createTime 2024年03月07日 10:49
 * @Copyright cuick
 */
@LiteflowComponent(id = "savePostDataNode", name = "保存后置处理数据")
@Slf4j
public class SavePostDataNode extends AbstractNode {
    @Autowired
    IAysPostprocessDataService processDataService;

    @Override
    public void process() throws Exception {
        AnlysisDefaultContext context = this.getRequestData();
        Set<String> insertIds = null;
        try {
            final String workId = context.getWorkId();
            log.info("保存发送模型数据 workId:{}", workId);

            final Set<String> ids = context.getProcessData().stream().map(AysProcessDataModel::getNewId).collect(Collectors.toSet());
            if(CollUtil.isNotEmpty(ids)) {
                this.sendPrivateDeliveryData("modifyModelResltDataAnalysisStatusNode", ids);
            }

            //保存数据
            final List<AysProcessDataModel> aysPreprocessData = context.getProcessData();
            insertIds =  processDataService.saveBatch(context.getClientId(), aysPreprocessData);

            log.info("保存发送模型数据完成");
        } catch (Exception e) {
            log.error("保存发送模型数据失败", e);
            long count = processDataService.remove(context.getClientId(), insertIds);
            log.info("insertIds:{} deleteIds{}", insertIds.size(), count);
            throw new RetryException(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AnlysisDefaultContext context = this.getRequestData();

        Assert.isTrue(CollUtil.isNotEmpty(context.getProcessData()), "processData cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getWorkId()), "getChannelId cannot be empty");

        return true;
    }

}
