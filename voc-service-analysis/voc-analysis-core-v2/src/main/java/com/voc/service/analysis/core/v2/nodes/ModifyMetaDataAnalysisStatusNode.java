package com.voc.service.analysis.core.v2.nodes;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.voc.service.analysis.api.IAysBatchPushRecordV2Service;
import com.voc.service.analysis.api.IAysMetaDataAnalysisService;
import com.voc.service.analysis.api.IAysPreprocessDataService;
import com.voc.service.analysis.core.v2.nodes.abstracts.AbstractNode;
import com.voc.service.analysis.core.v2.nodes.context.AnlysisDefaultContext;
import com.voc.service.analysis.model.AysProcessDataModel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Title: IsExistPreDataNode
 * @Package: com.voc.service.analysis.core.v2.nodes.abstracts
 * @Description:
 * @Author: cuick
 * @Date: 2024/4/19 10:34
 * @Version:1.0
 */
@LiteflowComponent(id = "modifyMetaDataAnalysisStatusNode", name = "修改前置处理数据状态节点")
@Slf4j
public class ModifyMetaDataAnalysisStatusNode extends AbstractNode {
    @Autowired
    IAysPreprocessDataService preprocessDataService;
    @Autowired
    IAysMetaDataAnalysisService metaDataAnalysisService;
    @Autowired
    IAysBatchPushRecordV2Service batchPushRecordV2Service;

    @Override
    public void process() throws Exception {
        try {
            final Set<String> ids = this.getPrivateDeliveryData();
            Assert.isTrue(CollUtil.isNotEmpty(ids), "processData ids cannot be empty");
            AnlysisDefaultContext context = this.getRequestData();

            //保存数据
            metaDataAnalysisService.modifyToDone(context.getClientId(), ids);

            //过滤出已被过滤的数据id集合
            final Set<String> ids_ = context.getProcessData().stream()
                    .filter(model -> "1".equalsIgnoreCase(model.getAbandon()))
                    .map(AysProcessDataModel::getNewId)
                    .collect(Collectors.toSet());
            final Collection<String> intersectionList = CollUtil.intersection(ids_, ids);

            if (CollUtil.isNotEmpty(ids_)) {
                metaDataAnalysisService.modifyToDataStatus(context.getClientId(), ids_,"1");
            }
            //模型不接收已被过滤的数据
            List<AysProcessDataModel> dataModels = context.getProcessData().stream().filter(model -> "0".equals(model.getAbandon())).toList();
            context.setProcessData(dataModels);
            batchPushRecordV2Service.modifyStatus(context.getClientId(), new HashSet<>(intersectionList), "1", "A");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public boolean isAccess() {
        AnlysisDefaultContext context = this.getRequestData();
        Assert.isTrue(StrUtil.isNotBlank(context.getWorkId()), "getWorkId cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(context.getClientId()), "getClientId cannot be empty");

        return true;
    }
}
