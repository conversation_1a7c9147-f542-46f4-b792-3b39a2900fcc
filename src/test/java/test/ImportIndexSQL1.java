package test;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportIndexSQL1 {

    /**
     * 生成指标体系表SQL
     *
     * @param srcFile
     * @param targetFile
     */
    public static void dim_voc3_index_system(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }
                        int indexId = 0;
                        Map<String, Integer> indexIdMap = new HashMap<>();
                        List<String> lines = new ArrayList<>();
                        lines.add("INSERT INTO dim_voc3_index_system(w_pdate_dt, w_insert_dt, job_name, batch_dt, " +
                                "first_index_id, first_index_name, second_index_id, second_index_name, " +
                                "third_index_id, third_index_name, four_index_id, four_index_name, index_type, status, index_id)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String index_type = StringUtils.trim(row.size() > 13 ? row.get(13) : "");
                            String first_index_id = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String first_index_name = StringUtils.trim(row.size() > 6 ? row.get(6) : "");
                            String second_index_id = StringUtils.trim(row.size() > 7 ? row.get(7) : "");
                            String second_index_name = StringUtils.trim(row.size() > 8 ? row.get(8) : "");
                            String third_index_id = StringUtils.trim(row.size() > 9 ? row.get(9) : "");
                            String third_index_name = StringUtils.trim(row.size() > 10 ? row.get(10) : "");
                            String four_index_id = StringUtils.trim(row.size() > 11 ? row.get(11) : "");
                            String four_index_name = StringUtils.trim(row.size() > 12 ? row.get(12) : "");
                            if (!indexIdMap.containsKey(index_type)) {
                                indexIdMap.put(index_type, indexId + 1);
                                indexId++;
                            }
                            String str = "(now(), now(), NULL, now(), '"
                                    + first_index_id + "', '" + first_index_name + "', '"
                                    + second_index_id + "', '" + second_index_name + "', '"
                                    + third_index_id + "', '" + third_index_name + "', '"
                                    + four_index_id + "', '" + four_index_name + "', '"
                                    + index_type + "', '1', " + indexIdMap.get(index_type) + ")";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    /**
     * 生成标志关键词SQL
     *
     * @param srcFile
     * @param targetFile
     */
    public static void dim_voc3_index_system_standard_keyword(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }
                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_voc3_index_system_standard_keyword(" +
                                "w_pdate_dt, w_insert_dt, job_name, batch_dt, index_type, four_index_id, four_index_name, " +
                                "standard_keyword, status, standard_keyword_id, emotion_attribute, clarity, department, department_id, charge, charge_id, field)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String index_type = StringUtils.trim(row.size() > 17 ? row.get(17) : "");
                            String four_index_id = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String four_index_name = StringUtils.trim(row.size() > 6 ? row.get(6) : "");
                            String standard_keyword = StringUtils.trim(row.size() > 7 ? row.get(7) : "");
                            String id = StringUtils.trim(row.size() > 9 ? row.get(9) : "");
                            String emotion_attribute = StringUtils.trim(row.size() > 10 ? row.get(10) : "");
                            String clarity = StringUtils.trim(row.size() > 11 ? row.get(11) : "");
                            String field = StringUtils.trim(row.size() > 16 ? row.get(16) : "");
                            String str = "(now(), now(), NULL, now(), '" + index_type + "', '"
                                    + four_index_id + "', '"
                                    + four_index_name + "', '"
                                    + standard_keyword + "', '1', '" + id + "', '"
                                    + emotion_attribute + "', '"
                                    + clarity
                                    + "', null, null, null, null, '" + field + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public static void dim_voc3_m_standard_keyword(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }
                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_voc3_m_standard_keyword(" +
                                "data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt," +
                                "standard_keyword, status, emotion_attribute, clarity, department, department_id, charge, charge_id, field)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String standard_keyword = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String id = StringUtils.trim(row.size() > 7 ? row.get(7) : "");
                            String emotion_attribute = StringUtils.trim(row.size() > 8 ? row.get(8) : "");
                            String clarity = StringUtils.trim(row.size() > 9 ? row.get(9) : "");
                            String field = StringUtils.trim(row.size() > 14 ? row.get(14) : "");
                            String str = "('"+id+"',now(), now(), NULL, now(), '"
                                    + standard_keyword + "', '1', '"
                                    + emotion_attribute + "', '"
                                    + clarity
                                    + "', null, null, null, null, '" + field + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }


    /**
     * 生成语料SQL
     *
     * @param srcFile
     * @param targetFile
     */
    public static void dim_voc3_m_keyword(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_voc3_m_keyword(w_pdate_dt, w_insert_dt, job_name, batch_dt, standard_keyword_id, keyword, status, sentence)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String standard_keyword_id = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String keyword = StringUtils.trim(row.size() > 6 ? row.get(6) : "");
                            String sentence = StringUtils.trim(row.size() > 8 ? row.get(8) : "");
                            String str = "(now(), now(), NULL, now(), " + (StringUtils.isNotBlank(standard_keyword_id) ? standard_keyword_id : "NULL") + ", '" + keyword + "', '1', '" + sentence + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }


    public static void delete_dim_voc3_m_keyword_by_keyword(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("UPDATE INTO dim_voc3_m_keyword SET status  = '-1' ");
                        lines.add("WHERE keyword in (");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String keyword = StringUtils.trim(row.size() > 6 ? row.get(6) : "");
                            lines.add("'" + keyword + "',");
                        }
                        lines.add(");");
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public static void delete_dim_voc3_m_keyword_by_id(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("UPDATE INTO dim_voc3_m_keyword SET status  = '-1' ");
                        lines.add("WHERE data_id in (");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String dataId = StringUtils.trim(row.size() > 0 ? row.get(0) : "");
                            lines.add("'" + dataId + "',");
                        }
                        lines.add(");");
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }


    /**
     * 生成语料SQL
     *
     * @param srcFile
     * @param targetFile
     */
    public static void dim_voc3_index_system_keyword(File srcFile, File targetFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_voc3_index_system_keyword(w_pdate_dt, w_insert_dt, job_name, batch_dt, standard_keyword_id, keyword, status, sentence)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String standard_keyword_id = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String keyword = StringUtils.trim(row.size() > 6 ? row.get(6) : "");
                            String sentence = StringUtils.trim(row.size() > 8 ? row.get(8) : "");
                            String str = "(now(), now(), NULL, now(), " + (StringUtils.isNotBlank(standard_keyword_id) ? standard_keyword_id : "NULL") + ", '" + keyword + "', '1', '" + sentence + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public static void main(String[] args) {
        //dim_voc3_index_system(new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system.xlsx"), new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system.txt"));
        //dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system_standard_keyword.xlsx"), new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system_standard_keyword.txt"));
        //dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system_keyword.xlsx"), new File("D:\\doc\\VOC\\20220628入库\\dim_voc3_index_system_keyword.txt"));
        //dim_voc3_m_standard_keyword(new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_standard_keyword - 线下新增V2(2).xlsx"), new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_standard_keyword.txt"));
        dim_voc3_m_keyword(new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_keyword - 白名单0815.xlsx"), new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_keyword-white-0815.txt"));
        //delete_dim_voc3_m_keyword_by_keyword(new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_keyword - 4词删除.xlsx"), new File("D:\\MyData\\zhouzx11\\Downloads\\dim_voc3_index_system_keyword-delete-4word.txt"));
        //delete_dim_voc3_m_keyword_by_id(new File("D:\\MyData\\zhouzx11\\Downloads\\算法提取-语料错误需修正的语料（第一批）0812.xlsx"),
        //                                new File("D:\\MyData\\zhouzx11\\Downloads\\voc3MKeywordDel20220815.txt"));
    }
}
