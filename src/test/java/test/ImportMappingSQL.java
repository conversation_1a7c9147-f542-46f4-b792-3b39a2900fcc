package test;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ImportMappingSQL {

    public static void main(String[] args) {
        File file = new File("D:\\MyData\\wujw82\\Downloads\\待增加的mapping和车系信息-2022年6月28日(1).xlsx");
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(file));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }
                        int index = 1447;
                        for (List<String> row : rows) {
                            System.out.println("INSERT INTO changan_voc3_web.dim_voc3_series_mapping(id, original_series, new_series, new_brand) VALUES('" + index + "', '" + StringUtils.trim(row.size() > 0 ? row.get(0) : "") + "', '" + StringUtils.trim(row.size() > 1 ? row.get(1) : "") + "', '" + StringUtils.trim(row.size() > 2 ? row.get(2) : "") + "');");
                            index++;
                        }
                    }
                }
            }
        } catch (FileNotFoundException e) {
        } catch (IOException e) {
        }
    }


//    public static void main(String[] args) {
//        File file = new File("D:\\MyData\\wujw82\\Downloads\\待增加的mapping和车系信息-2022年6月28日.xlsx");
//        XSSFWorkbook workbook = null;
//        try {
//            workbook = new XSSFWorkbook(new FileInputStream(file));
//            // 判断Sheet的总数是否大于0
//            if (workbook.getNumberOfSheets() > 0) {
//                XSSFSheet sheet = workbook.getSheetAt(1);
//                if (sheet != null) {
//                    List<List<String>> rows = new ArrayList<List<String>>();
//                    // 读取Row
//                    int rowNum = sheet.getPhysicalNumberOfRows();
//                    if (rowNum > 0) {
//                        for (int j = 1; j < rowNum; j++) {
//                            XSSFRow xssfRow = sheet.getRow(j);
//                            if (xssfRow != null) {
//                                List<String> row = new ArrayList<String>();
//                                rows.add(row);
//                                if (xssfRow.getLastCellNum() > 0) {
//                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
//                                        XSSFCell cell = xssfRow.getCell(k);
//                                        if (cell != null) {
//                                            cell.setCellType(CellType.STRING);
//                                        }
//                                        row.add(cell != null ? cell.getStringCellValue() : "");
//                                    }
//                                }
//
//                            }
//                        }
//                        int index = 1;
//                        for (List<String> row : rows) {
//                            System.out.println("INSERT INTO changan_voc3_web.dim_voc3_model_mapping(id, original_series, original_model, new_model) VALUES('" + index + "', '" + StringUtils.trim(row.size() > 0 ? row.get(0) : "") + "', '" + StringUtils.trim(row.size() > 1 ? row.get(1) : "") + "', '" + StringUtils.trim(row.size() > 2 ? row.get(2) : "") + "');");
//                            index++;
//                        }
//                    }
//                }
//            }
//        } catch (FileNotFoundException e) {
//        } catch (IOException e) {
//        }
//    }


//    public static void main(String[] args) {
//        File file = new File("D:\\MyData\\wujw82\\Downloads\\mapping+品牌车系车型4月28日-整理版本2.0.xlsx");
//        XSSFWorkbook workbook = null;
//        try {
//            workbook = new XSSFWorkbook(new FileInputStream(file));
//            // 判断Sheet的总数是否大于0
//            if (workbook.getNumberOfSheets() > 0) {
//                for (int i = 0; i < 1; i++) {
//                    XSSFSheet sheet = workbook.getSheetAt(i);
//                    if (sheet != null) {
//                        List<List<String>> rows = new ArrayList<List<String>>();
//                        // 读取Row
//                        int rowNum = sheet.getPhysicalNumberOfRows();
//                        if (rowNum > 0) {
//                            for (int j = 1; j < rowNum; j++) {
//                                XSSFRow xssfRow = sheet.getRow(j);
//                                if (xssfRow != null) {
//                                    List<String> row = new ArrayList<String>();
//                                    rows.add(row);
//                                    if (xssfRow.getLastCellNum() > 0) {
//                                        for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
//                                            XSSFCell cell = xssfRow.getCell(k);
//                                            if (cell != null) {
//                                                cell.setCellType(CellType.STRING);
//                                            }
//                                            row.add(cell != null ? cell.getStringCellValue() : "");
//                                        }
//                                    }
//
//                                }
//                            }
//                        }
//                        int index = 1;
//                        for (List<String> row : rows) {
//                            System.out.println("INSERT INTO changan_voc3_web.dim_voc3_data_source(data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, data_source, is_outer, weight, status) VALUES('" + (i + "_" + index) + "', now(), now(), '手动创建', now(), '" + StringUtils.trim(row.size() > 0 ? row.get(0) : "") + "', '" + StringUtils.trim(row.size() > 1 ? row.get(1) : "") + "', 1.0, '1');");
//                            index++;
//                        }
//                    }
//                }
//            }
//        } catch (FileNotFoundException e) {
//            log.error(e.getMessage(), e)
//        } catch (IOException e) {
//            log.error(e.getMessage(), e)
//        }
//    }
}
