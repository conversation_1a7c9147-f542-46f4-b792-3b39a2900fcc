package test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.meicloud.voc.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.RandomAccessFile;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Hashtable;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicLong;

public class TimeoutAnalysis {

    public static void main(String[] args) {
        try {
            Date startDate = DateUtil.parse("2022-06-14 01:00:00", "yyyy-MM-dd HH:mm:ss");
            Date endDate = DateUtil.parse("2022-06-14 23:40:00", "yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MMM-d HH:mm:ss", Locale.ENGLISH);
            File file = new File("D:\\MyData\\wujw82\\Desktop\\dat1\\haproxy2.log");
            AtomicLong total = new AtomicLong();
            AtomicLong timeOut = new AtomicLong();
            Hashtable<String, AtomicLong> statusTable = new Hashtable<>();
            FileUtil.readLines(new RandomAccessFile(file, "rw"), Charset.forName("utf-8"), line -> {
                if (StringUtils.isBlank(line)) {
                    return;
                }
                String[] array = line.split(" ");
                if (array.length > 19 && StringUtils.equals(array[19], "/changan/voc/analyzer")) {
                    Date date = null;
                    try {
                        date = sdf.parse("2022-Jun-" + array[1] + " " + array[2]);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    if (date.getTime() >= startDate.getTime() && date.getTime() <= endDate.getTime()) {
                        //只计算分析接口
                        //总数加1
                        total.getAndIncrement();
                        //状态码
                        String status = array[10];
                        if (!statusTable.containsKey(status)) {
                            statusTable.put(status, new AtomicLong());
                        }
                        statusTable.get(status).getAndIncrement();
                        //接口耗时
                        int time = Integer.parseInt(array[9].split("/")[4]);
                        if (time >= 60000) {
                            timeOut.getAndIncrement();
                        }
                    }

                }
            });
            System.out.println("数据总量：" + total + "，超时（超60秒）：" + timeOut + "，状态信息：" + JsonUtil.toJsonStr(statusTable));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
