package test;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportIndexSQL {
    //  check index
//    select s.index_type, s.four_index_name, count(distinct s.four_index_id) as c from dim_voc3_index_system s
//    group by s.index_type, s.four_index_name ORDER BY c desc
    // check standkeyword
//    select s.four_index_id, s.standard_keyword, count(distinct s.standard_keyword_id) as c from dim_voc3_index_system_standard_keyword s
//    group by s.four_index_id, s.standard_keyword ORDER BY c desc

    //    select s.keyword, count(distinct s.standard_keyword_id) as c from dim_voc3_index_system_keyword s
//    group by s.keyword ORDER BY c desc
    public static void main(String[] args) {
        dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\指标体系0617\\VRT.xlsx"));
        dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\指标体系0617\\VRT.xlsx"));
        dim_voc3_index_system(new File("D:\\doc\\VOC\\指标体系0617\\VRT.xlsx"), "2");

        dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\指标体系0617\\全领域业务.xlsx"));
        dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\指标体系0617\\全领域业务.xlsx"));
        dim_voc3_index_system(new File("D:\\doc\\VOC\\指标体系0617\\全领域业务.xlsx"), "0");

        dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\指标体系0617\\全旅程客户.xlsx"));
        dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\指标体系0617\\全旅程客户.xlsx"));
        dim_voc3_index_system(new File("D:\\doc\\VOC\\指标体系0617\\全旅程客户.xlsx"), "1");

        dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\指标体系0617\\商品化属性.xlsx"));
        dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\指标体系0617\\商品化属性.xlsx"));
        dim_voc3_index_system(new File("D:\\doc\\VOC\\指标体系0617\\商品化属性.xlsx"), "3");

        dim_voc3_index_system_keyword(new File("D:\\doc\\VOC\\指标体系0617\\销售线索.xlsx"));
        dim_voc3_index_system_standard_keyword(new File("D:\\doc\\VOC\\指标体系0617\\销售线索.xlsx"));
        dim_voc3_index_system(new File("D:\\doc\\VOC\\指标体系0617\\销售线索.xlsx"), "4");
    }

    public static Map<String, String> dim_voc3_index_system_map = new HashMap<>();

    public static void dim_voc3_index_system(File srcFile, String index_id) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                File targetFile = new File("D:\\MyData\\wujw82\\Desktop\\indexdata\\" + sheet.getSheetName() + "-指标体系表.txt");
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("INSERT INTO changan_voc3_web.dim_voc3_index_system(w_pdate_dt, w_insert_dt, job_name, batch_dt, " +
                                "first_index_id, first_index_name, second_index_id, second_index_name, " +
                                "third_index_id, third_index_name, four_index_id, four_index_name, index_type, status, index_id)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String index_type = StringUtils.trim(row.size() > 0 ? row.get(0) : "");
                            String first_index_id = StringUtils.trim(row.size() > 14 ? row.get(14) : "");
                            String first_index_name = StringUtils.trim(row.size() > 13 ? row.get(13) : "");
                            String second_index_id = StringUtils.trim(row.size() > 16 ? row.get(16) : "");
                            String second_index_name = StringUtils.trim(row.size() > 15 ? row.get(15) : "");
                            String third_index_id = StringUtils.trim(row.size() > 18 ? row.get(18) : "");
                            String third_index_name = StringUtils.trim(row.size() > 17 ? row.get(17) : "");
                            String four_index_id = StringUtils.trim(row.size() > 20 ? row.get(20) : "");
                            String four_index_name = StringUtils.trim(row.size() > 19 ? row.get(19) : "");
                            String key = index_type + "_" + first_index_id + "_" + second_index_id + "_" + third_index_id + "_" + four_index_id;
                            if (dim_voc3_index_system_map.containsKey(key) || StringUtils.isBlank(four_index_id)) {
                                continue;
                            }
                            dim_voc3_index_system_map.put(key, key);
                            String str = "(now(), now(), NULL, now(), '"
                                    + first_index_id + "', '" + first_index_name + "', '"
                                    + second_index_id + "', '" + second_index_name + "', '"
                                    + third_index_id + "', '" + third_index_name + "', '"
                                    + four_index_id + "', '" + four_index_name + "', '"
                                    + index_type + "', '1', " + index_id + "),";
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (FileNotFoundException e) {
        } catch (IOException e) {
        }
    }

    public static Map<String, String> dim_voc3_index_system_standard_keyword_map = new HashMap<>();

    public static void dim_voc3_index_system_standard_keyword(File srcFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                File targetFile = new File("D:\\MyData\\wujw82\\Desktop\\indexdata\\" + sheet.getSheetName() + "-标准关键词表.txt");
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO changan_voc3_web.dim_voc3_index_system_standard_keyword(" +
                                "w_pdate_dt, w_insert_dt, job_name, batch_dt, index_type, four_index_id, four_index_name, " +
                                "standard_keyword, status, standard_keyword_id, emotion_attribute, clarity, department, department_id, charge, charge_id, field)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String index_type = StringUtils.trim(row.size() > 0 ? row.get(0) : "");
                            String first_index_id = StringUtils.trim(row.size() > 14 ? row.get(14) : "");
                            String second_index_id = StringUtils.trim(row.size() > 16 ? row.get(16) : "");
                            String third_index_id = StringUtils.trim(row.size() > 18 ? row.get(18) : "");
                            String four_index_id = StringUtils.trim(row.size() > 20 ? row.get(20) : "");
                            String id = StringUtils.trim(row.size() > 3 ? row.get(3) : "");
                            if (StringUtils.isBlank(four_index_id)) {
                                continue;
                            }
                            String key = index_type + "_" + first_index_id + "_" + second_index_id + "_" + third_index_id + "_" + four_index_id + "" + id;
                            if (dim_voc3_index_system_standard_keyword_map.containsKey(key)) {
                                continue;
                            }
                            dim_voc3_index_system_standard_keyword_map.put(key, key);
                            String str = "(now(), now(), NULL, now(), '" + index_type + "', '"
                                    + four_index_id + "', '"
                                    + StringUtils.trim(row.size() > 19 ? row.get(19) : "") + "', '"
                                    + StringUtils.trim(row.size() > 4 ? row.get(4) : "") + "', '1', '" + id + "', '"
                                    + StringUtils.trim(row.size() > 7 ? row.get(7) : "") + "', '"
                                    + ("模糊".equals(StringUtils.trim(row.size() > 9 ? row.get(9) : "")) ? "模糊" : "清晰")
                                    + "', null, null, null, null, '" + StringUtils.trim(row.size() > 6 ? row.get(6) : "") + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (FileNotFoundException e) {
        } catch (IOException e) {
        }
    }

    public static Map<String, String> dim_voc3_index_system_keyword_map = new HashMap<>();

    public static void dim_voc3_index_system_keyword(File srcFile) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                File targetFile = new File("D:\\MyData\\wujw82\\Desktop\\indexdata\\" + sheet.getSheetName() + "-关键词表.txt");
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO changan_voc3_web.dim_voc3_index_system_keyword(w_pdate_dt, w_insert_dt, job_name, batch_dt, standard_keyword_id, keyword, status, sentence)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String standard_keyword_id = StringUtils.trim(row.size() > 3 ? row.get(3) : "");
                            String keyword = StringUtils.trim(row.size() > 1 ? row.get(1) : "");
                            String sentence = StringUtils.trim(row.size() > 5 ? row.get(5) : "");
                            String key = standard_keyword_id + keyword;
                            if (dim_voc3_index_system_keyword_map.containsKey(key)) {
                                continue;
                            }
                            dim_voc3_index_system_keyword_map.put(key, key);
                            String str = "(now(), now(), NULL, now(), " + (StringUtils.isNotBlank(standard_keyword_id) ? standard_keyword_id : "NULL") + ", '" + keyword + "', '1', '" + sentence + "')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (FileNotFoundException e) {
        } catch (IOException e) {
        }
    }


}
