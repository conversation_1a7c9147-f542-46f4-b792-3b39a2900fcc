package test;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

public class ImportCarSQL {
    public static void addCarMapping(File srcFile, File targetFile, int startId) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_series_mapping(id, original_series, new_series, new_brand)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String originalName  = StringUtils.trim(row.size() > 1 ? row.get(1) : "");
                            String seriesName = StringUtils.trim(row.size() > 2 ? row.get(2) : "");
                            String brandName = StringUtils.trim(row.size() > 0 ? row.get(0) : "");
                            String str = "('"+(startId)+"', '"+originalName+"', '"+seriesName+"', '"+brandName+"')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            lines.add(str);
                            startId++;
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public static void addCarSeries(File srcFile, File targetFile, int startId) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new FileInputStream(srcFile));
            // 判断Sheet的总数是否大于0
            if (workbook.getNumberOfSheets() > 0) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    List<List<String>> rows = new ArrayList<List<String>>();
                    // 读取Row
                    int rowNum = sheet.getPhysicalNumberOfRows();
                    if (rowNum > 0) {
                        for (int j = 1; j < rowNum; j++) {
                            XSSFRow xssfRow = sheet.getRow(j);
                            if (xssfRow != null) {
                                List<String> row = new ArrayList<String>();
                                rows.add(row);
                                if (xssfRow.getLastCellNum() > 0) {
                                    for (int k = 0; k < xssfRow.getLastCellNum(); k++) {
                                        XSSFCell cell = xssfRow.getCell(k);
                                        if (cell != null) {
                                            cell.setCellType(CellType.STRING);
                                        }
                                        row.add(cell != null ? cell.getStringCellValue() : "");
                                    }
                                }

                            }
                        }

                        List<String> lines = new ArrayList<>();
                        lines.add("SET NAMES utf8mb4;");
                        lines.add("INSERT INTO dim_voc3_car_series(data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt," +
                                " series_code, series_name, brand_code, brand_name, market_id, market_name)");
                        lines.add("VALUES");
                        for (int i = 0; i < rows.size(); i++) {
                            List<String> row = rows.get(i);
                            String seriesName = StringUtils.trim(row.size() > 1 ? row.get(1) : "");
                            String brandName = StringUtils.trim(row.size() > 0 ? row.get(0) : "");
                            String marketName = StringUtils.trim(row.size() > 2 ? row.get(2) : "");
                            String str = "('"+(startId)+"', now(), now(), '手动插入', now(), '"
                                    +seriesName+"', '"+seriesName+"', '"+brandName+"', '"+brandName+"', '"+marketName+"', '"+marketName+"')";
                            if (i < rows.size() - 1) {
                                str += ",";
                            } else {
                                str += ";";
                            }
                            startId++;
                            lines.add(str);
                        }
                        FileUtil.writeLines(lines, targetFile, "utf-8");
                    }
                }
            }
        } catch (Exception e) {
        }
    }

    public static void main(String[] args) {
        //addCarMapping(new File("D:\\MyData\\zhouzx11\\Downloads\\VOC需新增的车系（5-7月）.xlsx"), new File("D:\\MyData\\zhouzx11\\Downloads\\car_mapping.sql"), 800);
        addCarSeries(new File("D:\\MyData\\zhouzx11\\Downloads\\VOC需新增的车系（5-7月）-规整.xlsx"), new File("D:\\MyData\\zhouzx11\\Downloads\\car_series.sql"), 8000);
    }
}
