<!doctype html>
<html lang="">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width,initial-scale=1">
<meta http-equiv="Pragma" content="no-cache" />
<meta name="Expires" content="0" />
<link rel="icon" href="favicon.ico" />
<title>DDM数据驱动管理-启明星</title>
</head>
<script src="./jquery.min.js" type="text/javascript" ></script>
<style>
	table,th{align-content: center; }
	table,th,td{border:1px solid black; width:500px;}
</style>
<body>
	<div align="center">
		<h2>监控页面</h2>
		<h5>数据监控</h5>
		<table id="dataSource">
			<tr align="center">
				<td >数据源</td>
				<td>昨日数据量</td>
			</tr>
		</table>
	</div>
	<div align="center">
		<h5>服务状态监控</h5>
		<table id="server">
			<tr align="center">
				<td>服务</td>
				<td>状态</td>
			</tr>
		</table>
	</div>
	<div align="center">
		<h5>NLP接口监控</h5>
		<table id="nlp">
			<tr align="center">
				<td>数据源</td>
				<td>状态</td>
			</tr>
		</table>
	</div>
	<div align="center">
		<a href="javascript:;" onclick="refresh();" class="login-btn">刷新状态</a>
	</div>
	<script type="text/javascript">
	  function refresh() {
		  initTable();
		  getWebStatus();
		  getEsStatus();
		  getRedisStatus();
		  getMysqlStatus();
		  getNLPStatus();
		  getNLPJavaStatus();
	  }
	  
	  function initTable() {
		  $("#dataSource").html("<tr align='center'><td >数据源</td><td>昨日数据量</td></tr>");
		  $("#server").html("<tr align='center'><td >服务</td><td>状态</td></tr>");
		  $("#nlp").html("<tr align='center'><td >数据源</td><td>状态</td></tr>");
	  }
	
	  function getWebStatus() {
        $.ajax({
          url: '/monitor/webStatus',
          type: 'GET',
          dataType: 'json',
          async: true,
          success: function (res) {
            let data = res.data;
            //alert(JSON.stringify(res));
            //alert(data);
            for (var a in data) {
	              if ("GREEN" == data[a]) {
	  				$("#server").append("<tr align='center'><td>web:"+a+"</td><td><font color='green'>正常</font></td></tr>");
	              }else{
	  				$("#server").append("<tr align='center'><td>web:"+a+"</td><td><font color='red'>异常</font></td></tr>");
	              }
            }
          },
          error: function (XMLHttpRequest, textStatus, errorThrown) {
				$("#server").append("<tr align='center'><td>WEB</td><td><font color='yellow'>未知</font></td></tr>");
          }
        });
      }
	  
      function getEsStatus() {
        $.ajax({
          url: '/monitor/esStatus',
          type: 'GET',
          dataType: 'json',
          async: true,
          success: function (res) {
            let data = res.data;
            //alert(JSON.stringify(res));
            //alert(data);
            if ("GREEN" == data) {
				$("#server").append("<tr align='center'><td>ES</td><td><font color='green'>正常</font></td></tr>");
            }else{
				$("#server").append("<tr align='center'><td>ES</td><td><font color='red'>异常</font></td></tr>");
            }
          },
          error: function (XMLHttpRequest, textStatus, errorThrown) {
				$("#server").append("<tr align='center'><td>ES</td><td><font color='yellow'>未知</font></td></tr>");
          }
        });
      }
      function getRedisStatus() {
        $.ajax({
          url: '/monitor/redisStatus',
          type: 'GET',
          dataType: 'json',
          async: true,
          success: function (res) {
            let data = res.data;
            if ("GREEN" == data) {
				$("#server").append("<tr align='center'><td>Redis</td><td><font color='green'>正常</font></td></tr>");
            }else{
				$("#server").append("<tr align='center'><td>Redis</td><td><font color='red'>异常</font></td></tr>");
            }
          },
          error: function (XMLHttpRequest, textStatus, errorThrown) {
				$("#dataSource").append("<tr align='center'><td>Redis</td><td><font color='yellow'>未知</font></td></tr>");
          }
        });
      }
      function getMysqlStatus() {
          $.ajax({
            url: '/monitor/mysqlStatus',
            type: 'GET',
            dataType: 'json',
            async: true,
            success: function (res) {
              let data = res.data;
              if ("GREEN" == data) {
  				$("#server").append("<tr align='center'><td>Mysql</td><td><font color='green'>正常</font></td></tr>");
              }else{
  				$("#server").append("<tr align='center'><td>Mysql</td><td><font color='red'>异常</font></td></tr>");
              }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
  				$("#server").append("<tr align='center'><td>Mysql</td><td><font color='yellow'>未知</font></td></tr>");
            }
          });
      }
      function getNginxStatus() {
          $.ajax({
            url: 'https://www.baidu.com',
            type: 'GET',
            dataType: 'html',
            async: true,
            success: function (res) {
           	  //alert(JSON.stringify(res));
              let data = res.data;
              if ("GREEN" == data) {
  				$("#server").append("<tr align='center'><td>Nginx</td><td><font color='green'>正常</font></td></tr>");
              }else{
  				$("#server").append("<tr align='center'><td>Nginx</td><td><font color='red'>异常</font></td></tr>");
              }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
            	alert(JSON.stringify(XMLHttpRequest));
            	alert(textStatus);
  				$("#server").append("<tr align='center'><td>Nginx</td><td><font color='yellow'>未知</font></td></tr>");
            }
          });
      }
      function getNLPJavaStatus() {
          $.ajax({
            url: '/monitor/nlpJavaStatus',
            type: 'GET',
            dataType: 'json',
            async: true,
            success: function (res) {
           	  //alert(JSON.stringify(res));
              let data = res.data;
              for (var a in data) {
	              if ("GREEN" == data[a]) {
	  				$("#server").append("<tr align='center'><td>"+a+"</td><td><font color='green'>正常</font></td></tr>");
	              }else{
	  				$("#server").append("<tr align='center'><td>"+a+"</td><td><font color='red'>异常</font></td></tr>");
	              }
              }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
  				$("#server").append("<tr align='center'><td>Nginx</td><td><font color='yellow'>未知</font></td></tr>");
            }
          });
    	  //sendRequest("GET","http://www.baidu.com/");
      }
      function getNLPStatus() {
          $.ajax({
            url: '/monitor/nlpStatus',
            type: 'GET',
            dataType: 'json',
            async: true,
            success: function (res) {
           	  //alert(JSON.stringify(res));
              let data = res.data;
              for (var a in data) {
	              if ("GREEN" == data[a]) {
	  				$("#nlp").append("<tr align='center'><td>"+a+"</td><td><font color='green'>正常</font></td></tr>");
	              }else{
	  				$("#nlp").append("<tr align='center'><td>"+a+"</td><td><font color='red'>异常</font></td></tr>");
	              }
              }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
  				$("#nlp").append("<tr align='center'><td>Nginx</td><td><font color='yellow'>未知</font></td></tr>");
            }
          });
    	  //sendRequest("GET","http://www.baidu.com/");
      }
      refresh();
      
      function sendRequest(requestType, url){
		var xhr = new XMLHttpRequest();
		if ("withCredentials" in xhr) {
		     xhr.withCredentials = true;
		}
		xhr.open(requestType, url, true); //true表示异步请求，如果是false则是同步请求，
		
		xhr.onreadystatechange = function () {
		     if (xhr.readyState == 4) {
		         if (xhr.status == 200) {
		             var response = xhr.responseText;
		             alert(response);
		         }
		     }
		};
		xhr.send("data");
      }
      
    </script>
</body>
</html>