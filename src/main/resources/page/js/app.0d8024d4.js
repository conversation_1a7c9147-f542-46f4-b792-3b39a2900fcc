(function(){var e={5446:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.data&&e.data.data.length?a("div",{staticClass:"public-chart-div",attrs:{id:e.divId}}):a("no-data")],1)},r=[],i={data(){return{xActiveName:""}},props:{data:{type:Object,default(){return{data:[],xDataKey:"",seriesDataKey:[],chart:""}}},divId:{type:String,default:""},xNameText:{type:String,default:""},transverse:{type:Boolean,default:!1},xAxisName:{type:String,default:""},yAxisName:{type:[String,Array],default:""},isTwoYaxis:{type:Boolean,default:!1},tooltipFormatter:{type:Function},needLinkage:{type:Boolean,default:!1}},watch:{data:{immediate:!0,handler(e,t){this.$nextTick((()=>{this.dataHandle()}))}}},mounted(){this.xActiveName=this.xNameText},methods:{dataHandle(){for(var e=this.data.data||[],t=this.data.xDataKey,a=this.data.seriesDataKey||[],s=[],r=[],i=0;i<a.length;i++)r.push(a[i].name),a[i]["itemStyle"]={borderRadius:3,borderColor:"#fff",borderWidth:.5},a[i]["barMaxWidth"]=25,a[i]["data"]={};for(i=0;i<e.length;i++){var n=e[i][t];-1==s.indexOf(n)&&s.push(n);for(var l=0;l<a.length;l++)a[l]["data"][n]=e[i][a[l]["key"]]}for(i=0;i<a.length;i++){var o=[];for(l=0;l<s.length;l++){var d=a[i].data[s[l]];void 0==d&&(d="-"),"scatter"!=a[i].type&&"line"!=a[i].type||(a[i].type="line",a[i].symbol="circle",a[i].smooth=!0,a[i].symbolSize=9,a[i].color="#5D7092",a[i].lineStyle={type:"dashed",color:"#5D7092"},a[i].itemStyle={borderWidth:2,color:"rgba(93, 112, 146, .3)",borderColor:"#5D7092"}),o.push(d)}a[i].data=o}var c=["正面提及量","中性提及量","负面提及量"];r.sort(((e,t)=>{var a=-1==c.indexOf(e)?100:c.indexOf(e),s=-1==c.indexOf(t)?100:c.indexOf(t);return a-s})),this.drawChart(s,a,r)},drawChart(e,t,a){var s=this,r=document.getElementById(this.divId);if(null==r)return!1;var i=this.$echarts.getInstanceByDom(r);i&&i.dispose();var n=this.$echarts.init(r),l={color:["#0077FF","#3ED4A9","#5D7092","#FFC157","#7163FD","#95D8F2","#BA70CA","#FA9C78","#11999C","#FEBAD6"],legend:{show:!0,bottom:0,itemWidth:14,data:a},grid:{top:55,right:10,bottom:40,left:10,containLabel:!0},tooltip:{trigger:"axis",borderColor:"#0077FF",padding:0,axisPointer:{type:"cross",shadowStyle:{color:"rgba(41, 148, 255, 0.1)"}}},xAxis:{name:this.xAxisName,axisLabel:{margin:15,textStyle:{},rotate:e.length>=5?35:0,width:100,formatter:function(e){return"{"+(s.xActiveName==e?"active":"normal")+"|"+e+"}"},rich:{active:{fontSize:16,color:"#0077ff",fontWeight:"bold"},normal:{fontSize:14,color:"rgba(0, 0, 0, 0.45)",fontWeight:"400"}}},triggerEvent:!0,type:this.transverse?"":"category",data:e,axisPointer:{type:"shadow"},splitLine:{show:!0},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},yAxis:{name:this.yAxisName,show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==s.yAxisName?100*e+"%":"提及量"==s.yAxisName?s.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},series:t};this.isTwoYaxis&&(l.yAxis=[{name:this.yAxisName[0],show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==s.yAxisName[0]?100*e+"%":"提及量"==s.yAxisName[0]?s.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},{name:this.yAxisName[1],show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==s.yAxisName[1]?100*e+"%":"提及量"==s.yAxisName[0]?s.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}}]),this.transverse&&(l.yAxis.data=e,l.yAxis.type="category",delete l.xAxis.type,delete l.xAxis.data),this.tooltipFormatter&&(l.tooltip.formatter=this.tooltipFormatter),n.setOption(l),this.chart=n,window.mineChart=n,this.chartLis(n),window.addEventListener("resize",(()=>{n.resize()}))},chartLis(e){const t=this;e.getZr().off("click"),e.getZr().on("click",(function(a){const s=[a.offsetX,a.offsetY];if(e.containPixel("grid",s)){var r=e.convertFromPixel({seriesIndex:0},[a.offsetX,a.offsetY])[0];let s=e.getOption(),i=s.xAxis[0].data[r];t.xActiveName=i,t.dataHandle(),t.xNameColor=i,t.needLinkage&&t.dataHandle(),t.$emit("seeDetail",{name:i}),t.$emit("drill",{name:i})}})),e.getZr().on("mousemove",(t=>{var a=[t.offsetX,t.offsetY];e.containPixel("grid",a)?e.getZr().setCursorStyle("pointer"):e.getZr().setCursorStyle("default")}))},downloadChart(e){var t=this.chart.getDataURL({pixelRatio:2,backgroundColor:"#fff"});const a=document.createElement("a");a.href=t,a.setAttribute("download",e),a.click()}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"dbba1720",null),d=o.exports},7371:function(e,t,a){"use strict";a.d(t,{Z:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.data&&e.data.data.length?a("div",{staticClass:"public-chart-div",attrs:{id:e.divId}}):a("no-data")],1)},r=[],i=a(4806),n=a.n(i),l={props:{data:{type:Object,default(){return{data:[],xDataKey:"",seriesDataKey:[],chart:""}}},divId:{type:String,default:""},transverse:{type:Boolean,default:!1},xAxisName:{type:String,default:""},yAxisName:{type:[String,Array],default:""},isTwoYaxis:{type:Boolean,default:!1},isShadowClick:{type:Boolean,default:!0},legendSelect:{type:Array,default(){return[]}},tooltipFormatter:{type:Function},axisLabelColor:{type:String,default:""},xAxisMin:{type:[String,Number]},yAxisMax:{type:Number},yAxisMin:{type:Number},gridTop:{type:Number,default:55}},data(){return{chartOption:[]}},watch:{data:{immediate:!0,handler(e,t){this.$nextTick((()=>{this.dataHandle()}))}}},methods:{dataHandle(){for(var e=this,t=this.data.data||[],a=this.data.xDataKey,s=this.data.seriesDataKey||[],r=[],i={},n=[],l=0;l<s.length;l++)n.push(s[l].name),s[l]["itemStyle"]={borderRadius:1,borderColor:"#fff",borderWidth:.3,emphasis:{shadowBlur:10,shadowColor:"#0077FF"}},s[l]["barMaxWidth"]=25,s[l]["data"]={},i[s[l]["name"]]=-1!=this.legendSelect.indexOf(s[l]["name"]);for(l=0;l<t.length;l++){var o=t[l][a];-1==r.indexOf(o)&&r.push(o);for(var d=0;d<s.length;d++)s[d]["data"][o]=t[l][s[d]["key"]]}"Invalid Date"!==new Date(r[0])&&r.sort(((t,a)=>e.$moment(t).format("x")-e.$moment(a).format("x")));for(l=0;l<s.length;l++){var c=[];for(d=0;d<r.length;d++){var h=s[l].data[r[d]];void 0==h&&(h="-"),c.push(h)}s[l].data=c}var m=["正面提及量","中性提及量","负面提及量"];n.sort(((e,t)=>{var a=-1==m.indexOf(e)?100:m.indexOf(e),s=-1==m.indexOf(t)?100:m.indexOf(t);return a-s})),this.drawChart(r,s,i,n)},drawChart(e,t,a,s){var r=this,i=document.getElementById(this.divId);if(null!=i){var l=this.$echarts.getInstanceByDom(i);l&&l.dispose();var o=this.$echarts.init(i),d={color:["#0077FF","#3ED4A9","#5D7092","#FFC157","#7163FD","#95D8F2","#BA70CA","#FA9C78","#11999C","#FEBAD6"],legend:{show:!0,bottom:0,itemWidth:14,data:s},grid:{top:this.gridTop,right:10,bottom:40,left:10,containLabel:!0},tooltip:{trigger:"axis",padding:0,axisPointer:{type:"cross",shadowStyle:{color:"rgba(41, 148, 255, 0.1)"}},borderColor:"#0077FF"},xAxis:{name:this.xAxisName,axisLabel:{margin:20,fontSize:14,rotate:e.length>=5?35:0,width:100},triggerEvent:!0,type:this.transverse?"":"category",data:e,axisPointer:{type:"shadow"},splitLine:{show:!0},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},yAxis:{name:this.yAxisName,triggerEvent:!0,show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==r.yAxisName?100*e+"%":"提及量"==r.yAxisName?r.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},series:t};this.axisLabelColor&&(this.transverse?(d["yAxis"]["axisLabel"]["color"]=this.axisLabelColor,d["yAxis"]["inverse"]=!0,d["yAxis"]["axisLabel"]["width"]=100,d["yAxis"]["axisLabel"]["overflow"]="truncate",d["xAxis"]["axisLabel"]={margin:15,fontSize:14}):d["xAxis"]["axisLabel"]["color"]=this.axisLabelColor),this.legendSelect.length&&(d.legend["selected"]=a),this.isTwoYaxis&&(d.yAxis=[{name:this.yAxisName[0],show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==r.yAxisName[0]?100*e+"%":"提及量"==r.yAxisName[0]?r.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}},{name:this.yAxisName[1],show:!0,axisLabel:{show:!0,formatter(e){return"负面提及率"==r.yAxisName[1]?100*e+"%":"提及量"==r.yAxisName[1]?r.$publicHandle.makeDataUnit(e):e}},splitLine:{show:!0,lineStyle:{color:"#F0F0F0"}},splitArea:{interval:1,show:!0,areaStyle:{color:["rgba(255, 255, 255, 0)","rgba(250, 250, 250, 1)"]}},axisTick:{show:!1},axisLine:{show:!1},nameTextStyle:{color:"rgba(0,0,0,0.45)"}}]),this.xAxisMin&&(d.xAxis.min=this.xAxisMin),this.xAxisMax&&(d.xAxis.max=this.xAxisMax),this.transverse&&(d.yAxis.data=e,d.yAxis.type="category",delete d.xAxis.type,delete d.xAxis.data),this.tooltipFormatter&&(d.tooltip.formatter=this.tooltipFormatter),this.yAxisMax&&(d.yAxis=n().assign(n().cloneDeep(d.yAxis),{max:this.yAxisMax,axisLabel:{formatter:e=>`${n().isInteger(e)?e:`${this.accMul(e.toPrecision(2),100)}%`}`}})),this.yAxisMin&&(d.yAxis=n().assign(n().cloneDeep(d.yAxis),{min:this.yAxisMin})),this.chartOption=d,o.setOption(d),this.chart=o,this.chartLis(o),window.addEventListener("resize",(()=>{o.resize()}))}},accMul(e,t){var a=0,s=e.toString(),r=t.toString();try{a+=s.split(".")[1].length}catch(i){}try{a+=r.split(".")[1].length}catch(i){}return Number(s.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,a)},chartLis(e){var t=1==this.transverse?"yAxis":"xAxis";const a=this;this.isShadowClick?(e.getZr().off("click"),e.getZr().on("click",(function(t){const s=[t.offsetX,t.offsetY];if(e.containPixel("grid",s)){var r=e.convertFromPixel({seriesIndex:0},[t.offsetX,t.offsetY])[0];let s=e.getOption(),i=s.xAxis[0].data[r];a.$emit("seeDetail",{name:i}),a.$emit("drill",{name:i})}})),e.getZr().on("mousemove",(t=>{var a=[t.offsetX,t.offsetY];e.containPixel("grid",a)?e.getZr().setCursorStyle("pointer"):e.getZr().setCursorStyle("default")}))):(e.off("click"),e.on("click",(e=>{"series"==e.componentType?a.$emit("seeDetail",{seriesName:e.seriesName,name:e.name}):e.componentType==t&&a.$emit("drill",{name:e.value})})))},downloadChart(e){var t=this.chart.getDataURL({pixelRatio:2,backgroundColor:"#fff"});const a=document.createElement("a");a.href=t,a.setAttribute("download",e),a.click()}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,"403f8d0d",null),h=c.exports},6943:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.data.length?a("div",{staticClass:"public-chart-div",attrs:{id:e.divId}}):a("no-data")],1)},r=[],i={props:{data:{type:Object,default(){return{data:[]}}},divId:{type:String,default:""},radius:{type:Array,default(){return[["0%","50%"]]}},showLabel:{type:Boolean,default:!1},showInnerLabel:{type:Boolean,default:!1},showLegend:{type:Boolean,default:!1},total:{type:[String,Number],default:""},legendData:{type:Array},legendData:{type:Array},pieChartTextShow:{type:Boolean,default:!0}},data(){return{chart:""}},watch:{data:{immediate:!0,handler(e,t){JSON.stringify(e)!==JSON.stringify(t)&&this.$nextTick((()=>{this.dataHandle(e.data||[])}))}}},methods:{dataHandle(e){for(var t=this,a=0;a<e.length;a++){var s,r=e[a],i=0;if(r.length>8){r.sort(((e,t)=>t.value-e.value)),s=r.slice(0,7);for(var n={name:"其他",value:0},l=7;l<r.length;l++)n.value+=r[l].value;s.push(n)}else s=r.slice(0,8);for(l=0;l<s.length;l++)s[l]["label"]=i<3&&0==a?{show:!0,formatter(e){var a="{a|"+e.name+"：}";return a+="\n{hr|}\n",a+="{b|提及量："+t.$publicHandle.makeDataUnit(e.value)+"}",a+="\n{hr|}\n",a+="{b|占比：\t\t\t"+e.percent+"%}",a}}:{show:!1},i++;e[a]={type:"pie",data:s,center:["50%","48%"],radius:this.radius[a],label:{show:this.showLabel||0==a&&this.showInnerLabel,fontSize:16,backgroundColor:"white",borderColor:"#0077ff",borderWidth:1,borderRadius:4,padding:[4,6,4,6],rich:{a:{color:"rgba(0, 0, 0, 0.85)",lineHeight:28,fontSize:16,align:"left",fontWeight:600},hr:{width:"100%",height:0,fontWeight:500},b:{color:"rgba(0, 0, 0, 0.95); ",fontSize:14,lineHeight:22,align:"left"}}},itemStyle:{borderRadius:5,borderColor:"#fff",borderWidth:1},labelLine:{length:20,length2:40}}}this.drawChart(e)},drawChart(e){const t=this;var a=document.getElementById(this.divId);if(null!=a){var s=this.$echarts.getInstanceByDom(a);s&&s.dispose();var r=this.$echarts.init(a),i={color:["#0077FF","#3ED4A9","#5D7092","#FFC157","#7163FD","#95D8F2","#BA70CA","#FA9C78","#11999C","#FEBAD6"],title:{text:""!=this.total?"总提及量： \n\n"+this.$publicHandle.makeDataUnit(this.total):0,textAlign:"center",left:"50%",top:"41.5%",textStyle:{color:"rgba(0, 0, 0, .8)",fontSize:"18px"}},legend:{show:this.showLegend,bottom:0,type:"scroll",itemGap:20},grid:{top:40,right:20,bottom:40,left:20,containLabel:!0},tooltip:{padding:0,formatter(e){var a='<div class="public-tooltip-div">';return a+='<div class="axis-name">'+e.marker+e.name+"</div>",a+='<div class="each-series"><span class="each-series-name">提及量：</span>'+e.value+"</div>",a+='<div class="each-series"><span class="each-series-name">占比：</span>'+e.percent+"%</div></div>",t.pieChartTextShow&&(a+='<div class="public-tooltip-click-tips">点击区域可查看分析</div>'),a}},series:e};this.legendData&&(i.legend.data=this.legendData),i.title.show=""!=this.total,r.setOption(i),this.chart=r,this.chartLis(r),window.addEventListener("resize",(()=>{r.resize()}))}},chartLis(e){e.off("click"),e.on("click",(e=>{var t=e.name;0==e.seriesIndex&&this.$emit("seeDetail",{name:t})}))},downloadChart(e){var t=this.chart.getDataURL({pixelRatio:2,backgroundColor:"#fff"});const a=document.createElement("a");a.href=t,a.setAttribute("download",e),a.click()}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"78d4b5b5",null),d=o.exports},3469:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.length?a("div",{staticClass:"public-chart-div",staticStyle:{height:"450px"},attrs:{id:e.divId}}):a("no-data")],1)},r=[],i={props:{divId:{type:String,default:"indexChart"},data:{type:[Array,Object],default(){return[]}},attr:{type:String,default:""},attrName:{type:String,default:""},maxChildren:{type:Number,default:1}},watch:{data:{immediate:!0,handler(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.drawChart()}}},methods:{drawChart(){var e=this;this.$nextTick((()=>{var t=document.getElementById(this.divId),a=this.$echarts.init(t),s={tooltip:{trigger:"item",triggerOn:"mousemove",formatter(t){var a=`<div>${t.marker+t.name}</div>`,s=t.data,r="experienceValue"==e.attr?e.$publicHandle.formatNum(s.experienceValue):e.$publicHandle.formatPercent(s.negativeMentionRate)+"%";return a+=`<div>${e.attrName}：${r}</div>`,a+=`<div>提及量：${e.$publicHandle.makeDataUnit(s.totalMentionValue)}</div>`,a}},series:[{type:"tree",edgeShape:"curve",initialTreeDepth:-1,data:e.data,left:"1%",right:"2%",top:"8%",bottom:"10%",symbolSize:12,symbol:"emptyCircle",orient:"vertical",itemStyle:{color:"#0077FF"},lineStyle:{color:"#C2CDD6",width:.8,curveness:.8},label:{position:"right",verticalAlign:"middle",fontWeight:500,color:"rgba(0, 0, 0, 0.75)",width:700/e.maxChildren,overflow:"breakAll",lineHeight:18,formatter:function(t){var a=t.data||{},s=(e.$publicHandle.makeDataUnit(a.totalMentionValue),t="experienceValue"==e.attr?e.$publicHandle.formatNum(a.experienceValue):e.$publicHandle.formatPercent(a.negativeMentionRate)+"%","");let r=a.name;return r.length>6&&(r=r.slice(0,5)+"..."),s+="{title|"+r+"}",s},rich:{title:{fontSize:14,overflow:"hidden",overflow:"truncate",align:"center",fontWeight:600,color:"rgba(0, 0, 0, 0.75)"},desc:{fontSize:13,fontWeight:400,overflow:"truncate",color:"#5F7483"}}},animationDurationUpdate:750}]};a.setOption(s)}))}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,null,null),d=o.exports},5487:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.length?a("div",{staticClass:"public-chart-div",staticStyle:{height:"380px"},attrs:{id:e.divId}}):a("no-data")],1)},r=[],i={props:{divId:{type:String,default:""},data:{type:[Array,Object],default(){return[]}}},data(){return{chart:"",wordCloudData:[],sizeRange:[14,40]}},watch:{data:{immediate:!0,handler(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.$nextTick((()=>{this.drawWordChart(),this.drawChart()}))}}},created(){let e=document.body.clientWidth;e<=1366&&(this.sizeRange=[12,25])},methods:{drawChart(){var e=document.getElementById(this.divId);if(e){var t=this.$echarts.init(e),a={tooltip:{},series:[{textStyle:{color:function(){var e=["#0077FF","#3ED4A9","#5D7092","#FFC157","#7163FD","#95D8F2","#BA70CA","#FA9C78","#11999C","#FEBAD6"],t=Math.floor(10*Math.random());return e[t]}},type:"wordCloud",shape:"circle",sizeRange:this.sizeRange,rotationRange:[0,0],gridSize:8,drawOutOfBound:!1,data:this.wordCloudData}]};t.setOption(a),this.chart=t,this.chartLis(t)}},drawWordChart(){var e=this.data?JSON.parse(JSON.stringify(this.data)):[];e=e.sort(((e,t)=>t.value-e.value)),this.wordCloudData=e.slice(0,50)},chartLis(e){const t=this;e.off("click"),e.on("click",(e=>{t.$emit("click",{name:e.name})}))},downloadChart(e){var t=this.chart.getDataURL({pixelRatio:2,backgroundColor:"#fff"});const a=document.createElement("a");a.href=t,a.setAttribute("download",e),a.click()}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"5c1e85b6",null),d=o.exports},3532:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"f-s-12",class:e.showTitle?"m-t-5":""},[e.showTitle?a("span",[e._v("标准关键词：")]):e._e(),e.showData.length?[e._l(e.showData,(function(t,s){return["正面"==t.extractedsense&&t.standardkeyword?a("div",{staticClass:"public-inline-block tag green-tag m-r-10 p-l-8 p-r-8"},[e._v(e._s(t.standardkeyword))]):e._e(),"中性"==t.extractedsense&&t.standardkeyword?a("div",{staticClass:"public-inline-block tag blue-tag m-r-10 p-l-8 p-r-8",attrs:{ley:""}},[e._v(e._s(t.standardkeyword))]):e._e(),"负面"==t.extractedsense&&t.standardkeyword?a("div",{staticClass:"public-inline-block tag red-tag m-r-10 p-l-8 p-r-8"},[e._v(e._s(t.standardkeyword))]):e._e()]}))]:e._e()],2)},r=[],i={props:{showTitle:{type:Boolean,default:!0},indexTypeName:{type:String,default:""},data:{type:Object,default(){return{}}}},computed:{showData(){const e=this.data.extractedinfo||[];for(var t=this.indexTypeName||"全领域业务",a=[],s=[],r=0;r<e.length;r++)e[r].extracteddomain==t&&-1==s.indexOf(e[r].standardkeyword)&&(a.push(e[r]),s.push(e[r].standardkeyword));return a}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"dae8edc6",null),d=o.exports},7141:function(e,t,a){"use strict";a.d(t,{Z:function(){return f}});var s,r,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"relative"},[e.labelType?a("div",{staticClass:"label"},[a("el-button",{staticClass:"m-r-10",attrs:{size:"small",type:"primary",plain:""},on:{click:e.labelClick}},[e._v("一键标注")])],1):e._e(),e.textDetailFormJsxShow?a("textDetailFormJsx",{attrs:{trainData:e.trainData,data:e.data,tableType:e.tableType,indexTypeName:e.indexTypeName},on:{correction:e.correctionHandle}}):e._e(),a("el-dialog",{attrs:{appendToBody:!0,visible:e.labelFlag,width:"560px",modalFlag:!1,title:"一键标注",showClose:!0},on:{"update:visible":function(t){e.labelFlag=t},close:e.closeDialogHandle}},[a("el-form",{ref:"formLabel",staticClass:"form-label",attrs:{model:e.dataLabel,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"添加标注：",prop:"keyword",rules:[{required:!0,message:"请输入标注",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.dataLabel.keyword,callback:function(t){e.$set(e.dataLabel,"keyword",t)},expression:"dataLabel.keyword"}})],1),a("el-form-item",{attrs:{label:"标准关键词："}},[a("el-select",{staticStyle:{width:"100%"},attrs:{"value-key":"standardKeywordName",filterable:"",remote:"","remote-method":e.remoteMethod,placeholder:"请选择"},model:{value:e.standardKeyword,callback:function(t){e.standardKeyword=t},expression:"standardKeyword"}},e._l(e.options,(function(t,s){return a("el-option",{key:t.dataId+s,attrs:{label:t.name,value:{standardKeywordId:t.dataId,standardKeywordName:t.name}}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.name,placement:"top-start"}},[a("span",{staticClass:"float-left full-item",staticStyle:{width:"30%"}},[e._v(e._s(t.name))])]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.indexNames,placement:"top-start"}},[a("span",{staticClass:"float-right full-item",staticStyle:{width:"68%"}},[e._v(e._s(t.indexNames))])])],1)})),1)],1),a("el-form-item",[a("div",{staticClass:"l-h-20",staticStyle:{color:"#909090"}},[e._v("请复制原文中的语料进行标注，提交标注后会流转至「综合管理-指标体系管理-语料训练」中，通过人工审核后进行算法应用。")])]),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{size:"small"},on:{click:e.closeDialogHandle}},[e._v("取消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submit}},[e._v("提交")])],1)],1)],1),a("el-dialog",{attrs:{appendToBody:!0,visible:e.correctionVisible,width:"560px",modalFlag:!1,title:"一键纠错",showClose:!0},on:{"update:visible":function(t){e.correctionVisible=t},close:e.closeDialogHandle}},[a("el-form",{ref:"correctionForm",staticClass:"form-label",attrs:{model:e.correctionData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"纠错语料："}},[a("span",[e._v(e._s(e.correctionData.keyword))])]),a("el-form-item",{attrs:{label:"标准关键词："}},[a("span",[e._v(e._s(e.correctionData.standardKeywordName))])]),a("el-form-item",{attrs:{label:"纠错类型：",prop:"errorType",rules:[{required:!0,message:"请输入标注",trigger:["blur","change"]}]}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.correctionData.errorType,callback:function(t){e.$set(e.correctionData,"errorType",t)},expression:"correctionData.errorType"}},e._l(e.errorTypeOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"说明："}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.correctionData.errorDescribe,callback:function(t){e.$set(e.correctionData,"errorDescribe",t)},expression:"correctionData.errorDescribe"}})],1),a("el-form-item",[a("div",{staticClass:"l-h-20",staticStyle:{color:"#909090"}},[e._v("语料纠错提交后会流转至「综合管理-指标体系管理-语料管理」中，通过人工审核后进行算法应用。")])]),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{size:"small"},on:{click:e.closeDialogHandle}},[e._v("取消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.submitCorrection}},[e._v("提交")])],1)],1)],1)],1)},n=[],l=a(311),o=a.n(l),d=a(6427),c=(a(4806),{name:"textDetailFormJsx",props:{data:{type:Object,default(){return{}}},tableType:{type:String,default:"test"},indexTypeName:{type:String,default:"test"},trainData:{type:Object,default(){return{}}}},data(){return{template:"",analysisResult:{}}},created(){this.init()},mounted(){this.clearNoneValue()},methods:{clearNoneValue(){var e=document.getElementById("textForm").querySelectorAll(".el-form-item");e.forEach((e=>{e.style.display="block",""==e.getElementsByClassName("el-form-item__content")[0].innerText&&(e.style.display="none")}))},init(){for(var e=this.data,t=d.Z.details[this.tableType],a="<el-form ref='form' :model=\"data\" label-width='150px' class='text-form' id='textForm'>",s=0;s<t.length;s++){var r=t[s];a+=`<el-form-item class="each-row" label='${r.label}：' key='${s}'>`,a+="<div class='value'>";for(var i=0;i<r.keys.length;i++){var n=this.changeNameFunc(r.keys[i]),l=e[n]||"",o="";-1!=["url","postsLink"].indexOf(n)?o=`<a target="_blank" href="${l}">${l}</a>`:"isOuter"==n?o=`<span>${"是"==e["isOuter"]?"（外部数据源）":"（内部数据源）"}</span>`:"commonType"==n?o=`<span>${4==e["commonType"]?"回复":""}</span>`:"standardkeyword"==n?o='<key-words :showTitle="false" :data="data.analysisResult" :indexTypeName="indexTypeName"></key-words>':-1!=["comment","content","title","postsContent","postsTitle"].indexOf(n)?o+=this.hightLight(e,n):o=`<span>${l}</span>`,"content"==n&&"consulting_service"==this.tableType&&(o=o.replaceAll("访客","\n访客"),o=o.replaceAll("客服","\n客服")),"seriesName"!=n&&"modelName"!=n||!l||(o=`  /  ${l}`),o=o.replace("\n访客","访客"),a+=o,r.keys.length>1&i+1!=r.keys.length&l&&(a+="<span>/</span>")}a+="</div>",a+="</el-form-item>"}a+="</el-form>",this.template=`<div>${a}</div>`},hightLight(e,t){var a=e[t];if(!a)return"";var s=this.makeExtractedinfoObj(e),r=!!Object.keys(this.trainData).length,i=0;for(var n in s){for(var l=n||a,o=this.changeStrToArr(a,l),d=o.data,c=o.modifyIndexs,h=0;h<c.length;h++)for(var m=d[c[h]],u=0;u<s[n].length;u++){var p,y=s[n][u],g=y.extractedfeature||"",v=y.extractedsentiment||"",f=y.standardkeyword||"";if("NULL"==g)-1==v.indexOf("&")?p="YING_TE_ZHENG":(p="CI_CAO",v=v.split("&"));else{p="BAI_MING_DAN";var b,w=v.length,S=m.indexOf(g),D=m.indexOf(v);S>D&&(b=S,S=D,D=b,w=g.length),v=m.substring(S,D+w)}if(v&&!/<[^>]+>/g.test(v)){for(var x=this.changeStrToArr(m,v),k=x.data,C=x.modifyIndexs,T=0;T<C.length;T++){var A=C[T];k[A]=this.replaceHightLight(k[A],p,f,r,i,u)}m=d[c[h]]=k.join("")}}a=d.join(""),i++}return a},replaceHightLight(e,t,a,s,r,i){var n={YING_TE_ZHENG:"background: rgb(255, 218, 163);",CI_CAO:"background: rgb(172, 208, 249);",BAI_MING_DAN:"background: rgb(210, 241, 236);"};return e=s?e.replaceAll(e,`<span style="padding: 0 2px; border-radius: 3px; display: inline-block; margin: 0 2px; ${n[t]}">${e}</span>`):e.replaceAll(e,`<el-popover placement="top-start" trigger="hover">\n          <div>\n            <div><span style="display: inline-block; width: 90px; vertical-align: super;">标准关键词：</span><span style="display: inline-block; vertical-align: super;">${a}</span>\n            <el-button type="text" style="padding-bottom: 0px; margin-left: 10px; padding-top: 0; vertical-align: super;" @click="correction(${r}, ${i})">纠错</el-button></div>\n          </div>\n          <span slot="reference" style="padding: 0 2px; border-radius: 3px; display: inline-block; margin: 0 2px;  ${n[t]}">${e}</span>\n          </el-popover>`),e},makeExtractedinfoObj(e){e.analysisResult=e.analysisResult||{};for(var t=e.analysisResult.extractedinfo||[],a={},s={},r=0;r<t.length;r++)if(t[r].extracteddomain==this.indexTypeName){var i=t[r].sourcephrase.trim();void 0==s[i]&&(s[i]=[]),void 0==a[i]&&(a[i]={}),void 0==a[i][t[r].standardkeyword]&&(a[i][t[r].standardkeyword]=1,s[i].push(t[r]))}return Object.keys(this.trainData).length&&(s={},s[this.trainData.sentence.trim()]=[{sourcephrase:this.trainData.sentence.trim(),extractedfeature:"NULL",extractedsentiment:this.trainData.keyword.trim()}]),this.analysisResult=s,s},changeStrToArr(e,t){Array.isArray(t)||(t=[t]);var a=e,s=t,r=[],i=[],n=[],l=0,o=!0;a.length&&n.push(a[l]);var d=(new Date).getTime();while(a.length){if((new Date).getTime()-d>=3e3)return a="",this.$message({message:"解析超时，请联系管理员",type:"error"}),{data:[e],modifyIndexs:[]};n.length>a.length&&(r.push(n.join("")),a=a.substring(l+1),l=0);for(var c=0;c<s.length;c++)if(0==s[c].toLowerCase().indexOf(n.join("").toLowerCase()))s[c].toLowerCase()==n.join("").toLowerCase()?(r.push(n.join("")),i.push(r.length-1),a=a.substring(l+1),l=0,n=[a[l]],o=!0):(l++,n.push(a[l]),o=!1);else if(0==s[c].toLowerCase().indexOf(n[l].toLowerCase())){var h=n.splice(0,l);r.push(h.join("")),a=a.substring(l),l=0,o=!1}else{if(c+1!=s.length)continue;o?(l++,n.push(a[l])):o=!0}}return{data:r,modifyIndexs:i}},changeNameFunc(e){let t=e.split("");return t.map(((e,a)=>{"_"==e&&(t.splice(a,1),t[a]=t[a].toUpperCase())})),t.join("")},correction(e,t){e=Object.keys(this.analysisResult)[e],this.$emit("correction",this.analysisResult[e][t])}},render(e){var t=this,a=o().extend({template:this.template,data:function(){return{data:t.data,indexTypeName:t.indexTypeName||"全领域业务",trainData:t.trainData}},methods:{correction:t.correction}});return e(a,{})}}),h=c,m=a(1001),u=(0,m.Z)(h,s,r,!1,null,"ec08a6f2",null),p=u.exports,y={components:{textDetailFormJsx:p},props:{data:{type:Object,default(){return{}}},indexTypeName:{type:String,default:""},tableType:{type:String,default:"test"},labelType:{type:Boolean,default:()=>!0},indexTypeName:{type:String,default:""},trainData:{type:Object,default(){return{}}}},data(){return{textDetailFormJsxShow:!1,labelFlag:!1,textData:{},flag:!0,correctionVisible:!1,dataLabel:{},standardKeyword:{},correctionData:{},options:[],formKey:0,errorTypeOpt:[{label:"匹配的标准关键词错误",value:"匹配的标准关键词错误"},{label:"语料无效（没有观点）",value:"语料无效（没有观点）"},{label:"其他",value:"其他"}],extractedfsKey:{}}},watch:{data:{immediate:!0,handler(e){this.textDetailFormJsxShow?(this.textDetailFormJsxShow=!1,this.$nextTick((()=>{this.textDetailFormJsxShow=!0}))):this.textDetailFormJsxShow=!0}}},methods:{closeDialogHandle(){this.labelFlag=!1,this.correctionVisible=!1,this.correctionData={},this.dataLabel={}},labelClick(){this.labelFlag=!0},submit(){this.$refs["formLabel"].validate((async e=>{if(!e)return console.log("error submit!!"),!1;{this.dataLabel=Object.assign(this.dataLabel,this.standardKeyword),this.dataLabel.addType=4,this.dataLabel.originalId=this.data.dataId,this.dataLabel.sentence=this.dataLabel.keyword;const e=await this.$store.dispatch("corpusAddCorpus",this.dataLabel);e&&(-1!=e["系统已存在的新词"].indexOf(this.dataLabel.keyword)?this.$message({message:"新词已存在！",type:"error"}):(this.$message.success("更新成功"),this.closeDialogHandle()))}}))},submitCorrection(){this.$refs["correctionForm"].validate((async e=>{if(!e)return console.log("error submit!!"),!1;{this.correctionData.sourceId=this.textData.dataId,this.correctionData.source=this.textData.dataSourceName;const e=await this.$store.dispatch("errorCheck",this.correctionData);e&&(this.$message.success("提交成功"),this.closeDialogHandle())}}))},async getStKeywordSearch(){this.options=(await this.$store.dispatch("stKeywordSearch",{indexTypeName:this.indexTypeName})).records},async remoteMethod(e){""!==e?(this.loading=!0,this.options=(await this.$store.dispatch("stKeywordSearch",{names:e,indexTypeName:this.indexTypeName})).records,this.loading=!1):this.options=[]},correctionHandle(e){this.correctionData.keyword=-1!=e.extractedfs.indexOf("NULL-")?e.extractedfs.split("-")[1]:e.extractedfs.indexOf("-")?e.extractedfs.replaceAll("-","->"):e.extractedfs,this.correctionData.standardKeywordName=e.standardkeyword,this.correctionData.standardKeywordId=e.standardkeywordid||"",this.correctionData.standardKeywordType=e.extractedsense||"",this.correctionVisible=!0}}},g=y,v=(0,m.Z)(g,i,n,!1,null,"672f364d",null),f=v.exports},5292:function(e,t,a){"use strict";a.d(t,{Z:function(){return f}});var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("chart-box",e._b({attrs:{vocExplain:"共查询到 "+(e.remarkData.totalCount||0)+" 条数据",chartId:e.chartId,title:e.titleTop,data:e.data,needDownLoad:e.needDownLoad},on:{download:e.downloadHandle}},"chart-box",e.$attrs,!1),[s("div",{staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[s("div",{staticClass:"m-t-20"},[s("el-table",{staticStyle:{width:"100%"},attrs:{size:"small",data:e.data}},[s("el-table-column",{attrs:{align:"center",width:"100",type:"index",label:"序号"}}),s("el-table-column",{attrs:{align:"left",prop:"standardKeyword",label:"内容"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"p-t-10 p-b-10 detail-content"},[s("div",{staticClass:"title m-t-5",domProps:{innerHTML:e._s(t.row[e.computeTitle(t.row.dataSourceName,"title")]||t.row[e.computeTitle(t.row.dataSourceName,"content")]||t.row[e.computeTitle(t.row.dataSourceName,"comment")])}}),s("div",{staticClass:"detail m-t-5 f-s-12",domProps:{innerHTML:e._s(t.row[e.computeTitle(t.row.dataSourceName,"content")]||t.row[e.computeTitle(t.row.dataSourceName,"comment")]||t.row[e.computeTitle(t.row.dataSourceName,"answer_fraction")]||t.row[e.computeTitle(t.row.dataSourceName,"answer_content")])}}),s("text-infos",{attrs:{textType:e.typeData[t.row.dataSourceName],data:t.row}}),s("key-words",{attrs:{indexTypeName:e.indexTypeName,data:t.row.analysisResult}})],1)]}}])}),s("el-table-column",{attrs:{align:"right",width:"250",label:""},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"p-r-20"},[s("img",{staticClass:"text-detail-empty",attrs:{src:a(693),alt:""}}),t.row.oneId?s("el-button",{attrs:{type:"text"},on:{click:function(a){return e.seeUserDetail(t.row)}}},[e._v(e._s(t.row.user||t.row.name))]):s("span",{staticClass:"detail-empty"},[e._v("-")]),s("mine-split"),s("el-button",{attrs:{type:"text"},on:{click:function(a){return e.seeDetail(t.row)}}},[e._v("查看原文")])],1)]}}])})],1),s("el-pagination",{staticClass:"pagination",attrs:{background:"","current-page":e.remarkData.page,"page-sizes":[10,20,30],"page-size":e.remarkData.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.remarkData.totalCount},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle,"update:currentPage":function(t){return e.$set(e.remarkData,"page",t)},"update:current-page":function(t){return e.$set(e.remarkData,"page",t)}}})],1)])]),s("mine-dialog",{attrs:{appendToBody:!0,dialogFormVisible:e.textDetailShow,width:"1200px",modalFlag:!1,title:e.titleTop,showClose:!0},on:{close:e.closeDialogHandle}},[e.textDetailShowInner?s("text-detail-form",e._b({attrs:{slot:"option",indexTypeName:e.indexTypeName,data:e.textDetailFormData,tableType:e.textDetailFormTableType},slot:"option"},"text-detail-form",e.$attrs,!1)):e._e()],1)],1)},r=[],i=a(3532),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"text-infos m-t-5 f-s-12"},[e.data.dataSourceName?a("div",{staticClass:"each-text-info p-r-15"},[e._v(e._s(e.data.dataSourceName||""))]):e._e(),e.data.brandName||e.data.seriesName?a("div",{staticClass:"each-text-info p-l-15 p-r-15"},[e._v(e._s(e.data.brandName?"【"+e.data.brandName+"】":"")+e._s(e.data.seriesName||""))]):e._e(),e.data.createTime?a("div",{staticClass:"each-text-info p-l-15 p-r-15"},[e._v(e._s(e.data.createTime||""))]):e._e(),e.data.vin?a("div",{staticClass:"each-text-info p-l-15 p-r-15"},[a("span",{staticClass:"m-r-10"},[e._v(e._s(e.data.vin||""))]),"是"==e.data.isCarOwner||"否"==e.data.isCarOwner?a("span",[e._v(e._s("是"==e.data.isCarOwner?"车主":"否"==e.data.isCarOwner?"非车主":""))]):e._e()]):e._e()])},l=[],o={props:{data:{type:Object,default(){return{}}},textType:{type:String,default:""}},data(){return{textTypes:{job_dwd_evt_comment_dtl:"帖子评论",job_dwd_evt_work_order_dtl:"工单",job_dwd_evt_opinion_dtl:"意见反馈",job_dwd_evt_consult_dtl:"咨询",job_dwd_evt_questionnaire_dtl:"问卷"}}}},d=o,c=a(1001),h=(0,c.Z)(d,n,l,!1,null,"11b1d2fc",null),m=h.exports,u=a(7141),p=a(6427),y={components:{keyWords:i.Z,textInfos:m,textDetailForm:u.Z},props:{chartId:{type:String,default:""},data:{type:Array},indexTypeName:{type:String,default:""},remarkData:{type:Object,default(){return{}}},titleTop:{type:String,default:()=>"原文详情"},needDownLoad:{type:Boolean,default:()=>!1}},computed:{computeTitle(){return function(e,t){var a=p.Z.summary,s=this.typeData[e];return a[s][t]}}},data(){return{textDetailShow:!1,textDetailShowInner:!1,detailData:{},textDetailFormData:{},textDetailFormTableType:"",typeData:{"引力域-资讯":"post_comments","引力域-帖子":"post_comments","联络中心热线服务":"work_order","维修三包":"work_order","口碑描述":"feedback","车机端-智慧小安":"consulting_service","联络中心在线对话":"consulting_service",GQRS:"questionnaire","口碑评分":"questionnaire","爱卡汽车-用户发帖":"post_comments","百度贴吧-用户发帖":"post_comments","懂车帝-用户发帖":"post_comments","汽车之家-用户发帖":"post_comments","太平洋汽车-用户发帖":"post_comments","新浪微博-用户发帖":"post_comments","易车网-用户发帖":"post_comments","长安汽车直评":"questionnaire","集团智慧营销直评":"questionnaire","凯程汽车直评":"questionnaire","欧尚汽车直评":"questionnaire","新能源汽车直评":"questionnaire","incallAPP-小安吐槽":"feedback","爱卡汽车-口碑描述":"feedback","汽车之家-口碑描述":"feedback","太平洋汽车-口碑描述":"feedback","易车网-口碑描述":"feedback","懂车帝-口碑描述":"feedback","爱卡汽车-口碑评分":"questionnaire","汽车之家-口碑评分":"questionnaire","太平洋汽车-口碑评分":"questionnaire","易车网-口碑评分":"questionnaire","车质网-投诉":"feedback","长安百科-视频":"post_comments","引力域-意见反馈":"feedback","体验官-活动":"post_comments","乘用车服务号":"post_comments","车机端-意见反馈":"feedback","长安百科-问答":"post_comments","体验官-帖子":"post_comments","体验官-建议":"feedback","短信回复":"feedback","联络中心留言板":"feedback","长安商城评价":"feedback","线下问卷导入":"questionnaire","懂车帝-口碑评分":"questionnaire","深蓝汽车联络中心":"work_order","阿维塔联络中心":"work_order"}}},watch:{textDetailShow(e){0==e?setTimeout((()=>{this.textDetailShowInner=!1}),500):this.textDetailShowInner=!0}},methods:{sizeChangeHandle(e){this.$emit("textDetailsChange",{pageSize:e,pageNum:1})},currentChangeHandle(e){this.$emit("textDetailsChange",{pageNum:e})},seeDetail(e){var t=e.jobName.split("_");t=t.slice(3),this.textDetailFormTableType=this.typeData[e.dataSourceName],this.textDetailShow=!0,this.textDetailFormData=e},closeDialogHandle(){this.textDetailShow=!1},seeUserDetail(e){e.oneId&&this.$emit("seeUserDetail",{oneId:e.oneId,userName:e.name||e.user})},downloadHandle(){this.$emit("download")}}},g=y,v=(0,c.Z)(g,s,r,!1,null,"4f6e5afe",null),f=v.exports},3242:function(e,t,a){"use strict";a.d(t,{Z:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.title?a("chart-box",e._b({attrs:{chartId:e.chartId,downloadChart:!0,title:e.title,loading:e.loading,data:[!0]},on:{download:e.downLoadHandle}},"chart-box",e.$attrs,!1),[a("div",{staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[a("div",{staticClass:"select-area p-t-20"},[a("div",{staticClass:"public-float-left public-inline-block"},[a("span",{staticClass:"select-name m-r-10"},[e._v("情感筛选")]),a("el-select",{attrs:{size:"small",placeholder:"请选择",width:"150"},on:{change:e.selectFeelTagChange},model:{value:e.remarkData.selectFeelTag,callback:function(t){e.$set(e.remarkData,"selectFeelTag",t)},expression:"remarkData.selectFeelTag"}},e._l(e.feelTagOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1)],1),a("div",{staticClass:"public-float-right public-inline-block"},[a("el-select",{staticClass:"m-r-10",attrs:{size:"small",placeholder:"请选择"},model:{value:e.selectPageNum,callback:function(t){e.selectPageNum=t},expression:"selectPageNum"}},e._l(e.pageNumOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1),a("el-select",{attrs:{size:"small",placeholder:"请选择排序方式"},on:{change:e.selectSortTypeChange},model:{value:e.remarkData.selectSortType,callback:function(t){e.$set(e.remarkData,"selectSortType",t)},expression:"remarkData.selectSortType"}},e._l(e.sortTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1)],1),a("div",{staticClass:"public-clear"})]),a("div",{staticClass:"el-table-border"},[a("el-table",{ref:"table",staticClass:"m-t-16",staticStyle:{width:"100%"},attrs:{data:e.showTableData}},[a("el-table-column",{attrs:{type:"index",width:"80",label:"排名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"index"},[e._v(e._s(t.$index+1))])]}}],null,!1,1143523642)}),a("el-table-column",{attrs:{prop:"keyword",label:"标准关键词",width:"470"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"standardKeyword",on:{click:function(a){return e.standardKeywordDetail(t.row.keyWord||t.row.keyword)}}},[e._v(e._s(t.row.keyWord||t.row.keyword))])]}}],null,!1,2164073970)}),a("el-table-column",{attrs:{prop:"emotionAttribute",label:"情感"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("feel-tag",{attrs:{data:e.row.emotionAttribute}})]}}],null,!1,3150733675)}),a("el-table-column",{scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"standardKeywordMentionValue"},[e._v(e._s(e.$publicHandle.makeDataUnit(t.row[e.totalMenKey])))]),t.row[e.momTotalMenKey]>0?a("div",{staticClass:"remark"},[e._v("+"+e._s(e.$publicHandle.makeDataUnit(t.row[e.momTotalMenKey])))]):t.row[e.momTotalMenKey]<=0?a("div",{staticClass:"remark"},[e._v("-"+e._s(e.$publicHandle.makeDataUnit(t.row[e.momTotalMenKey])))]):a("div",{staticClass:"remark"},[e._v("-")])]}}],null,!1,1647349747)},[a("template",{slot:"header"},[a("div",[e._v("提及量")]),a("div",{staticClass:"table-head-remark"},[e._v("提及量变化")])])],2),a("el-table-column",{attrs:{prop:"momTotalMentionValueRate",label:"提及量环比"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("show-compare",{attrs:{customClass:"show-compare-inner",compareKey:"momTotalMentionValueRate",compareValue:t.row[e.momTotalMenRateKey]}})]}}],null,!1,3379869939)}),a("el-table-column",{attrs:{prop:"mentionRate",label:"提及率"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.$publicHandle.formatPercent(t.row.mentionRate))+"%")]}}],null,!1,2411059603)})],1)],1)])]):a("div",{staticClass:"public-white chart-box p-20 m-t-20 public-border-radius"},[a("div",{staticClass:"select-area p-t-10"},[a("div",{staticClass:"public-float-left public-inline-block"},[a("span",{staticClass:"select-name m-r-10"},[e._v("情感筛选")]),a("el-select",{attrs:{size:"small",placeholder:"请选择"},on:{change:e.selectFeelTagChange},model:{value:e.remarkData.selectFeelTag,callback:function(t){e.$set(e.remarkData,"selectFeelTag",t)},expression:"remarkData.selectFeelTag"}},e._l(e.feelTagOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1)],1),a("div",{staticClass:"public-float-right public-inline-block"},[a("el-select",{staticClass:"m-r-10",attrs:{size:"small",placeholder:"请选择"},model:{value:e.selectPageNum,callback:function(t){e.selectPageNum=t},expression:"selectPageNum"}},e._l(e.pageNumOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1),a("el-select",{attrs:{size:"small",placeholder:"请选择"},on:{change:e.selectSortTypeChange},model:{value:e.remarkData.selectSortType,callback:function(t){e.$set(e.remarkData,"selectSortType",t)},expression:"remarkData.selectSortType"}},e._l(e.sortTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1)],1),a("div",{staticClass:"public-clear"})]),a("div",{staticClass:"el-table-border"},[a("el-table",{staticClass:"m-t-20",staticStyle:{width:"100%"},attrs:{data:e.showTableData}},[a("el-table-column",{attrs:{type:"index",width:"80",label:"排名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"index"},[e._v(e._s(t.$index+1))])]}}])}),a("el-table-column",{attrs:{prop:"keyword",label:"标准关键词",width:"470"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"standardKeyword",on:{click:function(a){return e.standardKeywordDetail(t.row.keyWord||t.row.keyword)}}},[e._v(e._s(t.row.keyWord||t.row.keyword))])]}}])}),a("el-table-column",{attrs:{prop:"emotionAttribute",label:"情感"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("feel-tag",{attrs:{data:e.row.emotionAttribute}})]}}])}),a("el-table-column",{scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"standardKeywordMentionValue"},[e._v(e._s(e.$publicHandle.makeDataUnit(t.row[e.totalMenKey])))]),t.row[e.momTotalMenKey]>0?a("div",{staticClass:"remark"},[e._v("+"+e._s(e.$publicHandle.makeDataUnit(t.row[e.momTotalMenKey])))]):t.row[e.momTotalMenKey]<=0?a("div",{staticClass:"remark"},[e._v("-"+e._s(e.$publicHandle.makeDataUnit(t.row[e.momTotalMenKey])))]):a("div",{staticClass:"remark"},[e._v("-")])]}}])},[a("template",{slot:"header"},[a("div",[e._v("提及量")]),a("div",{staticClass:"table-head-remark"},[e._v("提及量变化")])])],2),a("el-table-column",{attrs:{prop:"momTotalMentionValueRate",label:"提及量环比"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("show-compare",{attrs:{customClass:"show-compare-inner",compareKey:"momTotalMentionValueRate",compareValue:t.row[e.momTotalMenRateKey]}})]}}])}),a("el-table-column",{attrs:{prop:"mentionRate",label:"提及率"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e.$publicHandle.formatPercent(t.row.mentionRate))+"%")]}}])})],1)],1)])],1)},r=[],i=a(3346),n=a.n(i),l={mixins:[n()],props:{chartId:{type:String,default:""},title:{type:String,default:""},data:{type:[Array,Object],default(){return[]}},nowIndexLevel:{type:Array,default(){return[]}},showTitle:{type:Boolean,default:!1},attrName:{type:String,default:""},loading:{type:Boolean},totalMenKey:{type:String,default:"totalMentionValue"},momTotalMenKey:{type:String,default:"momTotalMentionValue"},momTotalMenRateKey:{type:String,default:"momTotalMentionValueRate"},remarkData:{type:Object}},computed:{showTableData(){return this.data.slice(0,this.selectPageNum)}},data(){return{selectPageNum:10}},methods:{downLoadHandle(e){this.$emit("download",e,"topQuestion",this.data)},standardKeywordDetail(e){this.$emit("standardKeywordDetail",e)},selectFeelTagChange(e){this.$emit("requestChange",{selectFeelTag:e,selectSortType:this.remarkData.selectSortType})},selectSortTypeChange(e){this.$emit("requestChange",{selectFeelTag:this.remarkData.selectFeelTag,selectSortType:e})}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,"6a6b8262",null),h=c.exports},9654:function(e,t,a){"use strict";a.d(t,{Z:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("chart-box",e._b({attrs:{chartId:e.chartId,title:e.title,loading:e.loading,data:e.chartData},on:{download:e.downloadHandle}},"chart-box",e.$attrs,!1),[a("div",{staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[e.remarkData.nowIndexName?a("div",{staticClass:"index-title"},[a("span",{staticClass:"now-index"},[e._v("【"+e._s(e.remarkData.nowIndexName.join(">"))+"】")]),a("span",[e._v(e._s(e.attrName))])]):e._e(),a("div",{staticClass:"side-tips"},[a("experience-tips",{attrs:{keys:e.experienceKeys,data:e.remarkData}})],1),a("div",{staticClass:"side-chart"},[a("bar-and-point-chart",{ref:"chart",attrs:{needLinkage:!1,tooltipFormatter:e.tooltipFormatter,isTwoYaxis:!0,yAxisName:e.yAxisName,data:e.chartData,divId:e.preDivId+"vocExperience"},on:{seeDetail:e.seeDetailHandle}})],1),a("div",{staticClass:"public-clear"})])])},r=[],i=a(5446),n=a(8483),l={components:{barAndPointChart:i.Z,experienceTips:n.Z},props:{chartId:{type:String,default:""},data:{type:[Array,Object],default(){return[]}},loading:{type:Boolean,default:!1},remarkData:{type:[Array,Object],default(){return[]}},attr:{type:String,default:""},attrName:{type:String,default:""},title:{type:String,default:""},preDivId:{type:String},needDetails:{type:Boolean,default:!0}},data(){return{nowIndexLevel:["全旅程"],chartData:{}}},computed:{yAxisName(){return["提及量",this.attrName]},experienceKeys(){return[this.attr,"totalMentionValue"].join(",")}},watch:{data(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.drawChart()}},mounted(){this.drawChart()},methods:{tooltipFormatter(e){var t=[this.attrName,"总提及量","正面提及量","中性提及量","负面提及量"],a=[],s="";let r=0;for(var i='<div class="public-tooltip-div">',n=0;n<e.length;n++){s='<div class="axis-name">'+e[n].axisValueLabel+"</div>";var l=t.indexOf(e[n].seriesName);a[l]='<div class="each-series"><span class="each-series-name">'+e[n].marker+e[n].seriesName+"：</span>";var o="体验值"==e[n].seriesName?this.$publicHandle.formatNum(e[n].value):"负面提及率"==e[n].seriesName?this.$publicHandle.formatPercent(e[n].value[1])+"%":this.$publicHandle.makeDataUnit(e[n].value);r+="正面提及量"==e[n].seriesName||"中性提及量"==e[n].seriesName||"负面提及量"==e[n].seriesName?Math.abs(e[n].value):0,a[l]+='<span class="each-series-value">'+o+"</span></div>"}return i+=s+a.join(""),i+='<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span><span class="each-series-value">'+this.$publicHandle.makeDataUnit(r)+"</span></div>",i+="</div>",this.needDetails&&(i+='<div class="public-tooltip-click-tips">点击柱子可查看分析</div>'),i},seeDetailHandle(e){this.$emit("seeDetail",e)},drawChart(){for(var e=JSON.parse(JSON.stringify(this.data)),t=0;t<e.length;t++)e[t].negativeMentionValue=e[t].negativeMentionValue?-e[t].negativeMentionValue:0;this.chartData={data:e,xDataKey:"keyWord",seriesDataKey:[{name:"中性提及量",key:"neutralMentionValue",type:"bar",stack:"total",color:"#0C92E0"},{name:"正面提及量",key:"positiveMentionValue",type:"bar",stack:"total",color:"#3ED4A9"},{name:"负面提及量",key:"negativeMentionValue",type:"bar",stack:"total",color:"#FF4A4D"},{name:this.attrName,key:this.attr,type:"scatter",yAxisIndex:1,color:"#5D7092"}]}},downloadHandle(e){this.$emit("download",e,"vocExperience")}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,"21effabd",null),h=c.exports},570:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("chart-box",e._b({attrs:{chartId:e.chartId,title:e.title,loading:e.loading,data:[!0]},on:{download:e.downloadHandle}},"chart-box",e.$attrs,!1),[a("div",{attrs:{slot:"content"},slot:"content"},[e.showTimeSelect?a("el-radio-group",{staticClass:"m-t-15",attrs:{size:"small"},model:{value:e.selectDateType,callback:function(t){e.selectDateType=t},expression:"selectDateType"}},e._l(e.dateTypes,(function(t,s){return a("el-radio-button",{key:s,attrs:{label:t.id}},[e._v(e._s(t.name))])})),1):e._e(),a("div",{staticClass:"public-chart-content m-t-15"},[a("bar-or-line-chart",{ref:"chart",attrs:{tooltipFormatter:e.tooltipFormatter,isTwoYaxis:!0,yAxisName:e.yAxisName,data:e.chartData,divId:e.preDivId+"vocTrend"},on:{seeDetail:e.seeDetail}})],1)],1)])},r=[],i=a(7371),n={components:{barOrLineChart:i.Z},props:{chartId:{type:String,default:""},data:{type:[Array,Object],default(){return[]}},loading:{type:Boolean},attr:{type:String,default:""},attrName:{type:String,default:""},remarkData:{type:Object,default(){return{}}},preDivId:{type:String},title:{type:String,default:"VOC体验值-趋势"},yAxisName:{type:Array,default(){return["提及量","体验值"]}},needDetails:{type:Boolean,default:!1},showTimeSelect:{type:Boolean,default:!0},dateTypes:{type:Array,default:()=>[{id:"day",name:"日"},{id:"month",name:"月"}]}},data(){return{chartData:{}}},computed:{selectDateType:{get(){return this.remarkData.type},set(e){this.$emit("changeVocTrendType",e)}}},watch:{data(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.drawChart()}},mounted(){this.drawChart()},methods:{seeDetail(e){this.$emit("vocTrendSeeDetail",e)},tooltipFormatter(e){e=JSON.parse(JSON.stringify(e));var t=["正面提及量","中性提及量","负面提及量"];this.attrName&&t.unshift(this.attrName);var a=[];let s=0;for(var r="",i='<div class="public-tooltip-div">',n=0;n<e.length;n++){r='<div class="axis-name">'+e[n].axisValueLabel+"</div>";var l=t.indexOf(e[n].seriesName);a[l]='<div class="each-series"><span class="each-series-name">'+e[n].marker+e[n].seriesName+"：</span>";var o="体验值"==e[n].seriesName?this.$publicHandle.formatNum(e[n].value):"负面提及率"==e[n].seriesName?this.$publicHandle.formatPercent(e[n].value)+"%":this.$publicHandle.makeDataUnit(e[n].value);s+="正面提及量"==e[n].seriesName||"中性提及量"==e[n].seriesName||"负面提及量"==e[n].seriesName?Math.abs(e[n].value):0,a[l]+='<span class="each-series-value">'+o+"</span></div>"}return i+=r+a.join(""),i+='<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span><span class="each-series-value">'+this.$publicHandle.makeDataUnit(s)+"</span></div></div>",1==this.needDetails&&(i+='<div class="public-tooltip-click-tips">点击图形可查看分析</div>'),i},drawChart(){for(var e=JSON.parse(JSON.stringify(this.data)),t=0;t<e.length;t++)e[t].negativeMentionValue=e[t].negativeMentionValue?-e[t].negativeMentionValue:0;this.chartData={data:e,xDataKey:"keyWord",seriesDataKey:[{name:"中性提及量",key:"neutralMentionValue",type:"bar",stack:"total",color:"#0C92E0"},{name:"正面提及量",key:"positiveMentionValue",type:"bar",stack:"total",color:"#3ED4A9"},{name:"负面提及量",key:"negativeMentionValue",type:"bar",stack:"total",color:"#FF4A4D"},{name:this.attrName,key:this.attr,type:"line",yAxisIndex:1,color:"#5D7092",smooth:!0}]}},downloadHandle(e){this.$emit("download",e,"vocTrend")}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"2c54f0bb",null),c=d.exports},4261:function(e,t,a){"use strict";a.d(t,{Z:function(){return u}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("chart-box",{attrs:{title:"基础数据",data:[!0],tooltipShow:!1,needDownLoad:!1,loading:e.tableLoading}},[a("div",{staticClass:"p-20",attrs:{slot:"content"},slot:"content"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{align:"center",prop:"keyWordDate",label:"时间"}}),a("el-table-column",{attrs:{align:"center",prop:"keyWordMentionValue",label:"提及量"}}),a("el-table-column",{attrs:{align:"center",prop:"momKeyWordMentionValue",label:"提及量变化"}}),a("el-table-column",{attrs:{align:"center",prop:"momKeyWordMentionRate",label:"提及量环比"}})],1)],1)]),a("voc-trend",{ref:"trend",attrs:{tooltipShow:!1,remarkData:e.trendRemarkData,needDetail:!1,yAxisName:e.yAxisName,title:"提及量趋势",loading:e.vocTrendLoading,data:e.vocTrendData},on:{vocTrendSeeDetail:e.vocTrendSeeDetailHandle,changeVocTrendType:e.changeVocTrendTypeHandle,download:e.trendDownloadHandle}}),a("div",[a("chart-box",{staticClass:"left",attrs:{title:"来源分析",tooltipShow:!1,data:[!0],loading:e.sourceAnalysis.loading},on:{download:e.sourceAnalysisDownload}},[a("pie-chart",e._b({ref:"sourceAnalysis",attrs:{slot:"content",divId:"sourceAnalysisChar",total:e.sourceAnalysis.remarkData.total,showLabel:!0,pieChartTextShow:!1,showLegend:!0,radius:e.sourceAnalysisRadius,data:e.sourceAnalysis},on:{seeDetail:e.datasourceSeeDetail},slot:"content"},"pie-chart",e.$attrs,!1))],1),a("chart-box",{staticClass:"right",attrs:{title:"集中车系",tooltipShow:!1,data:[!0],loading:e.topQuestion.loading},on:{download:e.focusChartDownload}},[a("bar-or-line-chart",{ref:"topQuestion",attrs:{slot:"content",isShadowClick:!1,tooltipFormatter:e.topTooltipFormatter,divId:"topQuestionChar",axisLabelColor:"#0077FF",transverse:!0,data:e.topQuestion},on:{seeDetail:e.focusSeeDetail},slot:"content"})],1),a("div",{staticClass:"public-clear"})],1),a("text-details",{attrs:{indexTypeName:e.sendData.indexTypeName||"",tooltipShow:!1,remarkData:e.textDetailRemarkData,data:e.textDetailData,loading:e.textDetailsLoading},on:{seeUserDetail:e.seeUserDetailHandle,textDetailsChange:e.textDetailsChangeHandle}})],1)},r=[],i=a(570),n=a(5292),l=a(7371),o=a(6943),d={components:{vocTrend:i.Z,textDetails:n.Z,barOrLineChart:l.Z,pieChart:o.Z},props:{imComeSendData:{type:Object,default(){return{}}}},data(){return{sourceAnalysisDownloadData:[],tableLoading:!1,tableData:[],trendRemarkData:{type:"day"},vocTrendLoading:!1,textDetailsLoading:!1,sourceChartLoading:!1,focusChartLoading:!1,sendData:{},vocTrendData:[],textDetailPage:{pageNum:1,pageSize:10},textDetailData:[],textDetailRemarkData:{},yAxisName:["提及量"],topQuestion:{loading:!1,data:[],xDataKey:"name",seriesDataKey:[{name:"提及量",key:"value",type:"bar"}]},sourceAnalysisRadius:[["43%","57%"]],sourceAnalysis:{loading:!1,data:[[{name:"维修三包",value:122},{name:"车机端-意见反馈",value:28}]],remarkData:{total:0}}}},mounted(){var e=this.imComeSendData,t=e.index;for(var a in delete e.index,t)e[a]=t[a];this.sendData=e,this.getKeyWordValue(),this.getVocTrendData(),this.getTextDetails(),this.getDetailStKeywordTopSeries(),this.getDetailStKeywordDataSource()},methods:{vocTrendSeeDetailHandle(e){var t=JSON.parse(JSON.stringify(this.imComeSendData));t["startDate"]=e.name,t["endDate"]=e.name,t["dateType"]=this.trendRemarkData.type||"day";var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"keywordDetailsDrawer",sendData:t,showSendData:a,head:"标准关键词详情",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},datasourceSeeDetail(e){var t=JSON.parse(JSON.stringify(this.imComeSendData));t["dataSources"]=[e.name];var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"keywordDetailsDrawer",sendData:t,showSendData:a,head:"标准关键词详情",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},focusSeeDetail(e){var t=JSON.parse(JSON.stringify(this.imComeSendData));t["seriesNames"]=[e.name];var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"keywordDetailsDrawer",sendData:t,showSendData:a,head:"标准关键词详情",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},topTooltipFormatter(e){for(var t='<div class="public-tooltip-div">',a=[],s="",r=0;r<e.length;r++)s='<div class="axis-name">'+e[r].name+"</div>",a.push('<div class="each-series"><span class="each-series-name">'+e[r].marker+e[r].seriesName+"：</span>"+this.$publicHandle.makeDataUnit(e[r].value)+"</div>");return t=t+s+a.join("")+"</div>",t},sourceAnalysisDownload(e){if("detail"==e){var t=["数据来源","提及量","占比"],a=["name","value","rate"];this.$publicHandle.downloadExcel(this.sourceAnalysisDownloadData,t,a,this.imComeSendData.standardKeyword+"提及量明细")}else"clientDetail"==e&&this.$publicHandle.linkClientDetail(this.sendData,this)},focusChartDownload(e){if("detail"==e){var t=["数据来源","提及量"],a=["name","value"];this.$publicHandle.downloadExcel(this.topQuestion.data,t,a,this.imComeSendData.standardKeyword+"提及量明细")}else"clientDetail"==e&&this.$publicHandle.linkClientDetail(this.sendData,this)},seeUserDetailHandle(e){var t={userId:e.oneId},a={userName:e.userName},s=this.$publicHandle.makeShowSendData(a,this),r={show:!0,component:"textDetailsDrawer",sendData:t,showSendData:s,head:"原文明细",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",r)},async getKeyWordValue(){this.tableLoading=!0;try{const r=await this.$store.dispatch("detailStKeywordDetail",this.sendData);var e=r.startDate+"~"+r.endDate,t=this.$publicHandle.makeDataUnit(r.currentMentionValue),a=(r.momTotalMentionValue>=0?"+":"-")+this.$publicHandle.makeDataUnit(r.momTotalMentionValue+""),s=r.momTotalMentionValueRate+"%";this.tableData=[{keyWordDate:e,keyWordMentionValue:t,momKeyWordMentionValue:a,momKeyWordMentionRate:s}]}catch(r){console.log(r),this.tableData=[]}this.tableLoading=!1},async getDetailStKeywordTopSeries(){this.topQuestion.loading=!0;let e=await this.$store.dispatch("detailStKeywordTopSeries",this.sendData);this.$set(this.topQuestion,"data",e),this.topQuestion.loading=!1},async getDetailStKeywordDataSource(){this.$set(this.sourceAnalysis,"loading",!0);let e=await this.$store.dispatch("detailStKeywordDataSource",this.sendData);e.forEach((e=>{this.sourceAnalysis.remarkData.total+=e["value"],e.rate=e.rate+"%"})),this.sourceAnalysisDownloadData=e,this.$set(this.sourceAnalysis,"data",[e]),this.$set(this.sourceAnalysis,"loading",!1)},async getVocTrendData(e){this.vocTrendLoading=!0;try{var t=JSON.parse(JSON.stringify(this.sendData));e||(this.trendRemarkData.type=this.$publicHandle.checkTimeTooLong(t.startDate,t.endDate,this)),t["dateType"]=this.trendRemarkData.type;const a=await this.$store.dispatch("attributeAnalysisVocExperienceTrend",t);this.vocTrendData=a}catch(a){this.vocTrendData=[],console.log(a)}this.vocTrendLoading=!1},changeVocTrendTypeHandle(e){this.trendRemarkData.type=e,this.getVocTrendData(!0)},async getTextDetails(){this.textDetailsLoading=!0;var e=JSON.parse(JSON.stringify(this.sendData));for(var t in this.textDetailPage)e[t]=this.textDetailPage[t];try{const t=await this.$store.dispatch("attributeAnalysisOriginalDetails",e)||{};this.textDetailData=t.list||[],this.textDetailRemarkData=t||{}}catch(a){this.textDetailData=[],this.textDetailRemarkData={},console.log(a)}this.textDetailsLoading=!1},textDetailsChangeHandle(e){for(var t in e)this.textDetailPage[t]=e[t];this.getTextDetails()},trendDownloadHandle(e){if("detail"==e){var t=["日期","正面提及量","中性提及量","负面提及量"],a=["dataDate","positiveMentionValue","neutralMentionValue","negativeMentionValue"];this.$publicHandle.downloadExcel(this.vocTrendData,t,a,this.imComeSendData.standardKeyword+"提及量明细")}else"clientDetail"==e&&this.$publicHandle.linkClientDetail(this.sendData,this)}}},c=d,h=a(1001),m=(0,h.Z)(c,s,r,!1,null,"7e47bb6a",null),u=m.exports},661:function(e,t,a){"use strict";a.d(t,{Z:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("text-details",{attrs:{indexTypeName:e.sendData.indexTypeName||"",remarkData:e.textDetailRemarkData,data:e.textDetailData,loading:e.textDetailsLoading},on:{textDetailsChange:e.textDetailsChangeHandle}})],1)},r=[],i=a(570),n=a(5292),l={components:{vocTrend:i.Z,textDetails:n.Z},props:{imComeSendData:{type:Object,default(){return{}}}},data(){return{vocTrendLoading:!1,textDetailsLoading:!1,sendData:{},vocTrendData:[],textDetailPage:{pageNum:1,pageSize:10},textDetailData:[],textDetailRemarkData:{},yAxisName:["提及量"]}},mounted(){var e=this.imComeSendData,t=e.index;for(var a in delete e.index,t)e[a]=t[a];this.sendData=e,this.getTextDetails()},methods:{async getVocTrendData(){this.vocTrendLoading=!0;try{const e=await this.$store.dispatch("attributeAnalysisVocExperienceTrend",this.sendData);this.vocTrendData=e}catch(e){this.vocTrendData=[],console.log(e)}this.vocTrendLoading=!1},async getTextDetails(){this.textDetailsLoading=!0;var e=JSON.parse(JSON.stringify(this.sendData));for(var t in this.textDetailPage)e[t]=this.textDetailPage[t];try{const t=await this.$store.dispatch("attributeAnalysisOriginalDetails",e)||{};this.textDetailData=t.list||[],this.textDetailRemarkData=t||{}}catch(a){this.textDetailData=[],this.textDetailRemarkData={},console.log(a)}this.textDetailsLoading=!1},textDetailsChangeHandle(e){for(var t in e)this.textDetailPage[t]=e[t];this.getTextDetails()},trendDownloadHandle(e){if("chart"==e)this.$refs.trend.$refs.chart.downloadChart(this.imComeSendData.standardKeyword+"提及量趋势");else if("detail"==e){var t=["日期","正面提及量","中性提及量","负面提及量"],a=["dataDate","positiveMentionValue","neutralMentionValue","negativeMentionValue"];this.$publicHandle.downloadExcel(this.vocTrendData,t,a,this.imComeSendData.standardKeyword+"提及量明细")}}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,null,null),h=c.exports},3691:function(e,t,a){"use strict";a.d(t,{Z:function(){return F}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"m-t-20"},[a("div",{staticClass:"module-tabs"},[e._l(e.showModuleTabs,(function(t,s){return a("div",{key:s,staticClass:"each-module-tab p-t-8 p-b-8 m-r-10",class:e.nowModule==t.name?"active":"",on:{click:function(a){return e.changNowModule(t.name)}}},[e._v(e._s(t.name))])})),e.isAsComponent?e._e():a("el-button",{staticClass:"float-right",attrs:{type:"primary",plain:"",size:"small",icon:"el-icon-plus"},on:{click:e.addTask}},[e._v("新增任务")]),e._l(e.showModuleTabs[e.nowModule]["components"],(function(t,s){return a(t.component,e._b({key:s,tag:"component",staticStyle:{"margin-top":"12px"},attrs:{showTitle:!0,attr:e.attr,attrName:e.attrName,indexTypeName:e.sendData.indexTypeName||"",remarkData:e.data[t.component]["remarkData"],remarkData2:e.data[t.component]["remarkData2"],remarkData3:e.data[t.component]["remarkData3"],data:e.data[t.component]["data"],chartId:t[e.pageType+"_chartId"],loading:e.data[t.component]["loading"],title:e.specialTitle(t.component),preDivId:"analysis"+(new Date).getTime(),yAxisName:e.analysisXAxisName,needDetails:!0},on:{seeIndexDetail:e.seeIndexDetail,seeAreaDetail:e.seeAreaDetail,datasourceSeeDetail:e.datasourceSeeDetail,seeDetail:e.seeDetailHandle,vocTrendSeeDetail:e.vocTrendSeeDetail,changeVocTrendType:e.changeVocTrendTypeHandle,changeDatasource:e.changeDatasourceHandle,textDetailsChange:e.textDetailsChange,standardKeywordDetail:e.standardKeywordDetailHandle,download:e.downloadHandle,wordCloudChartClick:e.wordCloudChartHandle,requestChange:e.requestChange,seeUserDetail:e.seeUserDetailHandle}},"component",e.$attrs,!1))}))],2),e.dialogVisible?a("dialog-form",{attrs:{dialogVisible:e.dialogVisible,taskPath:e.taskPath,detailMsg:e.sendData,taskType:e.taskType},on:{close:e.closeDialogTask}}):e._e()],1)},r=[],i=a(570),n=a(9654),l=a(3242),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("chart-box",{attrs:{chartId:e.chartId,title:"数据来源",data:e.data},on:{download:e.downloadHandle}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{slot:"content"},slot:"content"},[a("div",{staticClass:"public-chart-content"},[a("barAndPointChart",{attrs:{needLinkage:!0,tooltipFormatter:e.tooltipFormatter,xNameText:e.xNameText,data:e.chartData,divId:e.preDivId+"datasource",isTwoYaxis:!0,yAxisName:e.yAxisName},on:{seeDetail:e.seeDetailHandle}}),a("div",{staticClass:"m-t-20"}),a("public-border-top",[a("div",{staticClass:"chart-title",attrs:{slot:"content"},slot:"content"})]),a("div",{staticClass:"p-l-20 p-r-20 p-b-20"},[a("div",[a("div",{staticClass:"left-chart m-t-10"},[a("div",{staticClass:"chart-title m-t-20"},[a("span",{staticClass:"now-index-name"},[e._v("【"+e._s(e.datasourceName)+"】")]),e._v("- 提及量趋势 ")]),a("bar-or-line-chart",{attrs:{tooltipFormatter:e.trendTooltipFormatter,data:e.trendChartData,divId:e.preDivId+"datasourceAnalysis",yAxisName:e.yAxisName},on:{seeDetail:e.datasourceSeeDetail}})],1),a("div",{staticClass:"left-chart m-t-10"},[a("div",{staticClass:"chart-title m-t-20"},[a("span",{staticClass:"now-index-name"},[e._v("【"+e._s(e.datasourceName)+"】")]),e._v("- 词云图 ")]),a("word-cloud-chart",{attrs:{data:e.wordCloudData,divId:e.preDivId+"datasourceWordChart"},on:{click:e.wordCloudChartClick}})],1),a("div",{staticClass:"public-clear"})])])],1)])])},d=[],c=a(5446),h=a(7371),m=a(5487),u={components:{barAndPointChart:c.Z,barOrLineChart:h.Z,wordCloudChart:m.Z},data(){return{chartData:{},trendChartData:{},selectDateType:"day",dateTypes:[{id:"day",name:"日"},{id:"month",name:"月"}],wordCloudData:[],yAxisName:["提及量"],datasourceName:"",xNameText:""}},props:{chartId:{type:String,default:""},loading:{type:Boolean,default:!1},data:{type:[Array,Object],default(){return[]}},remarkData:{type:Object,default(){return{}}},attr:{type:String,default:""},attrName:{type:String,default:""},preDivId:{type:String}},watch:{data:{immediate:!0,handler(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.drawChart()}},"remarkData.trend":{immediate:!0,handler(e,t){this.drawTrendChart()}},"remarkData.wordCloud":{immediate:!0,handler(e,t){this.drawWordChart()}}},methods:{datasourceSeeDetail(e){this.$emit("datasourceSeeDetail",{dataSources:[this.datasourceName],startDate:e.name,endDate:e.name})},wordCloudChartClick(e){this.$emit("wordCloudChartClick",{dataSources:[this.datasourceName],name:e.name})},trendTooltipFormatter(e){var t=["体验值","正面提及量","中性提及量","负面提及量"],a=[],s="";let r=0;for(var i='<div class="public-tooltip-div">',n=0;n<e.length;n++){s='<div class="axis-name">'+e[n].axisValueLabel+"</div>";var l=t.indexOf(e[n].seriesName);a[l]='<div class="each-series"><span class="each-series-name">'+e[n].marker+e[n].seriesName+"：</span>";var o="体验值"==e[n].seriesName?this.$publicHandle.formatNum(e[n].value[1]):this.$publicHandle.makeDataUnit(e[n].value);a[l]+='<span class="each-series-value">'+o+"</span></div>",r+="正面提及量"==e[n].seriesName||"中性提及量"==e[n].seriesName||"负面提及量"==e[n].seriesName?Math.abs(e[n].value):0}return i+=s+a.join(""),i+='<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span><span class="each-series-value">'+this.$publicHandle.makeDataUnit(r)+"</span></div>",i+="</div>",i},tooltipFormatter(e){var t=["体验值","正面提及量","中性提及量","负面提及量"],a=[],s="";let r=0;for(var i='<div class="public-tooltip-div">',n=0;n<e.length;n++){s='<div class="axis-name">'+e[n].axisValueLabel+"</div>";var l=t.indexOf(e[n].seriesName);a[l]='<div class="each-series"><span class="each-series-name">'+e[n].marker+e[n].seriesName+"：</span>";var o="体验值"==e[n].seriesName?this.$publicHandle.formatNum(e[n].value):this.$publicHandle.makeDataUnit(e[n].value);a[l]+=o+"</div>",r+="正面提及量"==e[n].seriesName||"中性提及量"==e[n].seriesName||"负面提及量"==e[n].seriesName?Math.abs(e[n].value):0}return i+=s+a.join(""),i+='<div class="each-series"><span class="each-series-name">&nbsp;&nbsp;&nbsp;总提及量：</span>'+this.$publicHandle.makeDataUnit(r)+"</div>",i+="</div>",i},drawChart(){for(var e=JSON.parse(JSON.stringify(this.data)),t=0;t<e.length;t++)e[t].negativeMentionValue=e[t].negativeMentionValue?-e[t].negativeMentionValue:0;e&&Array.isArray(e)&&e.sort(((e,t)=>t.totalMentionValue-e.totalMentionValue)),this.chartData={data:e,xDataKey:"dataSource",seriesDataKey:[{name:"中性提及量",key:"neutralMentionValue",type:"bar",stack:"total",color:"#0C92E0"},{name:"正面提及量",key:"positiveMentionValue",type:"bar",stack:"total",color:"#3ED4A9"},{name:"负面提及量",key:"negativeMentionValue",type:"bar",stack:"total",color:"#FF4A4D"}]};var a={dataSource:"",num:0};if(e&&e.length){for(t=0;t<e.length;t++)e[t].totalMentionValue>a.num&&(a={dataSource:e[t].dataSource,num:e[t].totalMentionValue});this.xNameText=a.dataSource,this.changeDatasourceAnalysis(a.dataSource)}},seeDetailHandle(e){this.changeDatasourceAnalysis(e.name)},changeDatasourceAnalysis(e){this.datasourceName=e,this.$emit("changeDatasource",e)},drawTrendChart(){var e=JSON.parse(JSON.stringify(this.remarkData||{}));e=e.trend||[];for(var t=0;t<e.length;t++)e[t].negativeMentionValue=e[t].negativeMentionValue?-e[t].negativeMentionValue:0;this.trendChartData={data:e,xDataKey:"keyWord",seriesDataKey:[{name:"中性提及量",key:"neutralMentionValue",type:"bar",stack:"total",color:"#0C92E0"},{name:"正面提及量",key:"positiveMentionValue",type:"bar",stack:"total",color:"#3ED4A9"},{name:"负面提及量",key:"negativeMentionValue",type:"bar",stack:"total",color:"#FF4A4D"}]}},drawWordChart(){var e=this.remarkData||{},t=e.wordCloud?JSON.parse(JSON.stringify(e.wordCloud)):[];t=t.sort(((e,t)=>t.value-e.value)),this.wordCloudData=t.slice(0)},changeDateType(e){this.emit("vocTrendDataTypeChange",e)},downloadHandle(e){this.$emit("download",e,"datasourceAnalysis")}}},p=u,y=a(1001),g=(0,y.Z)(p,o,d,!1,null,"b82edec4",null),v=g.exports,f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("chart-box",{attrs:{chartId:e.chartId,title:"区域对比",data:[!0]},on:{download:e.downloadHandle}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[a("div",{staticClass:"select-area p-t-20"},[a("div",{staticClass:"public-float-right public-inline-block"},[a("el-select",{staticClass:"m-r-10",attrs:{size:"small",placeholder:"请选择"},model:{value:e.selectPageNum,callback:function(t){e.selectPageNum=t},expression:"selectPageNum"}},e._l(e.pageNumOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1),a("el-select",{attrs:{size:"small",placeholder:"请选择"},model:{value:e.selectSortType,callback:function(t){e.selectSortType=t},expression:"selectSortType"}},e._l(e.sortTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.key}})})),1)],1),a("div",{staticClass:"public-clear"})]),a("div",{staticClass:"table-area m-t-20"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{align:"center",type:"index",label:"排名",width:"60"}}),a("el-table-column",{attrs:{align:"center",prop:"province",label:"区域"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"standardKeyword",on:{click:function(a){return e.seeAreaDetail(t.row.province)}}},[e._v(e._s(t.row.province))])]}}])}),a("el-table-column",{attrs:{align:"center",prop:"experienceValue",label:"体验值"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$publicHandle.formatNum(t.row.experienceValue))+" ")]}}])}),a("el-table-column",{attrs:{align:"center",prop:"momExperienceValue",label:"体验值环比"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("show-compare",{attrs:{compareKey:"momExperienceValueRate",compareValue:e.row.momExperienceValueRate}})]}}])}),a("el-table-column",{attrs:{align:"center",prop:"totalMentionValue",label:"提及量"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$publicHandle.makeDataUnit(t.row.totalMentionValue))+" ")]}}])}),a("el-table-column",{attrs:{align:"center",prop:"momTotalMentionValue",label:"提及量环比"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("show-compare",{attrs:{compareKey:"momTotalMentionValueRate",compareValue:e.row.momTotalMentionValueRate}})]}}])})],1)],1)])])],1)},b=[],w=a(3346),S=a.n(w),D={mixins:[S()],props:{chartId:{type:String,default:""},loading:{type:Boolean,default:!1},data:{type:Array,default(){return[]}}},data(){return{selectPageNum:10,selectSortType:"experienceValue",sortTypeOptions:[{key:"experienceValue",label:"体验值"},{key:"momExperienceValueRate",label:"体验值环比"},{key:"totalMentionValue",label:"提及量"},{key:"momTotalMentionValueRate",label:"提及量环比"}]}},computed:{tableData(){var e=this.selectSortType,t=this.data.sort(((t,a)=>a[e]-t[e]));return t.slice(0,this.selectPageNum)}},methods:{downloadHandle(e){this.$emit("download",e,"areaAnalysis")},seeAreaDetail(e){this.$emit("seeAreaDetail",e)}}},x=D,k=(0,y.Z)(x,f,b,!1,null,"0bb7dcd7",null),C=k.exports,T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("chart-box",{attrs:{chartId:e.chartId,title:"指标排名",data:[!0],loading:e.loading},on:{download:e.downloadHandle}},[a("div",{staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[a("div",{staticClass:"m-t-10"},[a("topological-graph-chart",{attrs:{maxChildren:e.maxChildren,attrName:e.attrName,attr:e.attr,divid:"indexChart",data:e.remarkChartData}})],1),a("div",{staticClass:"el-table-border"},[a("el-table",{staticClass:"m-t-20",staticStyle:{width:"100%"},attrs:{"default-sort":e.defaultSort,data:e.tableData},on:{"sort-change":e.sortChange}},[a("el-table-column",{attrs:{align:"center",prop:"index",label:"排名（上期）"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.nowIndex)+" ("+e._s(t.row.momIndex)+") ")]}}])}),a("el-table-column",{attrs:{align:"center",prop:"keyWord",label:e.remarkData2},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"standardKeyword",on:{click:function(a){return e.seeIndexDetail(t.row.indexId)}}},[e._v(e._s(t.row.keyWord))])]}}])}),a("el-table-column",{attrs:{align:"center",sortable:"custom",prop:"totalMentionValue",label:"提及量"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$publicHandle.makeDataUnit(t.row.totalMentionValue))+" ")]}}])}),a("el-table-column",{attrs:{align:"center",sortable:"custom",prop:"momTotalMentionValueRate",label:"提及量环比"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("show-compare",{attrs:{compareKey:"momTotalMentionValueRate",compareValue:e.row.momTotalMentionValueRate}})]}}])}),"experienceValue"==e.attr?[a("el-table-column",{attrs:{align:"center",sortable:"custom",prop:"experienceValue",label:"体验值"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$publicHandle.formatNum(t.row.experienceValue))+" ")]}}],null,!1,3569904701)}),a("el-table-column",{attrs:{align:"center",sortable:"custom",prop:"momExperienceValueRate",label:"体验值环比"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("show-compare",{attrs:{compareKey:"momExperienceValueRate",compareValue:e.row.momExperienceValueRate}})]}}],null,!1,2667502506)})]:[a("el-table-column",{attrs:{align:"center",prop:"negativeMentionRate",sortable:"custom",label:"负面提及率"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$publicHandle.formatPercent(t.row.negativeMentionRate))+"% ")]}}])}),a("el-table-column",{attrs:{align:"center",prop:"momNegativeMentionRate",sortable:"custom",label:"负面提及率环比"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("show-compare",{attrs:{compareKey:"momNegativeMentionRate",compareValue:e.row.momNegativeMentionRate}})]}}])})]],2)],1)])])},A=[],_=a(3469),N={components:{topologicalGraphChart:_.Z},props:{chartId:{type:String,default:""},loading:{type:Boolean,default:!1},data:{type:Object,default(){return{}}},remarkData:{type:[Array,Object],default(){return[]}},remarkData2:{type:[Array,Object,String],default(){return[]}},remarkData3:{type:[Array,Object,String],default(){return[]}},attr:{type:String,default:""},attrName:{type:String,default:""}},watch:{data:{immediate:!0,handler(e,t){JSON.stringify(e)!==JSON.stringify(t)&&this.dataHandle()}},remarkData:{immediate:!0,handler(e,t){JSON.stringify(e)!==JSON.stringify(t)&&this.remarkDataHandle()}}},data(){return{sortField:"totalMentionValue",sortType:"desc",tableData:[],remarkChartData:[],defaultSort:{order:"descending",prop:"totalMentionValue"},maxChildren:0}},methods:{seeIndexDetail(e){this.$emit("seeIndexDetail",e)},downloadHandle(e){this.$emit("download",e,"indexAnalysis")},remarkDataHandle(){var e=this.remarkData;this.makeRemarkData(e),this.remarkChartData=JSON.parse(JSON.stringify(this.remarkData))},makeRemarkData(e){for(var t=0;t<e.length;t++)e[t].name=e[t].keyWord,e[t].value=e[t][this.attr],e[t].label={color:"red"},e[t].indexId==this.remarkData3&&(e[t]["itemStyle"]={color:"#FFAA00"}),e[t].children&&(this.maxChildren<e[t].children.length&&(this.maxChildren=e[t].children.length),this.makeRemarkData(e[t].children))},dataHandle(){var e=this.data.detail,t=this.data.detailMom,a={};this.sortData(e),this.sortData(t);for(var s=0;s<t.length;s++){var r=t[s];a[r["keyWord"]]=s+1}for(s=0;s<e.length;s++){var i=e[s];i["nowIndex"]=s+1,i["momIndex"]=void 0!=a[i["keyWord"]]?a[i["keyWord"]]:"-"}this.tableData=e},sortChange(e){const{prop:t,order:a}=e;this.sortType="ascending"==a?"asc":"desc",this.sortField=t,this.dataHandle()},sortData(e){var t=this.sortField;return"desc"==this.sortType?e.sort(((e,a)=>a[t]-e[t])):"asc"==this.sortType&&e.sort(((e,a)=>e[t]-a[t])),e}}},I=N,O=(0,y.Z)(I,T,A,!1,null,"7a375f22",null),R=O.exports,E=a(5292),L=a(8115),M=a(6509),V={components:{vocTrend:i.Z,vocExperience:n.Z,topQuestion:l.Z,datasourceAnalysis:v,areaAnalysis:C,indexAnalysis:R,textDetails:E.Z,populationCharacteristics:L.Z,dialogForm:M.Z},props:{pageType:{type:String,default:""},data:{type:Object,default(){return{}}},remarkData:{type:Object},attr:{type:String,default:""},attrName:{type:String,default:""},sendData:{type:Object,default:()=>{}},isAsComponent:{type:Boolean}},data(){return{nowModule:"概览",moduleTabs:{"概览":{name:"概览",components:[{component:"vocTrend",attribution_analysis_chartId:"bpfx-gyfx-voc-cs",depth_analysis_chartId:"jpfx-sdfx-voc-cs"},{component:"vocExperience",attribution_analysis_chartId:"bpfx-gyfx-voc",depth_analysis_chartId:"jpfx-sdfx-voc"}]},"TOP问题":{name:"TOP问题",components:[{component:"topQuestion",attribution_analysis_chartId:"bpfx-gyfx-top",depth_analysis_chartId:"jpfx-sdfx-top"}]},"人群特征":{name:"人群特征",components:[{component:"populationCharacteristics",attribution_analysis_chartId:"bpfx-gyfx-rqtz",depth_analysis_chartId:""}]},"数据源分析":{name:"数据源分析",components:[{component:"datasourceAnalysis",attribution_analysis_chartId:"bpfx-gyfx-sjyfx",depth_analysis_chartId:"jpfx-sdfx-sjyfx"}]},"地域分析":{name:"地域分析",components:[{component:"areaAnalysis",attribution_analysis_chartId:"bpfx-gyfx-area",depth_analysis_chartId:""}]},"指标分析":{name:"指标分析",components:[{component:"indexAnalysis",attribution_analysis_chartId:"bpfx-gyfx-zbpm",depth_analysis_chartId:"jpfx-sdfx-zbpm"}]},"原文明细":{name:"原文明细",components:[{component:"textDetails",attribution_analysis_chartId:"bpfx-gyfx-ywlb",depth_analysis_chartId:"jpfx-sdfx-ywlb"}]}},dialogVisible:!1,detailMsg:{},taskType:"2",taskPath:""}},computed:{showModuleTabs(){if("attribution_analysis"==this.pageType)return this.moduleTabs;if("depth_analysis"==this.pageType){var e=JSON.parse(JSON.stringify(this.moduleTabs));return delete e["人群特征"],delete e["地域分析"],e}},analysisXAxisName(){return["提及量",this.attrName]}},methods:{seeIndexDetail(e){this.$emit("seeIndexDetail",e)},seeAreaDetail(e){this.$emit("seeAreaDetail",e)},datasourceSeeDetail(e){this.$emit("datasourceSeeDetail",e)},seeDetailHandle(e){this.$emit("seeDetailHandle",e)},vocTrendSeeDetail(e){this.$emit("vocTrendSeeDetailHandle",e)},requestChange(e){this.$emit("requestChange",e)},wordCloudChartHandle(e){this.$emit("wordCloudChartClick",e)},specialTitle(e){return"vocTrend"==e?"VOC"+this.attrName+"趋势":"vocExperience"==e?"VOC"+this.attrName:"topQuestion"==e?"TOP问题":void 0},downloadHandle(e,t,a){this.$emit("download",{command:e,chartType:t,tableData:a})},changNowModule(e){this.nowModule=e},changeVocTrendTypeHandle(e){this.$emit("changeVocTrendType",e)},changeDatasourceHandle(e){this.$emit("changeDatasource",e)},textDetailsChange(e){this.$emit("textDetailsChange",e)},standardKeywordDetailHandle(e){this.$emit("standardKeywordDetail",e)},seeUserDetailHandle(e){this.$emit("seeUserDetail",e)},closeDialogTask(){this.dialogVisible=!1},addTask(){"attribution_analysis"==this.pageType?(this.taskType="2",this.taskPath="/mine_product_analysis/attribution_analysis"):(this.taskType="3",this.taskPath="/competitive_products_analysis/depth_analysis"),this.dialogVisible=!0}}},B=V,$=(0,y.Z)(B,s,r,!1,null,"6e0e2670",null),F=$.exports},6509:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.taskTitle,visible:e.dialogVisible,width:"60%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"任务名称",prop:"taskName"}},[a("el-input",{model:{value:e.ruleForm.taskName,callback:function(t){e.$set(e.ruleForm,"taskName",t)},expression:"ruleForm.taskName"}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"分配人",prop:"allocator"}},[a("el-input",{attrs:{disabled:""},model:{value:e.ruleForm.allocator.value,callback:function(t){e.$set(e.ruleForm.allocator,"value",t)},expression:"ruleForm.allocator.value"}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"任务时间",prop:"realFinishDate",rules:e.dateRule(e.ruleForm)}},[a("div",{staticClass:"flex"},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:e.ruleForm.planBeginDate,callback:function(t){e.$set(e.ruleForm,"planBeginDate",t)},expression:"ruleForm.planBeginDate"}}),e._v(" - "),a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:e.ruleForm.planFinishDate,callback:function(t){e.$set(e.ruleForm,"planFinishDate",t)},expression:"ruleForm.planFinishDate"}})],1)]),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"责任人",prop:"dutyLogin",rules:e.objRules(e.ruleForm.dutyLogin,"请选择责任人")}},[a("voc-autocomplete",{staticClass:"task-select",attrs:{value:e.ruleForm.dutyLogin},on:{handleSelect:e.handleDutyLogin}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"牵头部门",prop:"department",rules:e.objRules(e.ruleForm.department,"请选择牵头部门")}},[a("el-input",{attrs:{placeholder:"由责任人带出",disabled:""},model:{value:e.ruleForm.department.value,callback:function(t){e.$set(e.ruleForm.department,"value",t)},expression:"ruleForm.department.value"}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"确认人",prop:"confirmLogin",rules:e.objRules(e.ruleForm.confirmLogin,"请选择确认人")}},[a("voc-autocomplete",{staticClass:"task-select",attrs:{value:e.ruleForm.confirmLogin},on:{handleSelect:e.handleConfirmLogin}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"关闭要素",prop:"closeCondition"}},[a("el-input",{model:{value:e.ruleForm.closeCondition,callback:function(t){e.$set(e.ruleForm,"closeCondition",t)},expression:"ruleForm.closeCondition"}})],1),a("el-form-item",{staticClass:"demo-form-left",attrs:{label:"支持人",prop:"supportLoginIdList"}},[a("voc-autocomplete",{staticClass:"task-select",attrs:{value:e.ruleForm.supportLoginIdList,multiple:!0},on:{handleSelect:e.handleSupportLoginIdList}})],1),a("div",{staticStyle:{clear:"both"}}),a("el-form-item",{staticStyle:{width:"80%"},attrs:{label:"阅示人",prop:"readLoginIdList"}},[a("voc-autocomplete",{staticClass:"task-select",attrs:{value:e.ruleForm.readLoginIdList,multiple:!0},on:{handleSelect:e.handleReadLoginIdList}})],1),a("el-form-item",{staticStyle:{width:"80%"},attrs:{label:"交付物",prop:"delivery"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.ruleForm.delivery,callback:function(t){e.$set(e.ruleForm,"delivery",t)},expression:"ruleForm.delivery"}})],1),a("el-form-item",{staticStyle:{width:"80%"},attrs:{label:"任务目标",prop:"target"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.ruleForm.target,callback:function(t){e.$set(e.ruleForm,"target",t)},expression:"ruleForm.target"}})],1),a("div",{staticClass:"dot-horizon-solid"}),a("el-form-item",{staticClass:"m-t-20",staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("立即创建")])],1)],1)],1)},r=[],i=(a(1703),a(8632)),n={components:{vocAutocomplete:i.Z},data(){return{ruleForm:{taskName:"",planBeginDate:this.$moment(new Date).format("YYYY-MM-DD"),planFinishDate:"",dutyLogin:{},department:{},confirmLogin:{},closeCondition:"",supportLoginIdList:[],readLoginIdList:[],delivery:"",target:"",taskPath:this.taskPath,allocator:{key:"",value:""}},rules:{taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],closeCondition:[{required:!0,message:"请输入关闭要素",trigger:"blur"}],delivery:[{required:!0,message:"请输入交付物",trigger:"blur"}],target:[{required:!0,message:"请输入任务目标",trigger:"blur"}]}}},props:{dialogVisible:{type:Boolean,default:()=>!1},taskTitle:{type:String,default:"新建任务"},detailMsg:{type:Object,default:()=>({})},subscribeId:{type:[String,Number],default:()=>null},taskType:{type:[String,Number],default:()=>"2"},taskPath:{type:String,default:()=>""}},watch:{taskPath:{handler(e){e||(this.ruleForm.taskPath=this.$route.path)},immediate:!0}},created(){this.init()},methods:{init(){this.getUserByToken()},objRules(e,t){return{type:Object,trigger:["blur","change"],required:!0,validator:(a,s,r)=>e.value?Promise.resolve():Promise.reject(new Error(t))}},dateRule(e){return{trigger:["blur","change"],required:!0,validator:(t,a,s)=>e.planBeginDate?e.planFinishDate?new Date(e.planBeginDate)-new Date(e.planFinishDate)>0?Promise.reject(new Error("开始时间不能大于结束时间")):(this.$set(e,"planFinishDate",this.$moment(e.planFinishDate).format("YYYY-MM-DD")),Promise.resolve()):Promise.reject(new Error("请选择结束时间")):Promise.reject(new Error("请选择开始时间"))}},handleClose(){this.$emit("close")},dateChange(){},save(){this.$refs["ruleForm"].validate((async e=>{if(!e)return console.log("error submit!!"),!1;{let e=JSON.parse(JSON.stringify(this.ruleForm));this.$set(e,"planBeginDate",new Date(e.planBeginDate).getTime()),this.$set(e,"planFinishDate",new Date(e.planFinishDate).getTime()),this.$set(e,"taskType",this.taskType),this.$set(e,"subscribeId",this.subscribeId),e.taskRequestParams=JSON.stringify(this.detailMsg),this.$emit("close");const t=await this.$store.dispatch("taskCreateWithKtm",e);t?this.$message({type:"success",message:"创建成功"}):this.$message.error("创建失败")}}))},async getUserByToken(){const e=await this.$store.dispatch("getUserByToken")||{loginID:"67893",userCode:"向林艳"};this.ruleForm.allocator={key:e.loginID,value:e.userFullName}},handleDutyLogin(e){this.ruleForm.dutyLogin={key:e.loginID,value:e.userFullName};let t=e.title;"string"===typeof t&&(t=JSON.parse(t)),this.ruleForm.department={key:t.deptId,value:t.fullDepartmentName}},handleConfirmLogin(e){this.ruleForm.confirmLogin={key:e.loginID,value:e.userFullName}},handleSupportLoginIdList(e){this.ruleForm.supportLoginIdList=e.map((e=>({key:e.loginID,value:e.userFullName})))},handleReadLoginIdList(e){this.ruleForm.readLoginIdList=e.map((e=>({key:e.loginID,value:e.userFullName})))}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"f9cfacc4",null),c=d.exports},8483:function(e,t,a){"use strict";a.d(t,{Z:function(){return p}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"experience-tips"},e._l(e.showExperienceData,(function(t,s){return a("div",{key:s,staticClass:"each-experience-tip"},[a("div",{staticClass:"each-margin"},[a("div",{staticClass:"left"},[a("img",{attrs:{src:e.imgs[t.key],alt:"",width:"100%"}})]),a("div",{staticClass:"right"},[a("div",{staticClass:"name"},[e._v(e._s(t.name))]),a("div",{staticClass:"data-value"},["totalMentionValue"==t.key?a("span",[e._v(e._s(e.$publicHandle.makeDataUnit(e.data[t.key])))]):"negativeMentionRate"==t.key?a("span",[e._v(e._s(e.$publicHandle.formatPercent(e.data[t.key]))+"%")]):a("span",[e._v(e._s(e.$publicHandle.formatNum(e.data[t.key])))])])]),a("div",{staticClass:"public-clear"})]),a("div",{staticClass:"each-margin"},[a("div",{staticClass:"left"},[a("img",{attrs:{src:e.imgs[t.mom],alt:"",width:"100%"}})]),a("div",{staticClass:"right"},[a("div",{staticClass:"name"},[e._v(e._s(t.name+"环比"))]),a("div",{staticClass:"data-value"},[a("show-compare",{attrs:{customClass:"data-value-inner",compareValue:e.data[t.mom],compareKey:t.mom}})],1)])])])})),0)},r=[],i="data:image/png;base64,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",n="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAACN1JREFUeF7tnX9sldUZxz/PbQsOpCOzLWjGqMSt9JaWmLEs1ijC6pgoEAZ0umVTYQuUUjYdZFmWzGT+MRNRF7j9YSYy8Q+RH26IsilEh7puhhFKS8sqjnWipWvp5trxo6V9z3Lu7e0PuL33/XHetpB7/j7P9zzP9z3nfZ/zPM85rzCK7cvPqowuxa2qlxwFOUqRA0xFmIRiksAkrZ6CToROFJ1AiwiNAo2SQuN44eiR1XJ2tMyQkRz4tp3qMx1tFFmK+QjzgXwU3nQQzS91KN4KCG+lZ3Lwz8VyYaTs8qa8DS2VUlJQyZ29iu8pxXIg3YaYly4dIuxOEbbXlvCOiGiCfWu+EXjPfjX+n008jGKjUszwzYI4wCKcQnhyejbbfr9QuvzQwTiBfct0jaXYANzkh9IuMJsDwqb0TKpML2+jBOaVq0UWbEaR7cJI/0WEpgCsry+VfaYGM0JgbkhNF9isYLEpxfzEEXiVNMoaVstHXsfxTOCscvVNS7FVwWSvyoykvMCnAWHV8VJ5xcu4rgnM26nGqVY2KSjzosBoywpskSw21BdLtxtdXBGY95z6nNXFPhSFbgYdczJCdWA8i+q/L/92qptjAgt+rT7f08UbCoJOBxvL/QUaUsezoPYH8rETPR0RWFChcnoUB5RimpNBrpa+IpxOFe6uXSuNdnW2TWB45nVTfa2SFyUsTOI4Cu3ORFsE6neeusi719qyHW6W6eUs13GHnXdiQgL119Zq4+1r5oNhf21WBzKZl+jrnJDAYEhpB/mqdlXscnZ5P+3iNKyT9fHk4xKoneRexR63ClwLcinCsnjO9rAE9m3Paq62HYbph6Z3LKQxe7ht37AEBkNq71jZ2y7LheI8ONoCR89A9WnodLVvcEev3js3rJMlsaRjEhiOqihedTecean7Z8HP5w7gXuyB/Sdhx3E43mp+vFiIAWFxrCjOFQTqeN6nbTSMpZDU5QQONvDwJ/D4O/Ch402YQ+KFpsmZBC+PJ15BYF6FetSyeMohvOvuuRkwNxumpUPmRMiaCHsa4MXaAch4BOpePRa8UAMVh+FCj2tVEgoGhEfrS+WZwR2HEKjD8E2nOOV3JPlLN8CKIMy/GW4M592Gts3vQ9Vf7RMY7flBO5S8Bmf+l5ALtx2as2cwY3B6YAiBwQq1RllUukVPJHfj9bD+q7AoBwJxHCi3BOrx285B6X7/3o0SoKRhrVRFbe03Q2fP8ir40K8EUMkcWD0HxqUkohm8EKjRL1yCh34HdT58YHSiqn4tt0Szff0E5leouT0Wf0xsnrMeE9LgiSIocpCX80qg1vDsebh/NzTrVLzhlhrgrrq1ckjD9hMYLFdblWKlybH0kq28D/Q7z0kzQaAe72Q7PLAHzl9yMnriviI831Aqq/oJDLsurbSYTHrrmffSMviiQ/K0UqYI1Fjbj8ET7yUmxWGPjslZTNUuTXgGmnacNWhoIcy72aFafd1NEthrwfKd0NjuTpfhpKKOdZjA3JDSvs2PTA2hv7Rr5rhHM0mg1qKmBb5tPiTyqxPr5JEIgeXqGIoC9yYPSH7hs7DvAUiz8bUdbjzTBOpxvvsKHDljwsI+DKH2RKnMFl1idr6HVs9VUn24Ty+Ab9ziTVE/CHz9A9h4wJteQ6QFNSGVLJlVpe7u7eFNE9D5WfDyCu9IfhDY3QvzfgP/uehdvyhCSipfl2CFKlMWm03A/vJrsGSmdyQ/CNRa/fQg7LWdb0tsR0Aok9yQCgGlibvH76G3Zu+thMnXeUUy68YM1mZXPTxmdqtQLsFypfO8RV7N/spN8MJSrygReb9moHasl+wwo6NGEeGgnoE6cJTvFXZjITx8q1eU2ATOzIDbDaTyLQXbaszo2IdSJ7nl6h8mgqfacdbhKRPt8hloAtMXDKFJz0Bd4e5iwzVUpZeXQ/4UM2peNQRCuwRDqkvBOK+mv/0gTLneK0rsJWwG1TyKQLcxAmvW2Iv12THjUFMk82aiXbIiySc/WphAU0v40EORnMZYazoeWLTdN63ajX1EdhdDMNM3RV0DH2uJxAR9aX0fESNuTOW9kezaWGsH/g4//INvWtUZc6R/dgd8x0g8x6yx247Ck9VmMQehHTC2lbtzOlTd55uiroFX7oW/OCratT+UCCHJK1frLMUW+2Kxe6YFoHoVTPTsEHnVZEC+owtu3wq9Pp2WCwcTTIaznlkACzzGAs3RB/sa4ScHTSIOxQqHs0wGVAunwXNj6KzSg7+Fw80+ERgNqGp4kyH9rYvhNgMbf69ma2e85HWvKHHkoyH9MIEGk0q6WEj7hJKweNg/43TUZekOOOlvxdZAUsl0WvMX82D5KB7DeakuUvLmZxuS1jSdWNdf5O1LYfZUP02IjX2kGbTrovfAPrahiXU9kOnSjowJsGuFuQiNHTI+6YDiXWYTR7HGvaK0Q3fyo7go5wZ4dlGkaNLvdqYTVr82ApWqQMziIr/K2zInwJaFUGAo2BrrQejKg7L90D4Cd3UMW94WXsY+FVjqmsDH5sLSXLPzUCnYcwIeP+T7O69f8WELLHUPv0t89SzcUAhzDFxF8f7HsKka6tvMPpQEaPFLfLVwXrl6xFI87adad2XDt/IiDreditWoLl09kUi1jjC/6/m2A+cWJiwy15AjecxB1xDqdKWO5OiiJB3R1u9MHZA41w1t56H1HHz0X9A7iz+dBn1GZFSa3WMOfbNwVA/aaD/SZz/O8TOwfdAmijyWjno5ttawgOOjXnr85GHDyFNwfdhQCyePu4Lr466DlnLywHWcV0LCoFPyyH/8C3kSEhj+KicvnRh2DtoiUEsnrz2JzaFtAsMkJi/euYJFRwT2z8Tk1U8DwQU3Pmfy8rEB1hzPwKho8vq7fkfbzRwckElewOiNv7B08gpQAyT2R3GSl9B6YzN8DfJZSiyLH/t9eYUDTZsDAZ5Kz6ByTF+DPNig5EXcDh5vvK7Jq+ANEalhkj8jMEimhor+DsOymKksckTCv8SYEu93GCL8SykaJUBjIMDfRvt3GP8HXSusQnvj38wAAAAASUVORK5CYII=",l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAD+UlEQVRYCc1ZS0hUURj+z52Z1BzJQhLcFm0yCNq0bN2mBz02qURgOZpoGrgIX1AEjo2omS1aqESZkialSURIVJYYPYQ2RTtFM9Acx+ed0/+fmTuO13tn7h3ndeDOefyv755zzzn//w+DCEvB9fv7uLx2Ajg/wjnkMAY5VJM6bE9ie5Jq7Iwzi+1ZR8PlX5GYYmaErlTe2+vxrpdwLz+FcrlmZJF3gkmsb6dkbW13Fs0YlTUE0FF71+6e81ZwgEoAbjeqXJuPudGo054pNbbVFru1eTZGwwLMq2g+DTK0oUj2hlhUWtNgAUdXY+nTUNokPSLnnOWXtdSAzHqRJ9rgyGw26SYbZEsPhyahvPxJ2ixMd+AGOKsnGNVxxnqyILvA5Tq3pNa7ZQbpbeIKjhDhRJBNrZm0qBH/ns+qQYli9Xgc+gf7h8f419GhkWBbm5bYtyHom9P/JoKFo99mHCz8TPDGCQCko2RhTv6JRiPeEA8aHGCzWuDF63Hofv4+UvzTGZmW/coRFPgG6ZzbDrhI0WjIZfuxCJIASDeE7xDWYE/AEGEhTGRaAFyU5atmb4jUFBvQY7UEFkHzVRQ+WnrjhdvpSiV+qxDi3pPGhX2c7bcKQZIkGHg1Br2Do7riLfWXIGWHDQbffIbHA+90+dQE/31fLZFXgsRcNUMS9HMJmyRcpiRAowWBsFnJn9MiBo/tybSLrmdpBZZX1oJJEbV370pHN5HB8vIqePDRLYjNqjiZukxIaKq5KMh9w5+g7+XHUKyGaLerLkBa6g4YHvkCD/vf6soQNok8YV2OBBMIm2RkBhOF0z+DWCVpYYxzPGXZVJLiQ1hsijbJJLYOhAJZebNTkBc9y6HYDNNuOB+JXUynQqhC2KwUGoZb5JnZ+VB6TNP+/P1nSIawSRS3GuJOBBNikyioToRtIzYJm3BY88qav6OAqfs4Iz1V2FhZW4fV1XXR1nJY7chHRoL5BHP4n4muptJDwpuhiB+9B1MAFxaNbRi3QT41XsJEY8KZo3QEbumwUb5aSez6zO3D5AdIuRJcBmfsDJrTTFiU/I3PYUV5ypVg0FSEzYiDpsKqdoHE693W5TRNWJRXCvjrIorCXAkudcTaZdkL9GAArug3WVPYCQ4loiPhTYHCtw9DPw4fPU7jx+gn3oUBq+tylfqWwW9cHDPBQCj9kH+ttTtueRnFOOZnOu+UnEdHdtP0B5Z4g49xSuTgDdOjjMW89ieP1ODI7pYZVMDQTBaUt1bj69TELhXCKMdS1+EqqdcCFxKgAjRpE5gKQErkUK4EP+Da6BzmlAJmtaQzOEmk2FPXukusZqQ+pSNEFsIX6Ju6GlE8dkl0LbDx+hviP6ddi+ZxzLMOAAAAAElFTkSuQmCC",o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAEUUlEQVRYCc1ZTWgVVxT+7uQZE4x/D3+aoMWaqKDZaF3UPxBxp2JblESkCKUr0YVodyX+gS6qdCMuxVKKBkVx4UbdGXUl0pIsBC0qmqDWqPhCokne9Tv3vXlv3nszd+48k+gJyczc891zvjlzf849UahS9K22ZmSxFVn1LRSaaKYJWssVUKqXf3uh+evpu/BwRa3pfGh0Cf+oJHh966c5GB3eQyI/sF9rkr7EdpP4ZdRMOqXW/PXCta8TQd2zuwH9/fuR1QdouMHVeAQuA0+dQDp9Ui07nYnAFJpjCequth8xqk4Dem6h15jcqOeo0bvV2s5LNnNelFJrrfTN9oMcZxfHnpx45QvTtvgQX1E8QhX69r56jPT+yUG+ParjmLYrXECqaZda/cdgud2KCJq3mUhywkgCQZ9hkawgiK4dHRMWuWC4hKT4LpOST2wmhIw5zZXtc4iiZw/bghOnQMQsJf/3PxifCZHkbTm7Z6Vb/CWo+IllnRuLpaThG2DlCaDlF2DqoiTM8ljObsMl92giaHaIkQ+yFSVfhGvTJLIQqJ0J9F2nBd6vOFYk9vY+8OAMMPC42BZ/l0Gqtll2nJTBZof38pqM3OxVwLzNJNdcdCcEy2X6EhI+Djw8y935Wrk26rnBbKlAR56g/j4KWdEukVq6D5i2uEIV2aA4klp+BlJTgCeXI2Elitx+3+GZrMR1469nsrKcny8JuaDXBW3ArO+CLbb7VuHmmZTJBvN18vatvwKTGcFPkUX5SLrYYDpHgsznXGRBO1Df6IK0YyZNA+ZvtWN8Lbl5+WTTbwq/1jGRadwQrqum9av1TGpzw9/anYkwR6/Jhq04yIxVNXZMEq1E0W0cN6UKabrNQXq5TVuqG2VC8qantC3sSUjGCY8QKX5izR+71M2264PawT7g36PBlurvyY0DQdGinmq14vK2YmDeFqsZo9RZ4NnVeJxBqD4ZqXICs6+6Izw61M6IN7pwZzzmw5sEBNHr8fMKQbsMvbTrk2iT2CI3z5xb4xy8/icO4a5PYotnahLElVjrL+5wmHLsfKqIDbHlKuTm5U/83dY+g89ouMsKcVKKDbHlJt3CTRZqKVXEpxj//Q28f+VmOgwlfcWGq+Q55QiyHMF+9lP+8Fug53dg2A4L9S99TF/acJOMlEgEagiaWomUI+Ik8wi49xuz46dxyKJesNJH+roKufj1m+oOTbIvN27kwrwJqJsT7naI9aGnXJD7bnCCjYZjQltLD00FgoKt6tg55etc2u8v5LIQv+PxZuBJqHtro+3Y6XfM1WP0If95Qq+eOqTWnT8c9FkSQVGY8sPN9k7uMNuDwHG/l/rMuvNtSqmS1CU3iwPeDYCFHGY5FwLN43vrF4/KyInTigj6TEwkTZ1GH2Q0I3E+vqqrjDmlDmPtuSPlkfPtxTr+3AXMWILyJl90CdgPtSEqRXSpQmTNQb81qHO4H78iepjzifo3xEcU/nv364OzKAAAAABJRU5ErkJggg==",d=a(3795),c={components:{showCompare:d.Z},props:{keys:{type:String,default:"experienceValue,totalMentionValue"},data:{type:Object,default(){return{}}}},computed:{showExperienceData(){for(var e={experienceValue:{key:"experienceValue",name:"体验值",mom:"momExperienceValueRate"},negativeMentionRate:{key:"negativeMentionRate",name:"负面提及率",mom:"momNegativeMentionRate"},totalMentionValue:{key:"totalMentionValue",name:"提及量",mom:"momTotalMentionValueRate"}},t=[],a=this.keys.split(","),s=0;s<a.length;s++)a[s]&&t.push(e[a[s]]);return t}},data(){return{imgs:{experienceValue:i,momExperienceValueRate:n,totalMentionValue:l,momTotalMentionValueRate:o,negativeMentionRate:i,momNegativeMentionRate:n}}}},h=c,m=a(1001),u=(0,m.Z)(h,s,r,!1,null,"34794627",null),p=u.exports},8115:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("chart-box",e._b({attrs:{chartId:e.chartId,title:"人群特征",loading:e.loading,data:e.data.details},on:{download:e.downloadHandle}},"chart-box",e.$attrs,!1),[Object.keys(e.data).length?s("div",{ref:"content",staticClass:"public-chart-content",attrs:{slot:"content"},slot:"content"},[s("div",{staticClass:"population-characteristics p-t-10"},[s("div",{staticClass:"each-part"},e._l(e.leftArr,(function(t,a){return s("div",{key:a,staticClass:"left-part-detail m-t-20 m-b-20 p-l-10 p-t-5 p-b-5 p-t-10 p-r-10"},[s("span",{staticStyle:{"font-weight":"500"}},[e._v(e._s(t.type)+"：")]),e._v(" "+e._s(t.secondType)+"为主（占"+e._s(e.$publicHandle.formatPercent(t.mentionRate))+"%） "),s("div",{staticClass:"show-color",style:{background:e.colors[2*a]}})])})),0),s("div",{staticClass:"each-part each-part-center"},[s("div",{staticClass:"each-part-center-img"},[s("img",{attrs:{src:a(2285),alt:""}})]),s("div",{staticClass:"each-part-center-total total"},[e._v("人群总数："+e._s(e.$publicHandle.Thousandth(e.data.total))+"人")]),e.leftArr.length>=1?s("div",{staticClass:"circle1"}):e._e(),e.leftArr.length>=2?s("div",{staticClass:"circle2"}):e._e(),e.leftArr.length>=3?s("div",{staticClass:"circle3"}):e._e(),e.rightArr.length>=1?s("div",{staticClass:"circle4"}):e._e(),e.rightArr.length>=2?s("div",{staticClass:"circle5"}):e._e(),e.rightArr.length>=3?s("div",{staticClass:"circle6"}):e._e()]),s("div",{staticClass:"each-part"},e._l(e.rightArr,(function(t,a){return s("div",{key:a,staticClass:"right-part-detail m-t-20 m-b-20 p-l-10 p-t-5 p-b-5 p-t-10 p-r-10"},[s("span",{staticStyle:{"font-weight":"500"}},[e._v(e._s(t.type)+"：")]),e._v(" "+e._s(t.secondType)+"为主（占"+e._s(e.$publicHandle.formatPercent(t.mentionRate))+"%） "),s("div",{staticClass:"show-color",style:{background:e.colors[2*a+1]}})])})),0)]),s("div",{staticClass:"list-area"},e._l(e.chartObj,(function(t,a,r){return s("div",{key:r,staticClass:"each-list",style:{border:e.border[r],boxShadow:e.boxShadow[r]}},[s("div",{staticClass:"each-list-title p-l-15 p-t-10 p-b-10",style:{background:e.colors[r]}},[s("span",{staticClass:"tooltips-name"},[e._v(e._s(a))]),"性别"!=a?s("span",{staticClass:"tooltips"},[e._v("top5")]):e._e()]),s("div",{staticClass:"each-list-body"},e._l(t,(function(t,r){return s("div",{key:r,staticClass:"each-list-detail p-t-8 p-b-8"},["客户类型"!=a&&"性别"!=a&&r<3?s("span",{staticClass:"number",class:"no-"+r},[e._v("NO"+e._s(r+1))]):e._e(),"客户类型"!=a&&"性别"!=a&&r>=3?s("span",{staticClass:"number",attrs:{clas:""}},[e._v(e._s(r+1))]):e._e(),"客户类型"==a||"性别"==a?s("span",{staticClass:"p-l-15",staticStyle:{width:"0"}}):e._e(),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.secondType,placement:"top"}},[s("span",[e._v(e._s(t.secondType))])]),s("span",{staticClass:"each-list-value"},[e._v(e._s(e.$publicHandle.formatPercent(t.mentionRate))+"%")])],1)})),0)])})),0)]):s("no-data",{attrs:{slot:"content"},slot:"content"})],1)],1)},r=[],i={props:{chartId:{type:String,default:""},data:{type:Object,default(){return{}}},loading:{type:Boolean},tabTitle:{type:String,default:()=>"营销服务"}},data(){return{chartObj:{},leftArr:[],rightArr:[],colors:["#DAF0FF","#C7CEDA","#CFF4EA","#DACEED","#C7CEDA","#CFF4EA"],borderColor:["#0077FF","#5D7092","#3ED4A9","#BA70CA","#C7CEDA","#CFF4EA "],border:["1px solid #2994FF","1px solid #5D7092","1px solid #3ED4A9","1px solid #BA70CA","1px solid #5D7092","1px solid #3ED4A9"],boxShadow:["0px 0px 0px 3px #DAF0FF","0px 0px 0px 3px #C7CEDA","0px 0px 0px 3px #CFF4EA","0px 0px 0px 3px #DACEED","0px 0px 0px 3px #C7CEDA","0px 0px 0px 3px #CFF4EA "]}},watch:{data:{immediate:!0,handler(e,t){this.dataHandle()}}},methods:{dataHandle(){for(var e={},t=this.data.details||[],a=0;a<t.length;a++){var s=t[a].type;void 0==e[s]&&(e[s]=[]),e[s].push(t[a])}var r=[],i=[],n=0;for(var a in e)e[a].sort(((e,t)=>t.mentionRate-e.mentionRate)),n%2==0?r.push(e[a][0]):i.push(e[a][0]),n++,e[a]=e[a].slice(0,5);this.chartObj=e,this.leftArr=r,this.rightArr=i},downloadHandle(e){this.$emit("download",e,"populationCharacteristics",this.data)}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"54dee58a",null),d=o.exports},3795:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"show-compare"},["momTotalMentionValueRate"===e.compareKey?a("div",[e.compareValue>0?[a("span",{staticClass:"el-icon-caret-top"}),a("span",{staticClass:"value",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),e.compareValue<0?[a("span",{staticClass:"el-icon-caret-bottom"}),a("span",{staticClass:"value",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),0==e.compareValue?[a("span",{staticClass:"value",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e()],2):e._e(),"momExperienceValueRate"===e.compareKey?a("div",[e.compareValue>0?[a("span",{staticClass:"el-icon-caret-top green"}),a("span",{staticClass:"value green",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),e.compareValue<0?[a("span",{staticClass:"el-icon-caret-bottom red"}),a("span",{staticClass:"value red",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),0==e.compareValue?[a("span",{staticClass:"value",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e()],2):e._e(),"momNegativeMentionRate"===e.compareKey?a("div",[e.compareValue>0?[a("span",{staticClass:"el-icon-caret-top red"}),a("span",{staticClass:"value red",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),e.compareValue<0?[a("span",{staticClass:"el-icon-caret-bottom green"}),a("span",{staticClass:"value green",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e(),0==e.compareValue?[a("span",{staticClass:"value",class:e.customClass},[e._v(e._s(e.$publicHandle.formatPercent(e.compareValue))+" %")])]:e._e()],2):e._e()])},r=[],i={props:{compareKey:{type:String,default:""},compareValue:{type:[Number,String],default:""},customClass:{type:String,default:""}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"9acf909e",null),d=o.exports},3557:function(e,t,a){"use strict";a.d(t,{Z:function(){return ot}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"search-center"},[a("el-form",{key:e.selectRowKey,ref:"searchForm",staticClass:"search-form p-l-15 p-r-15"},[e.selectRow.attentionButton?a("el-form-item",[a("attention-button",e._b({ref:"attention",attrs:{selectRow:e.selectRow.attentionButton},on:{change:e.changeAttention}},"attention-button",e.$attrs,!1))],1):e._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:"全部"===e.attentionValue,expression:"attentionValue === '全部'"}]},[e.selectedShow?a("el-form-item",{attrs:{label:"已选："}},[a("selected-item",{staticClass:"show-selected",attrs:{showWrap:e.showWrap,dynamicTags:e.dynamicTags,indicatorSelect:e.indicatorSelect},on:{cutShowWrap:e.cutShowWrap,refresh:e.tagRefresh}})],1):e._e(),e.selectedShow?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.indicatorItem?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.indicatorSelectFlag,expression:"indicatorSelectFlag"}],attrs:{label:"指标体系："}},[a("indicator-item",{ref:"indicator",attrs:{selectRow:e.selectRow.indicatorItem,showWrap:e.showWrap,list:e.selectRow.indicatorItem.list||[],indicatorSelect:e.indicatorSelect},on:{cutShowWrap:e.cutShowWrap,selectedChange:e.selectedChange}})],1):e._e(),e.selectRow.indicatorItem?a("div",{directives:[{name:"show",rawName:"v-show",value:e.indicatorSelectFlag,expression:"indicatorSelectFlag"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.textSearch?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:e.selectRow.textSearch.name||"原文搜索："}},[a("text-search",{ref:"textSearch",attrs:{selectRow:e.selectRow.textSearch},on:{change:e.selectedChange}})],1):e._e(),e.selectRow.textSearch?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.textSearch1?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"标准关键词搜索："}},[a("text-search1",{ref:"textSearch1",attrs:{selectRow:e.selectRow.textSearch1},on:{change:e.selectedChange}})],1):e._e(),e.selectRow.textSearch1?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.marketSegment?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"search-form-label-lh-28",attrs:{label:"细分市场："}},[a("market-segment",{ref:"marketSegment",attrs:{selectRow:e.selectRow.marketSegment},on:{change:e.changeMarket}})],1):e._e(),e.selectRow.marketSegment?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.carAllBrandAsSeriesSelect&&!e.selectRow.carAllBrandAsSeriesSelect.hidden?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"series"===e.selectRow.carAllBrandAsSeriesSelect.type?"车系：":"品牌："}},[a("car-all-brand-as-series-select",{ref:"carBrandAsSeriesSelect",attrs:{selectRow:e.selectRow.carAllBrandAsSeriesSelect,marketArr:e.marketArr},on:{change:e.changeBrandOrSeries,clickMoreChange:function(t){return e.showMore=t}}})],1):e._e(),e.selectRow.carAllBrandAsSeriesSelect?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.measureIndex?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"度量指标："}},[a("measure-index")],1):e._e(),e.selectRow.carSelfBrandSeriesSelect?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"series"===e.selectRow.carSelfBrandSeriesSelect.type?"车系：":"品牌："}},[a("car-self-brand-series-select",{ref:"carBrandAsSeriesSelect",attrs:{selectRow:e.selectRow.carSelfBrandSeriesSelect,marketArr:e.marketArr},on:{change:e.changeBrandOrSeries}})],1):e._e(),e.selectRow.carSelfBrandSeriesSelect?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.screenItem?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"筛选："}},[a("screen-item",{ref:"screenItem",attrs:{selectRow:e.selectRow.screenItem.selectRow,diffDaysNum:0==e.selectRow.diffDaysNum?0:1},on:{change:e.screenItemChange}})],1):e._e(),e.selectRow.screenItem?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.comparativeGroup?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"对比组："}},[a("comparative-group",{ref:"comparativeGroup",attrs:{selectRow:e.selectRow.comparativeGroup},on:{change:e.changeComparativeGroup}})],1):e._e(),e.selectRow.comparativeGroup?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.restsItem?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"其他："}},[a("rests-item",{ref:"restsItem",attrs:{diffDaysNum:0==e.selectRow.diffDaysNum?0:1,selectRow:e.selectRow.restsItem.selectRow},on:{change:e.restsItemChange}})],1):e._e(),e.selectRow.restsItem?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.selectRow.commonDate&&!e.selectRow.commonDate.hidden?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:"时间："}},[a("div",{staticClass:"flex align-center m-l-10"},[a("common-date",{ref:"date",attrs:{"dateseg-list":e.datesegList,selectRow:e.selectRow.commonDate,diffDaysNum:0==e.selectRow.diffDaysNum?0:1},on:{confirm:e.handleDateChange}})],1)]):e._e(),e.selectRow.commonDate?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e()],1),e.selectRow.collection?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}]},[a("rests-item",{ref:"restsItem",attrs:{selectRow:e.selectRow.collection.selectRow,diffDaysNum:0==e.selectRow.diffDaysNum?0:1},on:{change:e.restsItemChange}})],1):e._e(),e.selectRow.collection?a("div",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"dot-horizon-divider"}):e._e(),e.searchBtn?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],attrs:{label:""}},[a("div",{staticClass:"flex align-center p-l-5 ps-relative"},[a("el-button",{staticClass:"search-button",attrs:{size:"small",type:"primary"},on:{click:e.timeoutSelectedChange}},[a("span",{staticClass:"el-icon-search"}),e._v(" 查看")]),a("el-button",{staticStyle:{"margin-left":"24px"},attrs:{size:"small"},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.reset.apply(null,arguments)}}},[e._v("重置 ")]),e._t("tips")],2)]):e._e()],1)],1)},r=[],i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{key:e.dateKey,staticClass:"commonDate"},[e.dateSeg.length>1?a("el-select",{staticStyle:{width:"90px","margin-right":"5px"},attrs:{placeholder:"请选择"},on:{change:e.changeTimeSeg},model:{value:e.timeseg,callback:function(t){e.timeseg=t},expression:"timeseg"}},e._l(e.dateSeg,(function(e){return a("el-option",{key:e.value,staticStyle:{width:"100px"},attrs:{label:e.label,value:e.value}})})),1):e._e(),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"year"==e.timeseg,expression:"timeseg == 'year'"}],ref:"year_ref",staticClass:"search-box-picker",staticStyle:{width:"100px"},attrs:{"picker-options":e.pickerOptionsCom,clearable:!1,type:"year","value-format":"yyyy",format:"yyyy"},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.yearTime,callback:function(t){e.yearTime=t},expression:"yearTime"}}),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"season"==e.timeseg,expression:"timeseg == 'season'"}],ref:"season_ref",staticStyle:{width:"100px"},attrs:{"picker-options":e.pickerOptionsCom,clearable:!1,type:"year","value-format":"yyyy",format:"yyyy"},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.seasonYear,callback:function(t){e.seasonYear=t},expression:"seasonYear"}}),a("el-select",{directives:[{name:"show",rawName:"v-show",value:"season"==e.timeseg,expression:"timeseg == 'season'"}],ref:"season_ref",staticStyle:{width:"120px","margin-left":"2px"},attrs:{placeholder:"请选择"},on:{change:function(t){return e.$emit("confirm")}},model:{value:e.seasonVal,callback:function(t){e.seasonVal=t},expression:"seasonVal"}},e._l(e.seasonData,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,disabled:e.disabled,value:e.value}})})),1),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"month"==e.timeseg,expression:"timeseg == 'month'"}],ref:"month_ref",staticClass:"search-box-picker",staticStyle:{width:"200px"},attrs:{type:"monthrange",clearable:!1,"picker-options":e.pickerOptionsCom,"value-format":"yyyy-MM",format:"yyyy-MM","range-separator":"至","unlink-panels":!0},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.monthsTime,callback:function(t){e.monthsTime=t},expression:"monthsTime"}}),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"other"==e.timeseg,expression:"timeseg == 'other'"}],ref:"other_ref",staticClass:"search-box-picker m-t-1",staticStyle:{width:"280px"},attrs:{"popper-class":"search-date-popper",type:"daterange","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd","picker-options":e.pickerOptionsCom,clearable:!1,editable:!1,"range-separator":"至","unlink-panels":!0},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.othersTime,callback:function(t){e.othersTime=t},expression:"othersTime"}}),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"week"==e.timeseg,expression:"timeseg == 'week'"}],ref:"week_ref",staticStyle:{width:"140px"},attrs:{"picker-options":e.pickerOptionsCom,type:"week",clearable:!1},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.weekTime,callback:function(t){e.weekTime=t},expression:"weekTime"}}),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"monthSin"==e.timeseg,expression:"timeseg == 'monthSin'"}],ref:"monthSin_ref",staticStyle:{width:"140px"},attrs:{"picker-options":e.pickerOptionsSinMonth,type:"month",clearable:!1,"value-format":"yyyy-MM",format:"yyyy-MM"},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.sinMonth,callback:function(t){e.sinMonth=t},expression:"sinMonth"}}),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:"otherSin"==e.timeseg,expression:"timeseg == 'otherSin'"}],ref:"otherSin_ref",staticStyle:{width:"140px"},attrs:{"picker-options":e.pickerOptionsCom,type:"date","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:e.singleDayClearable},on:{change:e.dateChange,blur:function(t){return e.$emit("confirm")}},model:{value:e.sinOther,callback:function(t){e.sinOther=t},expression:"sinOther"}})],1)},n=[];const l=[{value:"year",label:"年份"},{value:"season",label:"季度"},{value:"month",label:"月份"},{value:"other",label:"自定义"},{value:"week",label:"周"},{value:"monthSin",label:"月份"},{value:"otherSin",label:"自定义"},{value:"unlimited",label:"不限"}],o=[{value:"1",label:"第一季度"},{value:"2",label:"第二季度"},{value:"3",label:"第三季度"},{value:"4",label:"第四季度"}];let d=["2020-04-01","2040-12-31"];var c={props:{datesegList:{type:String,default:()=>"1|1|1|1|1|0|0"},defaultTimeUnit:{type:String,default:()=>""},diffDaysNum:{type:Number,default:()=>1},isCanSelectCurrentTime:{type:String,default:()=>"0|0|0"},limitOtherTime:{type:String|Number,default:()=>"0"},defaultOtherTime:{type:String|Number,default:()=>"30"},minCheckDate:{type:String,default:()=>""},maxCheckDate:{type:String,default:()=>""},singleDayClearable:{type:Boolean,default:()=>!0},limitYearTime:{type:String|Number,default:()=>"0"},selectRow:{type:Object,default:()=>({})}},watch:{dateSeg:(e,t)=>{if(JSON.stringify(e)==JSON.stringify(t))return;const a=new Map([["year",1],["season",2],["month",3],["monthSin",4],["week",5],["other",6],["otherSin",7]]);let s=e;return s.sort((function(e,t){return a.get(e.value)<a.get(t.value)?-1:1})),s}},data(){return{seasonData:[],timeseg:"other",startDate:"",endDate:"",othersTime:[],yearTime:"",seasonYear:"",seasonVal:"1",monthsTime:[],weekTime:"",sinMonth:"",sinOther:"",dateSeg:[],pickerOptionsCom:{},pickerOptionsSinMonth:{},autoTime:[],defaultDate:null,dateKey:0}},created(){const e=this;this.defaultDate="{}"===JSON.stringify(this.selectRow)?null:this.selectRow,e.init()},methods:{getAutoTime(){let e=this,t=[];e.maxCheckDate?t[1]=e.$moment(e.maxCheckDate)>e.$moment(d[1])?d[1]:e.maxCheckDate:t[1]=d[1],e.diffDaysNum>0?e.$moment(t[1]).isBefore(e.$moment().subtract(e.diffDaysNum,"days"))||(t[1]=e.$moment().subtract(e.diffDaysNum,"days").format("YYYY-MM-DD")):e.$moment(t[1]).isBefore(e.$moment())||(t[1]=e.$moment().format("YYYY-MM-DD")),e.minCheckDate?t[0]=e.$moment(e.minCheckDate)>e.$moment(d[0])?e.minCheckDate:d[0]:t[0]=d[0],e.autoTime=t},setDateSeg(){var e=!1;let t=this.$moment(this.autoTime[0]),a=this.$moment(this.autoTime[1]);this.$moment().subtract(parseInt(this.isCanSelectCurrentTime.split("|")[0]),"year");var s=this.$moment(t).startOf("year").format("x"),r=this.$moment(t).startOf("day").format("x");s<r&&(t=t.add(1,"year")),this.dateSeg=[];const i=this.datesegList.split("|");for(const n in i)"1"===i[n]&&("year"==l[n].value?t.format("x")<=a.format("x")&&this.dateSeg.push(l[n]):(this.dateSeg.push(l[n]),"unlimited"==l[n].value&&(e=!0)));this.timeseg=this.dateSeg[0].value,e&&(this.timeseg="unlimited")},seasonDataFn(e){let t=this,a=t.autoTime[0],s=0;if("01-01"!=t.$moment(a).format("MM-DD")&&t.seasonYear==t.$moment(a).format("YYYY")){let e=t.$moment(a).quarter(),r=t.$moment(a).format("MM-DD");"01-01"!==r&&"04-01"!==r&&"07-01"!==r&&"10-01"!==r||(e-=1),s=e}t.seasonData=[];for(let r=s;r<e;r++)t.seasonData.push(o[r]);if(t.seasonData.length>0)t.seasonVal=t.seasonData[0].value;else{const e=t.datesegList.split("|");e[1]&&(e[1]=0),t.setDateSeg(e),t.timeseg=t.defaultTimeUnit?t.defaultTimeUnit:t.dateSeg[t.dateSeg.length-1].value}},setDefaultDate(e){const t=this;t.timeseg=t.defaultTimeUnit?t.defaultTimeUnit:t.dateSeg[t.dateSeg.length-1].value,t.othersTime=[t.$moment(t.autoTime[1]).subtract(t.defaultOtherTime-1,"day").format("YYYY-MM-DD"),t.$moment(t.autoTime[1]).format("YYYY-MM-DD")],t.yearTime=t.$moment(t.autoTime[1]).subtract(parseInt(t.isCanSelectCurrentTime.split("|")[0]),"year").format("YYYY"),t.seasonYear=t.$moment(t.autoTime[1]).format("YYYY");let a=t.$moment(t.autoTime[1]).quarter();const s=t.$moment(t.autoTime[1]).format("MM-DD");"01-01"!=s&&"04-01"!=s&&"07-01"!=s&&"10-01"!=s||(a-=1),"01-01"==t.$moment(t.autoTime[1]).startOf("day").format("MM-DD")&&(t.seasonYear=parseInt(t.seasonYear)-1+""),this.seasonDataFn(a);let r=t.$moment().subtract(parseInt(t.isCanSelectCurrentTime.split("|")[2]),"month"),i=t.$moment(t.autoTime[1]).isBefore(r)?t.$moment(t.autoTime[1]):r;t.monthsTime=[i.format("YYYY-MM"),i.format("YYYY-MM")],t.weekTime=t.$moment().week(t.$moment().week()-1).subtract(t.diffDaysNum,"days").startOf("week").add(1,"days").format("YYYY-MM-DD"),t.sinMonth=i.format("YYYY-MM"),t.sinOther=t.$moment(t.autoTime[1]).format("YYYY-MM-DD");let n=!1;if(e||(e={endTime:t.othersTime[1],startTime:t.othersTime[0],timeseg:"other"}),n=!0,n)switch(t.timeseg=e.timeseg,e.timeseg){case"year":t.yearTime=t.$moment(e.startTime).format("YYYY");break;case"season":switch(t.seasonYear!==t.$moment(e.startTime).format("YYYY")?(t.seasonYear=t.$moment(e.startTime).format("YYYY"),this.seasonDataFn(4)):t.seasonYear=t.$moment(e.startTime).format("YYYY"),t.$moment(e.startTime).format("MM")){case"01":t.seasonVal="1";break;case"04":t.seasonVal="2";break;case"07":t.seasonVal="3";break;case"10":t.seasonVal="4";break;default:t.seasonVal="1"}break;case"month":t.monthsTime=[t.$moment(e.startTime).format("YYYY-MM"),t.$moment(e.endTime).format("YYYY-MM")],t.sinMonth=t.$moment(e.startTime).format("YYYY-MM");break;case"week":t.othersTime=[t.$moment(e.startTime).format("YYYY-MM-DD"),t.$moment(e.endTime).format("YYYY-MM-DD")],t.sinOther=t.$moment(e.endTime).format("YYYY-MM-dd");break;case"other":t.othersTime=[t.$moment(e.startTime).format("YYYY-MM-DD"),t.$moment(e.endTime).format("YYYY-MM-DD")],t.sinOther=t.$moment(e.endTime).format("YYYY-MM-dd");break;default:t.othersTime=[t.$moment(e.startTime).format("YYYY-MM-DD"),t.$moment(e.endTime).format("YYYY-MM-DD")],t.sinOther=t.$moment(e.endTime).format("YYYY-MM-dd");break}},setPickerOptionsCom(){const e=this;e.pickerOptionsCom={firstDayOfWeek:1,disabledDate(t){let a=e.$moment("1900-01-01"),s=e.$moment(e.autoTime[1]),r=e.$moment(t);if("other"===e.timeseg);else if("week"===e.timeseg){parseInt(e.$moment().subtract(e.diffDaysNum,"days").format("d"));e.$moment().week(e.$moment(s).week()-1).endOf("week").add(1,"days").isBefore(a)||(s=e.$moment().week(e.$moment(s).week()-1).endOf("week").add(1,"days"))}else if("year"===e.timeseg){const t=e.$moment().subtract(parseInt(e.isCanSelectCurrentTime.split("|")[0]),"year");t.isBefore(s)&&(s=t)}else if("month"===e.timeseg||"monthSin"===e.timeseg){const t=e.$moment().subtract(parseInt(e.isCanSelectCurrentTime.split("|")[2]),"month");t.isBefore(s)&&(s=t)}else e.timeseg;return s=s.toDate().getTime(),a=a.toDate().getTime(),r=r.toDate().getTime(),!(r<=s&&r>=a)}},e.pickerOptionsSinMonth={disabledDate(t){const a=e.$moment("1970-01-01"),s=e.$moment().subtract(1,"month").endOf("month"),r=t.getTime();return!(r<=s&&r>=a)}}},dateChange(e){if("season"===this.timeseg)if(e===this.$moment(this.autoTime[1]).format("YYYY")){let e=this.$moment(this.autoTime[1]).quarter();this.seasonDataFn(e),this.seasonVal=e}else this.seasonDataFn(4)},changeTimeSeg(e){this.$emit("confirm",e)},getDate(){const e={dateSeg:this.timeseg};switch(this.timeseg){case"year":e.dateFormat="%Y%m",e.startTime=this.yearTime+"-01-01 00:00:00",e.endTime=this.yearTime===this.$moment().format("YYYY")?this.$moment(this.autoTime[1]).format("YYYY-MM-DD")+" 23:59:59":this.yearTime+"-12-31 00:00:00";break;case"season":e.dateFormat="%Y%m";const t=this.$moment().subtract(1,"day"),a=this.$moment(t).format("YYYY-MM-DD")+" 23:59:59";switch(this.seasonVal+""){case"1":e.startTime=this.seasonYear+"-01-01 00:00:00",e.endTime=this.$moment(this.seasonYear+"-03-31 23:59:59").isBefore(t)?this.seasonYear+"-03-31 23:59:59":a;break;case"2":e.startTime=this.seasonYear+"-04-01 00:00:00",e.endTime=this.$moment(this.seasonYear+"-06-30 23:59:59").isBefore(t)?this.seasonYear+"-06-30 23:59:59":a;break;case"3":e.startTime=this.seasonYear+"-07-01 00:00:00",e.endTime=this.$moment(this.seasonYear+"-09-30 23:59:59").isBefore(t)?this.seasonYear+"-09-30 23:59:59":a;break;case"4":e.startTime=this.seasonYear+"-10-01 00:00:00",e.endTime=this.$moment(this.seasonYear+"-12-31 23:59:59").isBefore(t)?this.seasonYear+"-12-31 23:59:59":a;break;default:e.startTime=this.seasonYear+"-01-01 00:00:00",e.endTime=this.$moment(this.seasonYear+"-03-31 23:59:59").isBefore(t)?this.seasonYear+"-03-31 23:59:59":a;break}break;case"month":e.dateFormat="%Y%m",e.startTime=this.monthsTime[0]+"-01 00:00:00",e.endTime=this.monthsTime[1]===this.$moment().format("YYYY-MM")?this.$moment(this.autoTime[1]).format("YYYY-MM-DD")+" 23:59:59":this.$moment(this.monthsTime[1]).endOf("month").format("YYYY-MM-DD")+" 23:59:59";break;case"other":e.dateFormat="%Y%m%d",e.startTime=this.othersTime[0]+" 00:00:00",e.endTime=this.$moment(this.othersTime[1]).format("YYYY-MM-DD")==this.$moment().format("YYYY-MM-DD")?this.$moment().format("YYYY-MM-DD HH:mm:ss"):this.othersTime[1]+" 23:59:59";break;case"week":e.dateFormat="%Y%m%d",e.startTime=this.$moment(this.weekTime).startOf("week").add(1,"days").format("YYYY-MM-DD")+" 00:00:00",e.endTime=this.$moment(this.weekTime).endOf("week").add(1,"days").format("YYYY-MM-DD")+" 23:59:59";break;case"monthSin":e.dateFormat="%Y%m",e.dateSeg="month",e.startTime=this.sinMonth+"-01 00:00:00",e.endTime=this.$moment(e.startTime).endOf("month").format("YYYY-MM-DD")+" 23:59:59";break;case"otherSin":e.dateFormat="%Y%m%d",e.dateSeg="other",e.startTime=this.sinOther+" 00:00:00",e.endTime=this.sinOther+" 23:59:59";break;default:e.startTime=this.othersTime[0]+" 00:00:00",e.endTime=this.$moment(this.othersTime[1]).format("YYYY-MM-DD")==this.$moment().format("YYYY-MM-DD")?this.$moment().format("YYYY-MM-DD HH:mm:ss"):this.othersTime[1]+" 23:59:59";break}return{dateType:e.dateSeg,startDate:e.startTime,endDate:e.endTime}},init(){this.getAutoTime(),this.setDateSeg(),this.setPickerOptionsCom(),this.setDefaultDate(this.defaultDate)},reset(){this.dateKey++,this.getAutoTime(),this.setDateSeg(),this.setPickerOptionsCom(),this.setDefaultDate(null),this.$emit("confirm")}}},h=c,m=a(1001),u=(0,m.Z)(h,i,n,!1,null,"6b779382",null),p=u.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{key:e.tagKey,staticClass:"select-item flex",on:{click:e.cutShowWrap}},[a("div",{staticClass:"flex align-center float-left select-conter"},[e._l(e.dynamicTags,(function(t,s){return[t.name.includes("品牌：")?[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top",disabled:!t.allName.length}},[a("span",{attrs:{slot:"content"},slot:"content"},e._l(t.allName,(function(s,r){return a("span",{key:r},[e._v(e._s(s)+" "+e._s(r<t.allName.length-1?"，":""))])})),0),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:e.showTag(t.name),expression:"showTag(tag.name)"}],key:t.name,staticClass:"flex align-center m-r-8",attrs:{size:"medium",closable:"","disable-transitions":"",type:"info"},on:{close:function(a){return e.close(t.parent,t.itself)}}},[e._v(" "+e._s(t.name)+" ")])],1)]:[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top"}},[a("span",{attrs:{slot:"content"},slot:"content"},[t.allName&&t.allName.length?a("span",e._l(t.allName,(function(s,r){return a("span",{key:r},[e._v(e._s(s)+" "+e._s(r<t.allName.length-1?"，":""))])})),0):a("span",[e._v(" "+e._s(t.name))])]),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:e.showTag(t.name),expression:"showTag(tag.name)"}],key:t.name,staticClass:"m-t-2 m-r-8 word-limit",attrs:{size:"medium",closable:"","disable-transitions":"",type:"info"},on:{close:function(a){return e.close(t.parent,t.itself)}}},[e._v(" "+e._s(t.name)+" ")])],1)]]}))],2),e.indicatorSelect?e._e():a("div",{staticClass:"float-right"},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"f-s-14 f-color-2 more-cursor"},[e._v("收起条件筛选 "),a("i",{staticClass:"el-icon-arrow-up m-l-6"})]),a("span",{directives:[{name:"show",rawName:"v-show",value:!e.showWrap,expression:"!showWrap"}],staticClass:"f-s-14 f-color-2 more-cursor"},[e._v("展开条件筛选 "),a("i",{staticClass:"el-icon-arrow-down m-l-6"})])])])},g=[],v={data(){return{tagKey:0}},props:{showWrap:{type:Boolean,default:!1},dynamicTags:{type:Array,default:()=>[]},indicatorSelect:{type:Boolean}},computed:{showTag:function(){return e=>{let t=e.split("：");return!("不限"==t[1]||!t[1])}}},watch:{dynamicTags(){this.tagKey++}},methods:{cutShowWrap(){this.indicatorSelect||this.$emit("cutShowWrap")},close(e,t){this.$emit("refresh",e,t)}}},f=v,b=(0,m.Z)(f,y,g,!1,null,"41192e10",null),w=b.exports,S=a(9188),D=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"rests-item-box"},[e.selectRow.taskType?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("任务类型")]),e.taskTypeOpt.length>1?a("el-select",{staticStyle:{width:"120px"},attrs:{"value-key":"label"},on:{change:e.changeTaskType},model:{value:e.taskType,callback:function(t){e.taskType=t},expression:"taskType"}},e._l(e.taskTypeOpt,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e}})})),1):e._e()],1):e._e(),e.selectRow.taskStatus?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("任务状态")]),e.taskStatusOpt.length>1?a("el-select",{staticStyle:{width:"120px"},attrs:{"value-key":"label"},on:{change:e.changeTaskStatus},model:{value:e.taskStatus,callback:function(t){e.taskStatus=t},expression:"taskStatus"}},e._l(e.taskStatusOpt,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e}})})),1):e._e()],1):e._e(),e.selectRow.allTaskDepartment?a("div",{staticClass:"rests-form-item m-r-20 m-b-6"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("牵头部门")]),a("el-autocomplete",{attrs:{"fetch-suggestions":e.queryAllTaskDepartment,placeholder:"请输入牵头部门"},on:{select:e.changeAllTaskDepartment},model:{value:e.department,callback:function(t){e.department=t},expression:"department"}})],1):e._e(),e.selectRow.chargeValue?a("div",{staticClass:"rests-form-item m-r-20 align-center m-b-6"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("责任人")]),a("el-autocomplete",{attrs:{"fetch-suggestions":e.queryAllTaskInCharge,placeholder:"请输入责任人"},on:{select:e.changeAllTaskInCharge},model:{value:e.charge,callback:function(t){e.charge=t},expression:"charge"}})],1):e._e(),e.selectRow.taskName?a("div",{staticClass:"rests-form-item m-r-20 m-b-6"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("任务名称")]),a("el-input",{on:{input:e.changeTaskName},model:{value:e.taskName,callback:function(t){e.taskName=t},expression:"taskName"}})],1):e._e(),e.selectRow.emotionAttribute?a("div",{staticClass:"rests-form-item m-r-10"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("情感属性")]),a("el-select",{staticStyle:{width:"120px","margin-right":"5px"},attrs:{"value-key":"label"},on:{change:e.changeEmotionAttribute},model:{value:e.emotionAttribute,callback:function(t){e.emotionAttribute=t},expression:"emotionAttribute"}},e._l(e.emotionAttributeOpt,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.clarity?a("div",{staticClass:"rests-form-item m-r-10"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("清晰/模糊")]),a("el-select",{staticStyle:{width:"120px","margin-right":"5px"},attrs:{"value-key":"label"},on:{change:e.changeClarity},model:{value:e.clarity,callback:function(t){e.clarity=t},expression:"clarity"}},e._l(e.clarityOpt,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.field?a("div",{staticClass:"rests-form-item m-r-10"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("所属领域")]),a("el-select",{staticStyle:{width:"120px","margin-right":"5px"},attrs:{"value-key":"label"},on:{change:e.changeField},model:{value:e.field,callback:function(t){e.field=t},expression:"field"}},e._l(e.fieldOpt,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.errorCorrectionStatus?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("纠错状态")]),a("el-select",{on:{change:function(t){return e.$emit("change",{})}},model:{value:e.correctStatus,callback:function(t){e.correctStatus=t},expression:"correctStatus"}},e._l(e.errorCorrectionStatusOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.commonDate?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("最后变更时间")]),a("common-date",e._b({ref:"date",attrs:{"dateseg-list":e.datesegList,selectRow:e.selectRow.commonDate},on:{confirm:e.handleDateChange}},"common-date",e.$attrs,!1))],1):e._e()])},x=[],k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",disabled:!e.sourcesValue.length,placement:"top-start"}},[a("div",{attrs:{slot:"content"},slot:"content"},e._l(e.sourcesValue,(function(t,s){return a("span",{key:s},[e._v(e._s(t)+e._s(s<e.sourcesValue.length-1?"，":""))])})),0),a("el-cascader",{ref:"dataSources",staticClass:"xmy-data-sources",attrs:{filterable:!0,options:e.options,props:e.props,placeholder:"不限",size:"mini","collapse-tags":"",clearable:"",leafOnly:!1},on:{change:e.change},model:{value:e.sourcesValue,callback:function(t){e.sourcesValue=t},expression:"sourcesValue"}})],1)},C=[],T=a(3822),A={data(){return{props:{multiple:!0,emitPath:!1},options:[],sourcesValue:[]}},props:{sources:{type:String,default:()=>""},data:{type:Array,default:()=>[]}},computed:(0,T.rn)({sourceList:e=>JSON.parse(JSON.stringify(e.sourceList))}),mounted(){this.data&&(this.sourcesValue=JSON.parse(JSON.stringify(this.data)),this.iniSourceList(),this.$nextTick((()=>{this.sourcesValue.length&&this.change()})))},methods:{iniSourceList(){var e;let t=[],a=[];null===(e=this.sourceList)||void 0===e||e.forEach((e=>{e.value=e.dataSource,e.label=e.dataSource,"否"===e.isOuter?t.push({...e}):"是"===e.isOuter&&a.push({...e})})),this.sources?"nei"===this.sources?this.options=[{label:"内部",value:"nei",children:t}]:"wai"===this.sources&&(this.options=[{label:"外部",value:"wai",children:a}]):this.options=[{label:"内部",value:"nei",children:t},{label:"外部",value:"wai",children:a}]},change(){this.$emit("change",this.sourcesValue)},reset(){this.iniSourceList()}}},_=A,N=(0,m.Z)(_,k,C,!1,null,"510382f1",null),I=N.exports,O={components:{dataSources:I,CommonDate:p},data(){return{datesegList:"1|1|1|1|0|0|0",date:{},taskStatus:null,taskType:null,taskStatusOpt:[{label:"全部",value:"100"},{label:"进行中",value:"0"},{label:"已完成",value:"9"},{label:"已中止",value:"-1"},{label:"普通风险",value:"1"},{label:"超时/严重风险",value:"2"}],taskTypeOpt:[{label:"全部",value:"all"},{label:"我负责的",value:"inCharge"},{label:"我支持的",value:"support "},{label:"我分配的",value:"assign"},{label:"我确认的",value:"confirm"},{label:"我阅示的",value:"read"}],taskName:"",department:"",departmentObj:null,charge:"",chargeValue:[],chargeObj:{},allTaskDepartment:[],allTaskInCharge:[],screenList:{},emotionAttribute:null,emotionAttributeOpt:[{label:"不限",value:""},{label:"正面",value:"正面"},{label:"中性",value:"中性"},{label:"负面",value:"负面"}],clarity:null,clarityOpt:[{label:"不限",value:""},{label:"清晰",value:"清晰"},{label:"模糊",value:"模糊"}],field:null,fieldOpt:[{label:"不限",value:""},{label:"产品设计",value:"产品设计"},{label:"产品质量",value:"产品质量"},{label:"营销服务",value:"营销服务"}],correctStatus:null,errorCorrectionStatusOpt:[{label:"不限",value:"100"},{label:"待处理",value:"0"},{label:"已处理",value:"1"}],defaultData:{emotionAttribute:"",clarity:"",field:"",errorCorrectionStatus:"100",taskStatus:{label:"全部",value:100},taskName:"",taskType:{label:"全部",value:"all"}}}},props:{selectRow:{type:Object,default:()=>{}}},computed:{...(0,T.rn)({allTaskDepartmentList:e=>JSON.parse(JSON.stringify(e.allTaskDepartmentList)),allTaskInChargeList:e=>JSON.parse(JSON.stringify(e.allTaskInChargeList))})},created(){this.selectRow.commonDate&&this.selectRow.commonDate.datesegList&&(this.datesegList=this.selectRow.commonDate.datesegList)},mounted(){var e,t,a,s,r,i,n,l,o,d,c,h,m,u;(this.allTaskDepartment=this.allTaskDepartmentList||[],this.allTaskInCharge=this.allTaskInChargeList||[],this.selectRow.allTaskDepartment)&&(this.departmentObj=null!==(e=null===(t=this.selectRow.allTaskDepartment)||void 0===t?void 0:t.defaultValue)&&void 0!==e?e:{},this.department=this.departmentObj.value||"");this.selectRow.chargeValue&&(this.chargeObj=null!==(a=null===(s=this.selectRow.chargeValue)||void 0===s?void 0:s.defaultValue)&&void 0!==a?a:{},this.charge=this.chargeObj.value||"");this.selectRow.taskName&&(this.taskName=null!==(r=null===(i=this.selectRow.taskName)||void 0===i?void 0:i.defaultValue)&&void 0!==r?r:"");this.selectRow.emotionAttribute&&(this.emotionAttribute=null!==(n=null===(l=this.selectRow.emotionAttribute)||void 0===l?void 0:l.defaultValue)&&void 0!==n?n:"");this.selectRow.clarity&&(this.clarity=null!==(o=null===(d=this.selectRow.clarity)||void 0===d?void 0:d.defaultValue)&&void 0!==o?o:"");if(this.selectRow.taskStatus){var p;let e={100:{label:"全部",value:"100"},0:{label:"进行中",value:"0"},9:{label:"已完成",value:"9"},"-1":{label:"已中止",value:"-1"},1:{label:"普通风险",value:"1"},2:{label:"超时/严重风险",value:"2"}},t=null===(p=this.selectRow.taskStatus)||void 0===p?void 0:p.defaultValue;this.taskStatus=t?e[t]:e["100"]}if(this.selectRow.taskType){var y;let e={all:{label:"全部",value:"all"},inCharge:{label:"我负责的",value:"inCharge"},support:{label:"我支持的",value:"support"},assign:{label:"我分配的",value:"assign"},confirm:{label:"我确认的",value:"confirm"},read:{label:"我阅示的",value:"read"}},t=null===(y=this.selectRow.taskType)||void 0===y?void 0:y.defaultValue;this.taskType=t?e[t]:e["all"]}this.selectRow.field&&(this.field=null!==(c=null===(h=this.selectRow.field)||void 0===h?void 0:h.defaultValue)&&void 0!==c?c:"");this.selectRow.errorCorrectionStatus&&(this.correctStatus=null!==(m=null===(u=this.selectRow.errorCorrectionStatus)||void 0===u?void 0:u.defaultValue)&&void 0!==m?m:"100");this.handleDateChange()},methods:{handleDateChange(){var e,t;this.date=null===(e=this.$refs.date)||void 0===e?void 0:e.getDate(),this.date=null!==(t=this.date)&&void 0!==t?t:{},this.$emit("change",{})},changeTaskStatus(e){this.$emit("change",{})},changeTaskType(e){this.$emit("change")},queryAllTaskDepartment(e,t){var a=e?this.allTaskDepartment:this.allTaskDepartmentList;this.querySearchAsync(e,t,a)},queryAllTaskInCharge(e,t){var a=e?this.allTaskInCharge:this.allTaskInChargeList;this.querySearchAsync(e,t,a)},querySearchAsync(e,t,a){var s,r=a.some((t=>t.value===e));s=r?a:a.filter(this.createStateFilter(e)),t(s)},createStateFilter(e){return t=>t.value.indexOf(e)>-1},changeAllTaskDepartment(e){this.departmentObj=e,this.$emit("change",{})},changeAllTaskInCharge(e){this.chargeObj=e,this.$emit("change",{})},changeTaskName(e){this.$emit("change",{})},changeEmotionAttribute(e){this.$emit("change",{emotionAttribute:e})},changeClarity(e){this.$emit("change",{clarity:e})},changeField(e){this.$emit("change",{field:e})},getValue(){var e,t,a,s,r,i;return{...this.date,emotionAttribute:this.emotionAttribute,clarity:this.clarity,field:this.field,correctStatus:this.correctStatus,taskName:this.taskName,taskStatus:null===(e=this.taskStatus)||void 0===e?void 0:e.value,departmentId:(null===(t=this.departmentObj)||void 0===t?void 0:t.key)||null,departmentName:(null===(a=this.departmentObj)||void 0===a?void 0:a.value)||null,inChargeId:(null===(s=this.chargeObj)||void 0===s?void 0:s.key)||null,inChargeName:(null===(r=this.chargeObj)||void 0===r?void 0:r.value)||null,taskType:null===(i=this.taskType)||void 0===i?void 0:i.value}},resetItem(e){"allTaskDepartment"===e&&(this.department="",this.departmentObj={}),"chargeValue"===e&&(this.charge="",this.chargeObj={}),"date"===e?this.$refs[e].reset():this[e]=this.defaultData[e],this.$emit("change")}}},R=O,E=(0,m.Z)(R,D,x,!1,null,"fec06260",null),L=E.exports,M=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"header",staticClass:"car-series-header flex align-center"},[a("div",{staticClass:"car-series-header-left"},[a("el-input",{staticClass:"car-series-search",attrs:{placeholder:"series"===e.selectRow.type?"请输入车系":"请输入品牌","suffix-icon":"el-icon-search"},on:{input:e.filterEl},model:{value:e.textFilter,callback:function(t){e.textFilter=t},expression:"textFilter"}}),a("span",{class:e.checkAll?"all active":"all",on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.handleCheckAllChange.apply(null,arguments)}}},[e._v("不限")]),a("span",{staticClass:"hot-label m-l-20"},[e._v(e._s("series"===e.selectRow.type?"热门车系：":"热门品牌："))])],1),a("div",{staticClass:"car-series-header-right",style:e.headerRightWidth},[e.hotSeries.length&&"series"===e.selectRow.type?a("div",{staticClass:"hot-item-list"},e._l(e.hotSeries,(function(t){return a("span",{key:t.seriesCode,class:t.clicked?"hot-item m-r-12 active":"hot-item m-r-12",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseHot(t)}}},[e._v(e._s(t.seriesName))])})),0):e._e(),e.hotBrand.length&&"brand"===e.selectRow.type?a("div",{staticClass:"hot-item-list"},e._l(e.hotBrand,(function(t){return a("span",{key:t.brandCode,class:t.clicked?"hot-item m-r-12 active":"hot-item m-r-12",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseHot(t)}}},[t.brandLogo?a("img",{staticClass:"m-r-4",staticStyle:{width:"20px","vertical-align":"text-top"},attrs:{src:t.brandLogo,alt:""}}):e._e(),e._v(e._s(t.brandName))])})),0):e._e()]),a("span",{staticClass:"f-s-14 l-h-22 f-color-2 w-62 more-cursor more-space",on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.openMore.apply(null,arguments)}}},[e._v("更多 "),a("i",{class:e.showMore?"el-icon-arrow-up m-l-5":"el-icon-arrow-down m-l-5"})])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.showMore,expression:"showMore"}],staticClass:"car-series-board"},[a("div",{staticClass:"car-brand-board brand-board m-t-20 m-b-10"},[a("span",{staticClass:"triangle"}),a("div",{staticStyle:{"column-span":"all",height:"20px"}}),e._l(e.carSeriesAndBrandArr,(function(t,s){return a("div",{directives:[{name:"show",rawName:"v-show",value:!t.isFilterEmpty,expression:"!item.isFilterEmpty"}],key:s,staticClass:"brand-board-box"},[a("div",{staticClass:"brand-board-letter"},[e._v(e._s(t.letter))]),a("div",{staticClass:"brand-board-center"},["series"===e.selectRow.type?a("div",e._l(t.arr,(function(t,r){return a("el-popover",{directives:[{name:"show",rawName:"v-show",value:!t.hidden,expression:"!val.hidden"}],key:r,attrs:{placement:"bottom-start",width:"200",trigger:"hover","open-delay":500},on:{show:function(a){return e.choose(t,s,r)},hide:e.hidePopOver}},[a("div",{staticClass:"series-box"},[e.checkedBrandSeriesArr.length?[a("el-input",{staticClass:"m-b-6 filter-series-input",staticStyle:{height:"24px"},attrs:{size:"small","suffix-icon":"el-icon-search"},on:{input:e.filterseriesEl},model:{value:e.filterSeriesValue,callback:function(t){e.filterSeriesValue=t},expression:"filterSeriesValue"}}),e.checkedBrandSeriesArr.length&&!e.filterSeriesValue?a("el-checkbox",{attrs:{indeterminate:e.isSeriesIndeterminate},on:{change:e.handleSeriesCheckAllChange},model:{value:e.seriesCheckAll,callback:function(t){e.seriesCheckAll=t},expression:"seriesCheckAll"}},[e._v("全选")]):e._e(),e.checkedBrandSeriesArr.length?a("el-checkbox-group",{on:{change:e.handleCheckedSeriesChange},model:{value:e.checkedSeries,callback:function(t){e.checkedSeries=t},expression:"checkedSeries"}},e._l(e.checkedBrandSeriesArr,(function(t){return a("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.hidden,expression:"!series.hidden"}],key:t.seriesCode,attrs:{label:t.seriesCode,disabled:t.disabled}},[e._v(e._s(t.seriesName))])})),1):e._e()]:a("no-data",{attrs:{minHeight:0}})],2),a("span",{class:t.clicked?"brand-board-value active":"brand-board-value",attrs:{slot:"reference"},on:{click:function(a){return e.chooseBrand(t)}},slot:"reference"},[e._v(" "+e._s(t.brandName)+" "),e.badgeNum(t)?a("span",{staticClass:"brand-board-badge"},[e._v(" +"+e._s(e.badgeNum(t)))]):e._e()])])})),1):e._e(),"brand"===e.selectRow.type?a("div",{staticClass:"brand-board-center"},e._l(t.arr,(function(t,s){return a("div",{directives:[{name:"show",rawName:"v-show",value:!t.hidden,expression:"!val.hidden"}],key:s},[a("span",{class:t.clicked?"brand-board-value active":"brand-board-value",attrs:{slot:"reference"},on:{click:function(a){return e.chooseBrand(t)}},slot:"reference"},[e._v(e._s(t.brandName)+" ")])])})),0):e._e()])])}))],2)])])},V=[],B={props:{selectRow:{type:Object},marketArr:{type:Array,default:()=>[]}},data(){return{textFilter:"",marketString:"",showMore:!1,checkAll:!1,hotSeries:[],carSeriesAndBrandArr:[],checkedBrandSeriesArr:[],isSeriesIndeterminate:!0,seriesCheckAll:!1,checkedSeries:[],activeLetterIndex:-1,activeBrandIndex:-1,hotBrand:[],defaultID:[],brandNames:[],seriesNames:[],filterValue:"",filterSeriesValue:"",resetFlag:!1}},computed:{headerRightWidth(){return{width:"calc(100% - 360px)"}},...(0,T.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[],seriesList:e=>e.seriesList&&e.seriesList.length?JSON.parse(JSON.stringify(e.seriesList)):[]}),badgeNum(){return function(e){if(!e.clicked)return 0;let t=e.seriesItemList.filter((e=>e.clicked));return t.length}}},watch:{carSeriesAndBrandArr:{handler(e,t){},deep:!0},hotSeries:{handler(e,t){},deep:!0},showMore(e){this.$emit("clickMoreChange",e)},marketArr:{handler(e,t){this.$nextTick((()=>{this.marketString=e.join(","),this.filterEl(this.filterValue)}))},immediate:!0}},created(){},mounted(){this.init()},methods:{init(){var e,t,a;this.defaultID=this.resetFlag?[]:(null===(e=this.selectRow)||void 0===e?void 0:e.defaultID)||[],this.seriesNames=this.resetFlag?[]:(null===(t=this.selectRow)||void 0===t?void 0:t.seriesNames)||[],this.brandNames=this.resetFlag?[]:(null===(a=this.selectRow)||void 0===a?void 0:a.brandNames)||[],"series"===this.selectRow.type?(this.hotSeries=this.seriesList.reduce(((e,t)=>(this.$set(t,"clicked",!1),-1!=this.seriesNames.findIndex((e=>e===t["seriesName"]))&&this.$set(t,"clicked",!0),"1"===t.hot&&e.push({...t}),e)),[]),this.hotSeries.length||(this.hotSeries=this.seriesList.slice(0,4))):(this.hotBrand=this.brandList.reduce(((e,t)=>(this.$set(t,"clicked",!1),-1!=this.brandNames.findIndex((e=>e===t["brandName"]))&&this.$set(t,"clicked",!0),"1"===t.hot&&e.push({...t}),e)),[]),this.hotBrand.length||(this.hotBrand=this.brandList.slice(0,4))),this.carSeriesAndBrandArr=this.mapBoardArr(this.brandList,this.seriesList),this.checkAll=0==this.seriesNames.length&&0===this.brandNames.length},mapBoardArr(e=[],t=[]){let a=e.reduce(((e,a)=>(this.$set(a,"brandFirstLetter",a["brandFirstLetter"].toUpperCase()),this.$set(a,"clicked",!1),a["seriesItemList"]=[],e[a["brandFirstLetter"]]?e[a["brandFirstLetter"]].push(a):e={...e,[a["brandFirstLetter"]]:[a]},"series"===this.selectRow.type&&t.forEach((e=>{e["brandName"]==a["brandName"]&&(-1!=this.seriesNames.findIndex((t=>t==e["seriesName"]))?(this.$set(e,"clicked",!0),this.$set(a,"clicked",!0)):this.$set(e,"clicked",!1),a["seriesItemList"].push(e))})),-1!=this.brandNames.findIndex((e=>e==a["brandName"]))&&this.$set(a,"clicked",!0),e)),{}),s=Object.keys(a).sort(),r=[];for(let i=0;i<s.length;i++)r.push({letter:s[i],arr:a[s[i]]});return r},getValue(){let e=[],t=[];return e=this.brandList.filter((e=>e.clicked))||[],t=this.seriesList.filter((e=>e.clicked))||[],this.resetFlag=!1,this.checkAll?{brand:[],series:[],model:[]}:{brand:e,series:t,model:[]}},filterEl(e){if(e&&!this.showMore&&(this.showMore=!0),this.filterValue=e,e)this.carSeriesAndBrandArr.forEach(((t,a)=>{let s=0;const r=t["arr"];r.forEach((a=>{let i=0;PinyinMatch.match(a["brandName"],e)?a["hidden"]=!1:(a["hidden"]=!0,s++),t.isFilterEmpty=s==r.length;const n=a["seriesItemList"];a.isFilterEmpty=!0,n.forEach((t=>{PinyinMatch.match(t["seriesName"],e)?(t["hidden"]=!1,a["hidden"]=!1):(t["hidden"]=!0,i++),this.marketString?t.disabled=!this.marketString.match(t["marketId"]):t.disabled=!1,a.isFilterEmpty=i==n.length})),-1!=r.findIndex((e=>0==e["isFilterEmpty"]))&&(t.isFilterEmpty=!1)})),this.$set(this.carSeriesAndBrandArr,a,t)}));else for(let t=0;t<this.carSeriesAndBrandArr.length;t++){const e=this.carSeriesAndBrandArr[t]["arr"];for(let t=0;t<e.length;t++){const a=e[t];a.hidden=!1;const s=a["seriesItemList"];for(let e=0;e<s.length;e++){const t=s[e];t.hidden=!1,this.marketString?t.disabled=!this.marketString.match(t["marketId"]):t.disabled=!1}a.isFilterEmpty=!1}this.carSeriesAndBrandArr[t].isFilterEmpty=!1}},filterseriesEl(e){this.filterSeriesValue=e,e?this.checkedBrandSeriesArr.forEach((t=>{PinyinMatch.match(t["seriesName"],e)?t["hidden"]=!1:t["hidden"]=!0,this.marketString?t.disabled=!this.marketString.match(t["marketId"]):t.disabled=!1})):this.checkedBrandSeriesArr.forEach((e=>{e["hidden"]=!1,this.marketString?e.disabled=!this.marketString.match(e["marketId"]):e.disabled=!1}))},choose(e,t,a){this.checkAll&&(this.checkAll=!1),this.activeLetterIndex=t,this.activeBrandIndex=a,this.checkedBrandSeriesArr=e["seriesItemList"];const s=this.checkedBrandSeriesArr.filter((e=>e["clicked"]));s.length==this.checkedBrandSeriesArr.length?this.seriesCheckAll=!0:this.seriesCheckAll=!1,this.checkedSeries=this.checkedBrandSeriesArr.filter((e=>e["clicked"])).map((e=>e["seriesCode"]))},chooseHot(e){this.checkAll&&(this.checkAll=!1);const t=e["clicked"];this.$set(e,"clicked",!t),this.carSeriesAndBrandArr.forEach((a=>{const s=a["arr"];s.forEach((a=>{const s=a["seriesItemList"];let r=!1;"series"===this.selectRow.type?s.forEach((a=>{a["seriesCode"]===e["seriesCode"]?(this.$set(a,"clicked",!t),a["clicked"]&&(r=!0)):a["clicked"]&&(r=!0)})):(e.brandCode===a.brandCode&&this.$set(a,"clicked",!t),a.clicked&&(r=!0)),this.$set(a,"clicked",r)}))})),this.$emit("change")},refreshShowData(){this.init()},openMore(){this.showMore=!this.showMore},handleCheckAllChange(){this.checkAll||(this.checkAll=!this.checkAll,this.checkAll&&(this.carSeriesAndBrandArr.forEach((e=>{const t=e["arr"];t.forEach((e=>{const t=e["seriesItemList"];t.forEach((e=>{this.$set(e,"clicked",!1)})),this.$set(e,"clicked",!1)}))})),"series"===this.selectRow.type?this.hotSeries.forEach((e=>{this.$set(e,"clicked",!1)})):this.hotBrand.forEach((e=>{this.$set(e,"clicked",!1)}))),this.showMore=!1,this.$emit("change"))},handleSeriesCheckAllChange(e){e?this.checkedSeries=this.checkedBrandSeriesArr.filter((e=>{var t;return null!==(t=!e.disabled)&&void 0!==t?t:e})).map((t=>(this.$set(t,"clicked",e),t["seriesCode"]))):(this.checkedBrandSeriesArr.forEach((t=>{this.$set(t,"clicked",e)})),this.checkedSeries=[]),this.$set(this.carSeriesAndBrandArr[this.activeLetterIndex]["arr"][this.activeBrandIndex],"clicked",e);const t=this.carSeriesAndBrandArr[this.activeLetterIndex]["arr"][this.activeBrandIndex]["seriesItemList"];this.hotSeries.forEach((a=>{-1!==t.findIndex((e=>e["seriesCode"]==a["seriesCode"]))&&this.$set(a,"clicked",e)})),this.isSeriesIndeterminate=!1,this.$emit("change")},handleCheckedSeriesChange(e){let t=e.length;this.seriesCheckAll=t===this.checkedBrandSeriesArr.length,this.isSeriesIndeterminate=t>0&&t<this.checkedBrandSeriesArr.length,this.checkedBrandSeriesArr.forEach((t=>{e.find((e=>e==t["seriesCode"]))?this.$set(t,"clicked",!0):this.$set(t,"clicked",!1)})),this.$set(this.carSeriesAndBrandArr[this.activeLetterIndex]["arr"][this.activeBrandIndex],"clicked",e.length>0);const a=this.carSeriesAndBrandArr[this.activeLetterIndex]["arr"][this.activeBrandIndex]["seriesItemList"];this.hotSeries.forEach((e=>{const t=a.find((t=>t["seriesCode"]===e["seriesCode"]));t&&this.$set(e,"clicked",t["clicked"])})),this.$emit("change")},reset(){this.resetFlag=!0,this.init(),this.$emit("change")},getCheckAllBtnStatus(){return this.checkAll},hidePopOver(){this.activeLetterIndex=-1,this.activeBrandIndex=-1;let e=[];this.carSeriesAndBrandArr.forEach((t=>{const a=t["arr"];a.forEach((t=>{const a=t["seriesItemList"],s=a.filter((e=>e["clicked"]));s.length>0&&e.push(...s)}))}))},chooseBrand(e){if(this.$set(e,"clicked",!e.clicked),"series"==this.selectRow.type){let t=[];t=e.seriesItemList.filter((e=>e.clicked)),t.length&&this.$set(e,"clicked",!0)}for(let t=0;t<this.carSeriesAndBrandArr.length;t++){let e=this.carSeriesAndBrandArr[t].arr.find((e=>!0===e.clicked));if(e){this.checkAll=!1;break}}this.$emit("change")}}},$=B,F=(0,m.Z)($,M,V,!1,null,"64dcb050",null),P=F.exports,Z=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"market-box flex align-center"},[a("div",{staticClass:"car-series-header-right",style:e.headerRightWidth},[e.marketArr.length?a("div",{staticClass:"hot-item-list"},e._l(e.marketArr,(function(t){return a("span",{key:t.id,class:t.clicked?"hot-item m-r-20 active m-b-10":"hot-item m-r-20 m-b-10",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseHot(t)}}},[e._v(e._s(t.value))])})),0):e._e()])])},K=[],j={data(){return{marketArr:[]}},props:{selectRow:{type:Object,default:()=>{}}},computed:{headerRightWidth(){return{width:"100%"}},...(0,T.rn)({marketList:e=>JSON.parse(JSON.stringify(e.marketList))})},created(){this.init()},methods:{init(){var e;let t=(null===(e=this.selectRow)||void 0===e?void 0:e.defaultValue)||[];this.marketList.forEach((e=>{let a=t.filter((t=>t===e.id));this.$set(e,"clicked",!!a.length)})),this.marketArr=JSON.parse(JSON.stringify(this.marketList)),this.marketArr.unshift({id:"",clicked:!t.length,value:"不限"}),this.$emit("change",t)},chooseHot(e){if(this.$set(e,"clicked",!e["clicked"]),!e.id&&e.clicked)this.marketArr.forEach((e=>{this.$set(e,"clicked",!e.id)}));else if(e.clicked){this.$set(this.marketArr[0],"clicked",!1);let e=this.marketArr.filter((e=>e.clicked));e.length===this.marketArr.length-1&&this.marketArr.forEach((e=>{this.$set(e,"clicked",!e.id)}))}let t=this.marketArr.filter((e=>e.clicked&&e.id)).map((e=>e.value));this.$emit("change",JSON.parse(JSON.stringify(t)))},getValue(){let e=[],t=[];return this.marketArr.forEach((a=>{!0===a.clicked&&a.id&&(e.push(a.id),t.push(a.value))})),{marketId:e.join(","),marketName:t.join(",")}},reset(){Object.assign(this.$data,this.$options.data(this)),this.init(),this.$emit("change",[])}}},H=j,J=(0,m.Z)(H,Z,K,!1,null,"65060cca",null),Y=J.exports,W=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{attrs:{size:"mini"},model:{value:e.compare,callback:function(t){e.compare=t},expression:"compare"}},[a("el-radio-button",{attrs:{label:"owne1"}},[e._v("不限")]),a("el-radio-button",{attrs:{label:"owne2"}},[e._v("车主")]),a("el-radio-button",{attrs:{label:"owne3"}},[e._v("非车主")])],1)],1)},U=[],G={data(){return{compare:"owne1"}}},z=G,q=(0,m.Z)(z,W,U,!1,null,null,null),Q=q.exports,X=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{attrs:{size:"mini"},model:{value:e.compare,callback:function(t){e.compare=t},expression:"compare"}},[a("el-radio-button",{attrs:{label:"owne2"}},[e._v("负面提及率")]),a("el-radio-button",{attrs:{label:"owne1"}},[e._v("体验值")])],1)],1)},ee=[],te={data(){return{compare:"owne1"}}},ae=te,se=(0,m.Z)(ae,X,ee,!1,null,null,null),re=se.exports,ie=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"rests-item-box"},[e.selectRow.keyword?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v(" 语料名称")]),a("el-input",{attrs:{placeholder:"请输入语料名称",clearable:""},on:{blur:function(t){return e.$emit("change")}},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1):e._e(),e.selectRow.dataSources?a("div",{staticClass:"rests-form-item m-r-20 align-center"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("数据来源")]),a("data-sources",{key:e.dataSourcesKey,attrs:{sources:e.selectRow.dataSources.sources||"",data:e.dataSources},on:{change:e.dataSourcesChange}})],1):e._e(),e.selectRow.measureIndex?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("度量指标")]),e.measureIndexOpt.length>1?a("el-select",{staticStyle:{width:"120px","margin-right":"5px"},on:{change:e.changeMeasureIndex},model:{value:e.measureIndex,callback:function(t){e.measureIndex=t},expression:"measureIndex"}},e._l(e.measureIndexOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):e._e()],1):e._e(),e.selectRow.carOwner?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("是否车主")]),a("el-select",{staticStyle:{width:"120px","margin-right":"5px"},attrs:{placeholder:"请选择"},on:{change:e.changeCarOwner},model:{value:e.carOwner,callback:function(t){e.carOwner=t},expression:"carOwner"}},e._l(e.carOwnerOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.province?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("省份")]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top",disabled:e.province&&!e.province.length}},[a("span",{attrs:{slot:"content"},slot:"content"},e._l(e.province,(function(t,s){return a("span",{key:s},[e._v(e._s(t)+" "+e._s(s<e.province.length-1?"，":""))])})),0),a("el-select",{staticClass:"m-r-5",staticStyle:{width:"200px"},attrs:{filterable:"",multiple:"","collapse-tags":"",placeholder:"不限"},on:{change:e.changeProvince},model:{value:e.province,callback:function(t){e.province=t},expression:"province"}},e._l(e.provinceList,(function(e){return a("el-option",{key:e.province,attrs:{label:e.province,value:e.province}})})),1)],1)],1):e._e(),e.selectRow.detailNames?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-5"},[e._v(" 关键词名称")]),e.selectRow.detailNames.searchType?a("el-radio-group",{staticClass:"m-r-10",attrs:{size:"mini"},model:{value:e.detailNamesSearchType,callback:function(t){e.detailNamesSearchType=t},expression:"detailNamesSearchType"}},[a("el-radio-button",{attrs:{label:"dim"}},[e._v("模糊搜索")]),a("el-radio-button",{attrs:{label:"accurate"}},[e._v("精确搜索")])],1):e._e(),a("el-input",{attrs:{placeholder:"多个关键词用逗号隔开",clearable:""},on:{blur:e.namesBlur},model:{value:e.detailNames,callback:function(t){e.detailNames=t},expression:"detailNames"}})],1):e._e(),e.selectRow.detailNamesSelect?a("div",{staticClass:"m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v(" 关键词名称")]),a("el-radio-group",{staticClass:"m-r-10 inline-block",attrs:{size:"mini"},model:{value:e.detailNamesSelectSearchType,callback:function(t){e.detailNamesSelectSearchType=t},expression:"detailNamesSelectSearchType"}},e._l(e.detailNamesSelectSearchTypeOption,(function(t,s){return a("el-radio-button",{key:s,attrs:{label:t.value,plain:""}},[e._v(e._s(t.lebel))])})),1),"vague"==e.detailNamesSelectSearchType?[a("el-select",{staticStyle:{width:"50%"},attrs:{clearable:"","popper-class":"standard-keyword-name","value-key":"standardKeywordName","reserve-keyword":"",filterable:"",multiple:"","collapse-tags":"",remote:"","remote-method":e.remoteMethod,placeholder:"请选择标准关键词"},model:{value:e.detailNames,callback:function(t){e.detailNames=t},expression:"detailNames"}},[a("el-button",{staticClass:"right",attrs:{type:"text"},on:{click:e.selectAll}},[e._v("全选")]),a("div",{staticClass:"clear"}),e._l(e.detailNameOptions,(function(t,s){return a("el-option",{key:t.dataId+s,attrs:{label:t.name,value:t.name}},[a("el-tooltip",{attrs:{effect:"dark",content:t.name,placement:"top"}},[a("span",{staticClass:"float-left item"},[e._v(e._s(t.name))])])],1)}))],2),e.detailNames.length?a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("div",{staticClass:"tooltip",attrs:{slot:"content"},slot:"content"},e._l(e.detailNames,(function(t,s){return a("div",{key:s,staticClass:"m-b-5"},[a("span",[e._v(e._s(s+1)+".")]),a("span",[e._v(e._s(t))])])})),0),a("span",{staticClass:"md-icon-more m-l-5 f-s-16"})]):e._e()]:[a("el-input",{staticClass:"inline-block",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入标准关键词，逗号分隔"},model:{value:e.accurateStandardKeyword,callback:function(t){e.accurateStandardKeyword=t},expression:"accurateStandardKeyword"}}),a("span",{staticClass:"md-icon-more m-l-5 f-s-16",on:{click:e.showMoreDialog}}),a("mine-dialog",{attrs:{appendToBody:!0,dialogFormVisible:e.showMore,title:"精准搜索标准关键词",width:"900px",showClose:!0},on:{close:e.closeDialog}},[a("template",{slot:"option"},[a("el-input",{attrs:{clearable:"",placeholder:"请输入标准关键词，逗号分隔",autosize:{minRows:6,maxRows:10},type:"textarea"},model:{value:e.accurateStandardKeyword,callback:function(t){e.accurateStandardKeyword=t},expression:"accurateStandardKeyword"}})],1)],2)]],2):e._e(),e.selectRow.standardKeyword?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v(" 标准关键词")]),a("el-input",{attrs:{placeholder:"请输入标准关键词",clearable:""},on:{blur:function(t){return e.$emit("change")}},model:{value:e.standardKeyword,callback:function(t){e.standardKeyword=t},expression:"standardKeyword"}})],1):e._e(),e.selectRow.department?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("责任部门")]),a("el-autocomplete",{staticClass:"task-select",attrs:{"fetch-suggestions":function(t,a){return e.querySearchAsync(t,a,e.selectRow.department.temp||"detailDepartmentList")},"value-key":"value","label-key":"value",clearable:""},on:{select:e.handleDepartment,change:e.handleDepartmentChange},model:{value:e.department,callback:function(t){e.department=t},expression:"department"}})],1):e._e(),e.selectRow.vocAutocomplete?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("责任人")]),a("el-autocomplete",{staticClass:"task-select",attrs:{"fetch-suggestions":function(t,a){return e.querySearchAsync(t,a,e.selectRow.vocAutocomplete.temp||"detailChargeList")},"value-key":"value","label-key":"value",clearable:""},on:{select:e.handleDuty,change:e.handleDutyChange},model:{value:e.charge,callback:function(t){e.charge=t},expression:"charge"}})],1):e._e(),e.selectRow.subscribeType?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("订阅类型")]),a("el-select",{on:{change:e.subscribeTypeChange},model:{value:e.subscribeType,callback:function(t){e.subscribeType=t},expression:"subscribeType"}},e._l(e.subscribeTypeOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.pushObject?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("推送对象")]),a("el-autocomplete",{staticClass:"task-select",attrs:{"fetch-suggestions":function(t,a){return e.querySearchAsync(t,a,"subscribeCreatorList")},"value-key":"value","label-key":"value",clearable:""},on:{select:e.handleCreate,change:e.handleCreateChange},model:{value:e.createName,callback:function(t){e.createName=t},expression:"createName"}})],1):e._e(),e.selectRow.unMatch?a("div",{staticClass:"rests-form-item m-r-20"},[a("el-checkbox",{staticClass:"m-r-6",model:{value:e.unMatch,callback:function(t){e.unMatch=t},expression:"unMatch"}},[e._v("未匹配观点")])],1):e._e(),e.selectRow.operator?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v(e._s(e.selectRow.operator.name||"操作人"))]),a("el-autocomplete",{staticClass:"task-select",attrs:{"fetch-suggestions":function(t,a){return e.querySearchAsync(t,a,e.selectRow.operator.temp||"detailChargeList")},"value-key":"value","label-key":"value",clearable:""},on:{select:e.handleOperator,change:e.handleOperatorChange},model:{value:e.operator.lastModifierName,callback:function(t){e.$set(e.operator,"lastModifierName",t)},expression:"operator.lastModifierName"}})],1):e._e(),e.selectRow.addType?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("添加类型")]),a("el-select",{on:{change:function(t){return e.$emit("change")}},model:{value:e.addType,callback:function(t){e.addType=t},expression:"addType"}},e._l(e.addTypeOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.dealStatus?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("处理状态")]),a("el-select",{on:{change:function(t){return e.$emit("change")}},model:{value:e.dealStatus,callback:function(t){e.dealStatus=t},expression:"dealStatus"}},e._l(e.dealStatusOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.selectRow.commonDate?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("时间")]),a("common-date",e._b({ref:"date",attrs:{"dateseg-list":e.datesegList,selectRow:e.selectRow.commonDate},on:{confirm:e.handleDateChange}},"common-date",e.$attrs,!1))],1):e._e(),e.selectRow.operatorInterface?a("div",{staticClass:"rests-form-item m-r-20"},[a("span",{staticClass:"rests-form-label m-r-10"},[e._v("操作页面")]),a("el-select",{attrs:{clearable:""},on:{change:e.operatorInterfaceChange},model:{value:e.operatorInterface,callback:function(t){e.operatorInterface=t},expression:"operatorInterface"}},e._l(e.operatorInterfaceOpt,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e()])},ne=[],le=a(8632),oe={components:{CommonDate:p,dataSources:I,vocAutocomplete:le.Z},data(){return{showMore:!1,detailNamesSelectSearchType:"vague",detailNamesSelectSearchTypeOption:[{value:"accurate",lebel:"精确搜索"},{value:"vague",lebel:"模糊搜索"}],accurateStandardKeyword:"",detailNameOptions:[],datesegList:"1|1|1|1|0|0|0",date:{},dataSources:null,dataSourcesKey:0,measureIndex:null,measureIndexOpt:[{label:"负面提及率",value:"negativeMentionRate"},{label:"体验值",value:"experienceValue"}],carOwner:null,carOwnerOpt:[{label:"不限",value:"0"},{label:"车主",value:"1"},{label:"非车主",value:"2"}],province:null,detailNames:[],detailNamesSearchType:"",operator:{},standardKeyword:null,keyword:null,department:null,charge:null,chargeId:null,subscribeType:null,subscribeTypeOpt:[{label:"不限",value:null},{label:"标准关键词订阅",value:"0"},{label:"个性化订阅",value:"1"}],createName:null,createId:null,operatorInterfaceOpt:[],operatorInterface:null,unMatch:null,addType:null,addTypeOpt:[{label:"全部",value:""},{label:"系统跑数",value:"0"},{label:"人工添加",value:"1"},{label:"导入训练",value:"2"},{label:"一键标注",value:"4"}],dealStatus:null,dealStatusOpt:[{label:"待处理",value:"1"},{label:"待审核",value:"2"},{label:"已拉黑",value:"-1"}],defaultData:{carOwner:"0",measureIndex:"experienceValue",province:[],detailNames:[],standardKeyword:"",keyword:"",operator:{},department:"",vocAutocomplete:"",subscribeType:null,dataSources:[],operatorInterface:"",unMatch:!1,addType:"",dealStatus:"1"}}},props:{selectRow:{type:Object,default:()=>{}}},computed:{...(0,T.rn)({sourceList:e=>e.sourceList,provinceList:e=>JSON.parse(JSON.stringify(e.provinceList))})},created(){this.selectRow.commonDate&&this.selectRow.commonDate.datesegList&&(this.datesegList=this.selectRow.commonDate.datesegList)},watch:{},mounted(){var e,t,a,s,r,i,n,l,o,d,c,h,m,u,p,y,g,v,f,b,w,S,D,x;(this.handleDateChange(),this.selectRow.carOwner&&(this.carOwner=this.selectRow.carOwner.defaultValue||"0"),this.selectRow.measureIndex&&(this.measureIndex=this.selectRow.measureIndex.defaultValue||"experienceValue"),this.selectRow.province&&(this.province=this.selectRow.province.defaultValue||[]),this.selectRow.detailNames)&&(this.detailNames=null!==(e=null===(t=this.selectRow.detailNames)||void 0===t?void 0:t.defaultValue)&&void 0!==e?e:"",this.detailNamesSearchType=null!==(a=null===(s=this.selectRow.detailNames)||void 0===s?void 0:s.searchType)&&void 0!==a?a:"");this.selectRow.detailNamesSelect&&(this.detailNames=null!==(r=null===(i=this.selectRow.detailNamesSelect)||void 0===i?void 0:i.defaultValue.split(","))&&void 0!==r?r:[]);this.selectRow.standardKeyword&&(this.standardKeyword=null!==(n=null===(l=this.selectRow.standardKeyword)||void 0===l?void 0:l.defaultValue)&&void 0!==n?n:"");this.selectRow.keyword&&(this.keyword=null!==(o=null===(d=this.selectRow.keyword)||void 0===d?void 0:d.defaultValue)&&void 0!==o?o:"");this.selectRow.operator&&(this.operator=null!==(c=null===(h=this.selectRow.operator)||void 0===h?void 0:h.defaultValue)&&void 0!==c?c:{});this.selectRow.department&&(this.department=null!==(m=null===(u=this.selectRow.department)||void 0===u?void 0:u.defaultValue)&&void 0!==m?m:"");this.selectRow.vocAutocomplete&&(this.charge=null!==(p=null===(y=this.selectRow.vocAutocomplete)||void 0===y?void 0:y.defaultValue)&&void 0!==p?p:"");(this.selectRow.subscribeType&&(this.subscribeType=null),this.selectRow.dataSources&&(this.dataSourcesKey++,this.dataSources=this.selectRow.dataSources.defaultValue||[]),this.selectRow.operatorInterface)&&(this.getOperatorPage(),this.operatorInterface=null!==(g=null===(v=this.selectRow.operatorInterface)||void 0===v?void 0:v.defaultValue)&&void 0!==g?g:"");this.selectRow.unMatch&&(this.unMatch=null!==(f=null===(b=this.selectRow.unMatch)||void 0===b?void 0:b.defaultValue)&&void 0!==f&&f);this.selectRow.addType&&(this.addType=null!==(w=null===(S=this.selectRow.addType)||void 0===S?void 0:S.defaultValue)&&void 0!==w?w:"");this.selectRow.dealStatus&&(this.dealStatus=null!==(D=null===(x=this.selectRow.dealStatus)||void 0===x?void 0:x.defaultValue)&&void 0!==D?D:"1")},methods:{showMoreDialog(){this.showMore=!0},closeDialog(){this.showMore=!1},selectAll(){for(var e=0;e<this.detailNameOptions.length;e++)-1==this.detailNames.indexOf(this.detailNameOptions[e].name)&&this.detailNames.push(this.detailNameOptions[e].name)},async remoteMethod(e){var t=this.$parent.$parent.$parent.params;let{indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i,indexTypeName:n}=t,l={indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i,size:1e3,indexTypeName:n,names:e};this.detailNameOptions=(await this.$store.dispatch("stKeywordSearch",l)).records},changeTimeSeg(){},handleDateChange(){var e,t;this.date=null===(e=this.$refs.date)||void 0===e?void 0:e.getDate(),this.date=null!==(t=this.date)&&void 0!==t?t:{},this.$emit("change")},dataSourcesChange(e){this.dataSources=e,this.getValue(),this.$emit("change")},changeMeasureIndex(e){this.$emit("change")},changeCarOwner(e){this.$emit("change")},changeProvince(e){this.$emit("change")},handleDepartment(e){this.$emit("change")},handleDepartmentChange(e){e||this.$emit("change")},handleDuty(e){this.chargeId=e.id,this.$emit("change")},handleDutyChange(e){e||(this.chargeId="",this.charge="",this.$emit("change"))},handleCreate(e){this.createId=e.id,this.$emit("change")},handleCreateChange(e){e||(this.createName="",this.createId="",this.$emit("change"))},handleOperator(e){this.operator={lastModifierId:e.id,lastModifierName:e.value,creatorName:e.value},this.$emit("change")},handleOperatorChange(e){e||(this.operator={lastModifierId:"",lastModifierName:""},this.$emit("change"))},namesBlur(){this.$emit("change")},subscribeTypeChange(e){this.$emit("change")},getValue(){let e={...this.date,dataSources:this.dataSources,carOwner:this.carOwner,measureIndex:this.measureIndex,provinces:this.province,names:"dim"==this.detailNamesSearchType||""==this.detailNamesSearchType?Array.isArray(this.detailNames)?this.detailNames.join(","):this.detailNames:"",nameList:"accurate"==this.detailNamesSearchType?Array.isArray(this.detailNames)?this.detailNames:""==this.detailNames.trim()?[]:this.detailNames.split(","):[],standardKeyword:this.standardKeyword,keyword:this.keyword,subscribeType:this.subscribeType,department:this.department,chargeId:this.chargeId,charge:this.charge,createId:this.createId,...this.operator,createName:this.createName,operationPage:this.operatorInterface,dealStatus:this.dealStatus,addType:this.addType};return e},async querySearchAsync(e,t,a){const s=await this.$store.dispatch(a,null!==e&&void 0!==e?e:"")||[];var r=s,i=e?r.filter(this.createStateFilter(e)):r;t(i)},createStateFilter(e){return e=>e},async getOperatorPage(){this.operatorInterfaceOpt=await this.$store.dispatch("operationLogAllOperationPage")||[]},operatorInterfaceChange(e){this.$emit("change")},resetItem(e){"date"===e?this.$refs[e].reset():this[e]=this.defaultData[e],"dataSources"===e&&this.dataSourcesKey++,this.$emit("change")}}},de=oe,ce=(0,m.Z)(de,ie,ne,!1,null,"5b475e85",null),he=ce.exports,me=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"header",staticClass:"car-series-header flex"},[a("div",{staticClass:"car-series-header-left"},["series"===e.selectRow.type?a("el-input",{staticClass:"car-series-search m-r-10",attrs:{placeholder:"请输入车系","suffix-icon":"el-icon-search"},on:{input:e.filterEl},model:{value:e.textFilter,callback:function(t){e.textFilter=t},expression:"textFilter"}}):e._e(),e.brandRadio?e._e():a("span",{class:e.unlimitedClass,on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.handleCheckAllChange.apply(null,arguments)}}},[e._v("不限")])],1),a("div",{staticClass:"car-series-header-right flex align-center",style:e.headerRightWidth},[e.brandListOwn.length?a("div",{staticClass:"hot-item-list brand-item-list"},e._l(e.brandListOwn,(function(t,s){return a("span",{key:t.dataId,class:t.clicked?"hot-item m-r-12 m-b-6 active":"hot-item m-b-6 m-r-12",on:{click:function(a){return a.stopPropagation(),function(a){return e.chooseBrand(t,a)}.apply(null,arguments)}}},[t.brandLogo?a("img",{staticClass:"m-r-4",staticStyle:{width:"20px","vertical-align":"text-top"},attrs:{src:t.brandLogo,alt:""}}):e._e(),e._v(" "+e._s(t.brandName))])})),0):e._e()])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.showMore&&("series"===e.selectRow.type||"model"===e.selectRow.type),expression:"showMore && (selectRow.type === 'series' || selectRow.type === 'model')"}],staticClass:"car-series-board"},[a("div",{staticClass:"car-brand-board brand-board m-t-20 m-b-10"},[a("span",{staticClass:"triangle",style:e.triangleStyle}),e.modelFlag?a("div",e._l(e.brandToSeries,(function(t,s){return a("el-popover",{directives:[{name:"show",rawName:"v-show",value:!t.hidden,expression:"!val.hidden"}],key:s,attrs:{placement:"bottom-start",width:"200",trigger:"hover","open-delay":500},on:{show:function(a){return e.chooseModel(t,s)},hide:e.hidePopOver}},[a("div",{staticClass:"series-box"},[e.seriesToModel.length?[e.seriesToModel.length?a("el-checkbox",{on:{change:e.handleModelCheckAllChange},model:{value:e.modelCheckAll,callback:function(t){e.modelCheckAll=t},expression:"modelCheckAll"}},[e._v("全选")]):e._e(),e.seriesToModel.length?a("el-checkbox-group",{on:{change:e.handleCheckedModelChange},model:{value:e.checkedModel,callback:function(t){e.checkedModel=t},expression:"checkedModel"}},e._l(e.seriesToModel,(function(t,s){return a("el-checkbox",{key:s,attrs:{label:t.modelCode}},[e._v(e._s(t.modelName))])})),1):e._e()]:a("no-data",{attrs:{minHeight:0}})],2),a("span",{directives:[{name:"show",rawName:"v-show",value:t.seriesCode&&!t.ishide&&!t.disabled,expression:"val.seriesCode && !val.ishide && !val.disabled"}],class:t.clicked?"brand-board-value active":"brand-board-value",attrs:{slot:"reference"},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseSer(t)}},slot:"reference"},[e._v(e._s(t.seriesName)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.disabled,expression:"val.disabled"}],staticClass:"brand-board-value",staticStyle:{color:"rgba(0, 0, 0, 0.3)"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.seriesName))])])})),1):a("div",e._l(e.brandToSeries,(function(t,s){return a("div",{key:s,staticClass:"brand-board-box"},[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.ishide,expression:"!item.ishide"}],staticStyle:{overflow:"hidden"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:!t.disabled,expression:"!item.disabled"}],class:t.clicked?"brand-board-value active":"brand-board-value",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseSer(t)}}},[e._v(e._s(t.seriesName)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.disabled,expression:"item.disabled"}],staticClass:"brand-board-value",staticStyle:{color:"rgba(0, 0, 0, 0.3)"}},[e._v(e._s(t.seriesName))])])])})),0)])])])},ue=[],pe={props:{selectRow:{type:Object},marketArr:{type:Array,default:()=>[]}},data(){return{textFilter:"",brandListOwn:[],brandToSeries:[],showMore:!1,checkAll:!1,carSeriesAndBrandArr:[],defaultBrandObj:{},modelObj:{},seriesToModel:[],checkedModel:[],isSeriesIndeterminate:!0,modelCheckAll:!1,currentSeries:{},triangleStyle:"",filterValue:"",marketString:"",modelClickSeries:!1,resetFlag:!1}},computed:{headerRightWidth(){return"series"===this.selectRow.type?{width:"calc(100% - 240px)"}:{width:"calc(100% - 60px)"}},unlimitedClass(){let e=this.checkAll?"all active m-r-10 m-b-10":"all m-r-10 m-b-10";return"series"===this.selectRow.type?e+" m-l-20":e},...(0,T.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[],seriesList:e=>e.seriesList&&e.seriesList.length?JSON.parse(JSON.stringify(e.seriesList)):[],modelList:e=>e.modelList&&e.modelList.length?JSON.parse(JSON.stringify(e.modelList)):[]}),brandRadio(){var e;return null!==(e=this.selectRow.radio)&&void 0!==e&&e},modelFlag(){return"model"===this.selectRow.type}},watch:{carSeriesAndBrandArr:{handler(e,t){},deep:!0},seriesList:{handler(e,t){},deep:!0},showMore(e){this.$emit("clickMoreChange",e)},marketArr:{handler(e,t){this.$nextTick((()=>{this.marketString=e.join(","),this.filterRadioTrue(this.filterValue),this.$emit("change")}))},immediate:!0},modelList:{handler(e,t){e&&e.length&&this.$nextTick((()=>{this.setModelList()}))},deep:!0,immediate:!0}},mounted(){this.init()},methods:{init(){this.setCarList()},setCarList(){var e;let t="series"!==this.selectRow.type&&"model"!==this.selectRow.type,a=this.selectRow.brandNames&&!this.resetFlag?this.selectRow.brandNames[0]:"";this.brandListOwn=null===(e=this.brandList)||void 0===e?void 0:e.filter((e=>(this.$set(e,"clicked",!1),"1"===e.nature)));let s=[],r=[];this.resetFlag||(s=Array.isArray(this.selectRow.brandNames)?JSON.parse(JSON.stringify(this.selectRow.brandNames)):[],r=Array.isArray(this.selectRow.seriesNames)?JSON.parse(JSON.stringify(this.selectRow.seriesNames)):[]),this.carSeriesAndBrandArr=[],this.brandListOwn.forEach((e=>{t?this.setDefaultValue(e,s,"brandName"):a&&e.brandName===a&&(this.defaultBrandObj=e),this.seriesList.map((a=>{this.$set(a,"clicked",!1),this.$set(a,"ishide",!1),this.$set(e,"disabled",!1),a.brandName===e.brandName&&(t?this.carSeriesAndBrandArr.push(a):this.carSeriesAndBrandArr.push({...this.setDefaultValue(a,r,"seriesName")}))}))})),this.chooseBrand(this.defaultBrandObj),"series"!==this.selectRow.type||a||(this.checkAll=!0,this.openMore(!1))},setDefaultValue(e,t,a){if(t.length)for(let s=0;s<t.length;s++)if(e[a]===t[s]){this.$set(e,"clicked",!0),t.splice(s,1);break}return e},setModelList(){if(!this.modelFlag)return;this.modelObj={},this.checkedModel=this.resetFlag?[]:this.selectRow.modelNames||[];let e=[];this.resetFlag||(e=Array.isArray(this.selectRow.seriesNames)?JSON.parse(JSON.stringify(this.selectRow.seriesNames)):[]),this.modelList.forEach((t=>{this.$set(t,"clicked",!1),this.checkedModel.find((e=>e==t.modelName))&&e.find((e=>{e===t.seriesCode&&this.$set(t,"clicked",!0)})),this.modelObj[t.seriesCode]?this.modelObj[t.seriesCode].push(t):this.modelObj[t.seriesCode]=[t]})),this.$nextTick((()=>{this.$emit("change")}))},closeModelClicked(e){e&&e.forEach((e=>{this.$set(e,"clicked",!1)}))},getValue(){let e=[],t=[],a=[];return e=this.brandListOwn.filter((e=>e.clicked))||[],t=this.carSeriesAndBrandArr.filter((e=>e.clicked))||[],a=this.modelList.filter((e=>e.clicked))||[],this.resetFlag=!1,this.checkAll?{brand:this.selectRow.emptyBrandFlag?[]:this.brandListOwn,series:[],model:[]}:{brand:e,series:t,model:a}},filterEl(e){this.filterValue=e,this.showMore||(this.showMore=!0),this.brandRadio?this.filterRadioTrue(e):this.filterRadioFalse(e)},filterRadioTrue(e){var t,a;this.carSeriesAndBrandArr.forEach(((t,a)=>{this.marketString?PinyinMatch.match(t["seriesName"],e)&&this.marketString.match(t["marketId"])?this.$set(t,"disabled",!1):(this.$set(t,"disabled",!0),this.$set(t,"clicked",!1),this.modelFlag&&this.closeModelClicked(this.modelObj[t.seriesName])):(PinyinMatch.match(t["seriesName"],e)?this.$set(t,"ishide",!1):this.$set(t,"ishide",!0),this.$set(t,"disabled",!1))})),e&&"不限"===(null===(t=this.brandToSeries[0])||void 0===t?void 0:t.seriesName)?this.$set(this.brandToSeries[0],"ishide",!0):e||"不限"!==(null===(a=this.brandToSeries[0])||void 0===a?void 0:a.seriesName)||this.$set(this.brandToSeries[0],"ishide",!1)},filterRadioFalse(e){if(this.checkAll=!1,this.brandToSeries=[],e){this.carSeriesAndBrandArr.forEach(((t,a)=>{PinyinMatch.match(t["seriesName"],e)&&this.brandToSeries.push(t)})),this.brandToSeriesInsertCheckAll();for(let e=0;e<this.brandListOwn.length;e++)if(this.brandListOwn[e].clicked){this.$set(this.brandListOwn[e],"clicked",!1);break}}else this.brandToSeries=this.carSeriesAndBrandArr},marketFilterSeries(e){if(e&&e.length){const t=e.map((e=>e.id)).join(",");this.brandToSeries.forEach((e=>{t.match(e["marketId"])?this.$set(e,"ishide",!1):this.$set(e,"ishide",!0)}))}else this.filterRadioTrue(this.filterValue)},chooseBrand(e,t){if(this.textFilter="",this.checkAll&&(this.checkAll=!1),e.clicked&&!this.brandRadio){this.$set(e,"clicked",!1),this.openMore(!1);let t=this.brandListOwn.filter((e=>e.clicked));return this.checkAll=!t.length,void this.$emit("change")}if("brand"!==this.selectRow.type){for(let e=0;e<this.brandListOwn.length;e++)this.brandListOwn[e].clicked&&this.$set(this.brandListOwn[e],"clicked",!1);this.brandToSeries=this.carSeriesAndBrandArr.filter((a=>(this.brandRadio&&t&&(this.$set(a,"ishide",!1),this.$set(a,"clicked",!1)),a.brandName===e.brandName))),this.modelFlag&&t&&this.modelList.forEach((e=>{this.$set(e,"clicked",!1)})),this.brandToSeriesInsertCheckAll(),this.$set(e,"clicked",!0),this.$emit("change"),this.openMore(!0),this.chooseSerAll(),this.setTriangleLeft(t)}else this.$set(e,"clicked",!0),this.$emit("change");this.marketString&&this.filterRadioTrue(this.filterValue)},setTriangleLeft(e){e&&this.$nextTick((()=>{const e=document.getElementsByClassName("car-series-board")[0].getBoundingClientRect(),t=document.getElementsByClassName("brand-item-list")[0].getElementsByClassName("hot-item active")[0].getBoundingClientRect(),a=t.x-e.left+t.width/2-10+"px";this.triangleStyle="left:"+a}))},brandToSeriesInsertCheckAll(){this.brandToSeries.length&&this.brandToSeries.unshift({seriesName:"不限",seriesCode:"",clicked:!1})},chooseSer(e){let t=!e.clicked;if("不限"===e.seriesName)this.brandToSeries.forEach((e=>{this.$set(e,"clicked",!1)})),this.$set(e,"clicked",!0);else{if(this.modelFlag){let a=this;if(!a.modelClickSeries)return;a.seriesToModel.find((e=>e.clicked))?a.$set(e,"clicked",!0):a.$set(e,"clicked",t)}else this.$set(e,"clicked",t);t||"不限"!==this.brandToSeries[0].seriesName?this.chooseSerAll():this.$set(this.brandToSeries[0],"clicked",!1)}const a=this.carSeriesAndBrandArr.filter((e=>!0===e.clicked));this.$emit("change",a)},chooseSerAll(){if(this.brandToSeries.length&&"不限"===this.brandToSeries[0].seriesName){let e=JSON.parse(JSON.stringify(this.brandToSeries));e.shift(0);let t=0;e.forEach((e=>{e.clicked&&(t+=1)})),t==e.length?(this.brandToSeries.forEach((e=>{this.$set(e,"clicked",!1)})),this.$set(this.brandToSeries[0],"clicked",!0)):0==t?this.$set(this.brandToSeries[0],"clicked",!0):this.$set(this.brandToSeries[0],"clicked",!1)}},openMore(e){this.showMore=e},handleCheckAllChange(){if(!this.checkAll){var e;if(this.checkAll=!this.checkAll,this.openMore(!1),this.filterValue="",this.checkAll)this.brandListOwn.forEach((e=>{e["clicked"]=!1})),null===(e=this.carSeriesAndBrandArr)||void 0===e||e.forEach((e=>{this.$set(e,"clicked",!1)}));this.$emit("change")}},chooseModel(e){this.modelClickSeries=!0,this.seriesToModel=this.modelObj[e.seriesCode]||[],this.currentSeries=e,this.checkedModel=this.seriesToModel.filter((e=>e.clicked)).map((e=>e.modelCode))||[],this.checkedModel.length===this.seriesToModel.length?this.modelCheckAll=!0:this.modelCheckAll=!1},hidePopOver(){this.modelClickSeries=!1,this.modelCheckAll=!1,this.seriesToModel=[]},handleCheckedModelChange(e){e.length===this.seriesToModel.length?this.modelCheckAll=!0:this.modelCheckAll=!1,this.seriesToModel.forEach((t=>{e.find((e=>e===t["modelCode"]))?this.$set(t,"clicked",!0):this.$set(t,"clicked",!1)})),this.$set(this.currentSeries,"clicked",!!e.length),this.$emit("change")},handleModelCheckAllChange(e){this.checkedModel=e?this.seriesToModel.map((e=>e.modelCode)):[],this.seriesToModel.forEach((t=>{this.$set(t,"clicked",e)})),this.$set(this.currentSeries,"clicked",e),this.$emit("change")},reset(){this.resetFlag=!0,this.modelFlag&&this.setModelList(),this.init(!0),this.$emit("change")}}},ye=pe,ge=(0,m.Z)(ye,me,ue,!1,null,"a6e844fc",null),ve=ge.exports,fe=a(7222),be=a(7085),we=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"comparative-group"},[a("el-radio-group",{attrs:{size:"mini"},on:{change:e.cahngeCompare},model:{value:e.compare,callback:function(t){e.compare=t},expression:"compare"}},[e.selectRow.owneBrandHide?e._e():a("el-radio-button",{attrs:{label:"owneBrand"}},[e._v("品牌群组")]),e.selectRow.owneBrandCusHide?e._e():a("el-radio-button",{attrs:{label:"owneBrandCus"}},[e._v("品牌组自定义")]),e.selectRow.owneMarketHide?e._e():a("el-radio-button",{attrs:{label:"owneMarket"}},[e._v("细分市场")])],1),"owneBrand"===e.compare?a("div",{staticClass:"group-form m-l-10"},[a("span",{staticClass:"group-form-label m-r-6"},[e._v("品牌群组")]),a("brand-cluster",{ref:"brandCluster",attrs:{defaultValue:e.selectRow.owneBrandValue},on:{change:e.changeBrandCluster}})],1):e._e(),"owneBrandCus"===e.compare?a("div",{staticClass:"group-form m-l-10"},[a("span",{staticClass:"group-form-label m-r-6"},[e._v("品牌组自定义")]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top",disabled:!e.brandGroupsIds.length}},[a("span",{attrs:{slot:"content"},slot:"content"},e._l(e.brandGroupsIds,(function(t,s){return a("span",{key:s},[e._v(e._s(t)+" "+e._s(s<e.brandGroupsIds.length-1?"，":""))])})),0),a("brand-group",{ref:"brandGroup",attrs:{brandLengthFlag:!1},on:{change:e.brandGroupChange}})],1)],1):e._e(),"owneMarket"===e.compare?a("div",{staticClass:"group-form m-l-10"},[a("span",{staticClass:"group-form-label m-r-6"},[e._v("细分市场")]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top",disabled:!e.marketIds.length}},[a("span",{attrs:{slot:"content"},slot:"content"},e._l(e.marketIds,(function(t,s){return a("span",{key:s},[e._v(e._s(t)+" "+e._s(s<e.marketIds.length-1?"，":""))])})),0),a("el-cascader",{ref:"marketDom",attrs:{placeholder:"不限",options:e.marketList,props:{multiple:!0},filterable:!0,"collapse-tags":"",clearable:"",size:"small"},on:{change:e.marketChange},model:{value:e.marketDomValue,callback:function(t){e.marketDomValue=t},expression:"marketDomValue"}})],1)],1):e._e()],1)},Se=[],De=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",disabled:!e.value.length,placement:"top-start"}},[a("div",{attrs:{slot:"content"},slot:"content"},e._l(e.value,(function(t,s){return a("span",{key:s},[e._v(e._s(t)+e._s(s<e.value.length-1?"，":""))])})),0),a("el-cascader",{ref:"refBrandCluster",attrs:{options:e.brandGroupsOptions,props:e.props,"collapse-tags":"",clearable:"",size:"small","popper-class":"el-select-dropdown-cluster"},on:{change:e.changeBrandCluster},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.data;return[a("div",[a("span",{staticClass:"cluster-item",on:{click:function(t){return t.stopPropagation(),e.showBrand(s)}}},[e._v(" "+e._s(s.label)+" "),a("i",{class:s.clicked?"el-icon-arrow-up":"el-icon-arrow-down"})]),a("div",{directives:[{name:"show",rawName:"v-show",value:s.clicked,expression:"data.clicked"}],staticClass:"cluster-brand-list"},e._l(s.brandList,(function(t,s){return a("span",{key:s},[e._v(" "+e._s(t.brandName)+" ")])})),0)])]}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},xe=[],ke={props:{defaultValue:{type:Array,default:()=>[]}},data(){return{value:[],props:{multiple:!0,checkStrictly:!0,emitPath:!1},brandGroupsOptions:[]}},computed:{...(0,T.rn)({brandGroupsList:e=>JSON.parse(JSON.stringify(e.brandGroupsList))||[]})},created(){this.value=this.defaultValue,this.init()},mounted(){this.changeBrandCluster()},methods:{init(){this.setBrandGroupsOptions()},setBrandGroupsOptions(){this.brandGroupsOptions=[];let e={};this.brandGroupsList.forEach((t=>{e[t.groupName]?e[t.groupName].push({brandCode:t.brandCode,brandName:t.brandName}):e[t.groupName]=[{brandCode:t.brandCode,brandName:t.brandName}]}));for(let t in e)this.brandGroupsOptions.push({label:t,value:t,brandList:e[t],clicked:!1})},changeBrandCluster(e){let t=this.$refs.refBrandCluster.getCheckedNodes(),a=[];t.forEach((e=>{a=a.concat(e.data.brandList)})),this.$emit("change",a)},showBrand(e){this.$set(e,"clicked",!e.clicked)},getValue(){return this.value},reset(){this.value=[],this.init()}}},Ce=ke,Te=(0,m.Z)(Ce,De,xe,!1,null,null,null),Ae=Te.exports,_e=a(9168),Ne={props:{selectRow:{type:Object,default:()=>({})}},data(){return{value:"",compare:"owneBrand",marketList:[],brandGroupsOptionsIds:[],brandGroupsIds:[],marketIds:[],marketDomValue:[]}},components:{brandGroup:_e.Z,brandCluster:Ae},computed:{...(0,T.rn)({market:e=>JSON.parse(JSON.stringify(e.marketList))||[]})},created(){this.iniMarket()},methods:{iniMarket(){this.marketList=this.market.map((e=>({label:e.value,value:e.id})))},cahngeCompare(e){let t={};"owneBrand"===this.compare?t={brandName:this.brandGroupsOptionsIds.join(","),marketName:void 0}:"owneBrandCus"===this.compare?t={brandName:"",marketName:void 0}:"owneMarket"===this.compare&&(t={brandName:"",marketName:""}),this.$emit("change",t)},marketChange(e){this.marketIds=[],e.forEach((e=>{this.marketIds=this.marketIds.concat(e)})),this.$emit("change",{marketName:this.marketIds.join(",")})},brandGroupChange(e){this.brandGroupsIds=e,this.$emit("change",{brandName:this.brandGroupsIds.join(",")})},changeBrandCluster(e){this.brandGroupsOptionsIds=e.map((e=>e.brandName)),this.$emit("change",{brandName:this.brandGroupsOptionsIds.join(",")})},getValue(){return"owneBrand"===this.compare?"品牌群组":"owneBrandCus"===this.compare?"品牌组自定义":"owneMarket"===this.compare?"细分市场":""},reset(){this.compare="owneBrand",this.$emit("change"),this.$refs.brandCluster.reset(),this.$refs.brandGroup.reset(),this.marketDomValue=[]}}},Ie=Ne,Oe=(0,m.Z)(Ie,we,Se,!1,null,"35c8eea4",null),Re=Oe.exports,Ee=a(52),Le=a(1028),Me=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"market-box flex align-center"},[a("div",{staticClass:"car-series-header-right"},[e.provinceArr.length?a("div",{staticClass:"hot-item-list"},e._l(e.provinceArr,(function(t,s){return a("span",{key:s,class:t.clicked?"hot-item m-r-20 active m-b-10":"hot-item m-r-20 m-b-10",on:{click:function(a){return a.stopPropagation(),a.preventDefault(),e.chooseHot(t)}}},[e._v(e._s(t.province))])})),0):e._e()])])},Ve=[],Be={data(){return{provinceArr:[]}},computed:{...(0,T.rn)({provinceList:e=>JSON.parse(JSON.stringify(e.provinceList))})},mounted(){this.init()},methods:{init(){var e,t;let a=(null===(e=this.selectRow)||void 0===e?void 0:e.defaultValue)||[];null===(t=this.provinceList)||void 0===t||t.forEach(((e,t)=>{if(e){let t=a.filter((t=>t===e.id));this.$set(e,"clicked",!!t.length),this.$set(e,"id",e.province),this.provinceArr.push(e)}})),this.provinceArr.unshift({id:"",clicked:!a.length,province:"不限"})},chooseHot(e){if(this.$set(e,"clicked",!e["clicked"]),!e.id&&e.clicked)this.provinceArr.forEach((e=>{this.$set(e,"clicked",!e.id)}));else if(e.clicked){this.$set(this.provinceArr[0],"clicked",!1);let e=this.provinceArr.filter((e=>e.clicked));e.length===this.provinceArr.length-1&&this.provinceArr.forEach((e=>{this.$set(e,"clicked",!e.id)}))}let t=this.provinceArr.filter((e=>e.clicked&&e.id)).map((e=>e.id));this.$emit("change",JSON.parse(JSON.stringify(t)))}}},$e=Be,Fe=(0,m.Z)($e,Me,Ve,!1,null,"6608d438",null),Pe=Fe.exports,Ze=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{attrs:{size:"small"},on:{change:e.change},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[a("el-radio-button",{attrs:{label:"全部",plain:"",disabled:e.btnLoad}}),a("el-radio-button",{attrs:{label:"我的关注",plain:"",disabled:e.btnLoad}})],1)],1)},Ke=[],je={props:{selectRow:{type:Object,default:()=>({})},btnLoad:{type:Boolean,default:()=>!1}},data(){return{radio:"全部"}},mounted(){this.selectRow.defaultValue&&(this.radio=this.selectRow.defaultValue||"全部",this.$nextTick((()=>{this.change(this.radio)})))},methods:{change(e){this.$emit("change",e)},getValue(){return{attentionType:"全部"===this.radio?1:2}}}},He=je,Je=(0,m.Z)(He,Ze,Ke,!1,null,null,null),Ye=Je.exports,We=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{staticClass:"m-r-10 inline-block",attrs:{size:"mini"},on:{change:e.changeGroud},model:{value:e.label,callback:function(t){e.label=t},expression:"label"}},e._l(e.groudOptions,(function(e,t){return a("el-radio-button",{key:t,attrs:{label:e,plain:""}})})),1),"标准关键词搜索"!=e.label?a("el-input",{staticClass:"inline-block",staticStyle:{width:"200px"},attrs:{placeholder:"请输入关键词"},on:{blur:e.changeGroud},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}}):[a("el-select",{staticStyle:{width:"30%"},attrs:{clearable:"","popper-class":"standard-keyword-name","value-key":"standardKeywordName","reserve-keyword":"",filterable:"",multiple:"","collapse-tags":"",remote:"","remote-method":e.remoteMethod,placeholder:"请选择"},model:{value:e.standardKeyword,callback:function(t){e.standardKeyword=t},expression:"standardKeyword"}},[a("el-button",{staticClass:"right",attrs:{type:"text"},on:{click:e.selectAll}},[e._v("全选")]),a("div",{staticClass:"clear"}),e._l(e.options,(function(t,s){return a("el-option",{key:t.dataId+s,attrs:{label:t.name,value:t.name}},[a("el-tooltip",{attrs:{effect:"dark",content:t.name,placement:"top"}},[a("span",{staticClass:"float-left item"},[e._v(e._s(t.name))])])],1)}))],2),e.standardKeyword.length?a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("div",{staticClass:"tooltip",attrs:{slot:"content"},slot:"content"},e._l(e.standardKeyword,(function(t,s){return a("div",{key:s,staticClass:"m-b-5"},[a("span",[e._v(e._s(s+1)+".")]),a("span",[e._v(e._s(t))])])})),0),a("span",{staticClass:"md-icon-more m-l-5 f-s-16"})]):e._e()]],2)},Ue=[],Ge={props:{selectRow:{type:Object,default:{}}},data(){return{value:"",checked:[],label:"精确搜索",standardKeyword:[],options:[]}},computed:{groudOptions(){return this.selectRow.standardKeyword?["标准关键词搜索"]:["精确搜索","模糊搜索"]}},created(){(this.selectRow.value||this.selectRow.standardKeyword)&&(this.value=Array.isArray(this.selectRow.value)?this.selectRow.value.join():this.selectRow.value,this.label=this.selectRow.label||"精确搜索","标准关键词搜索"==this.label&&(this.standardKeyword=this.selectRow.value))},methods:{selectAll(){for(var e=0;e<this.options.length;e++)-1==this.standardKeyword.indexOf(this.options[e].name)&&this.standardKeyword.push(this.options[e].name)},getValue(){return"标准关键词搜索"==this.label?{standardKeywords:this.standardKeyword}:{searchType:"精确搜索"===this.label?1:2,searchKey:this.value}},changeGroud(){this.$emit("change",!1)},async remoteMethod(e){let t=this.$store.state.indexSystemItem,{indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i}=t,n={indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i,size:1e3};n.names=e,n.indexTypeName=t.textName?t.textName[0]:"",this.loading=!0,this.options=(await this.$store.dispatch("stKeywordSearch",n)).records,this.loading=!1},reset(){this.value="",this.label="精确搜索",this.changeGroud()}}},ze=Ge,qe=(0,m.Z)(ze,We,Ue,!1,null,"25f4df29",null),Qe=qe.exports,Xe=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{staticClass:"m-r-10 inline-block",attrs:{size:"mini"},on:{change:e.changeSearchType},model:{value:e.searchType,callback:function(t){e.searchType=t},expression:"searchType"}},e._l(e.searchTypeOption,(function(t,s){return a("el-radio-button",{key:s,attrs:{label:t.value,plain:""}},[e._v(e._s(t.lebel))])})),1),"vague"==e.searchType?[a("el-select",{staticStyle:{width:"300px"},attrs:{clearable:"","popper-class":"standard-keyword-name","value-key":"standardKeywordName","reserve-keyword":"",filterable:"",multiple:"","collapse-tags":"",remote:"","remote-method":e.remoteMethod,placeholder:"请搜索并选择标准关键词"},model:{value:e.standardKeyword,callback:function(t){e.standardKeyword=t},expression:"standardKeyword"}},[a("el-button",{staticClass:"right",attrs:{type:"text"},on:{click:e.selectAll}},[e._v("全选")]),a("div",{staticClass:"clear"}),e._l(e.options,(function(t,s){return a("el-option",{key:t.dataId+s,attrs:{label:t.name,value:t.name}},[a("el-tooltip",{attrs:{effect:"dark",content:t.name,placement:"top"}},[a("span",{staticClass:"float-left item"},[e._v(e._s(t.name))])])],1)}))],2),e.standardKeyword.length?a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("div",{staticClass:"tooltip",attrs:{slot:"content"},slot:"content"},e._l(e.standardKeyword,(function(t,s){return a("div",{key:s,staticClass:"m-b-5"},[a("span",[e._v(e._s(s+1)+".")]),a("span",[e._v(e._s(t))])])})),0),a("span",{staticClass:"md-icon-more m-l-5 f-s-16"})]):e._e()]:[a("el-input",{staticClass:"inline-block",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入标准关键词，英文逗号分隔"},model:{value:e.accurateStandardKeyword,callback:function(t){e.accurateStandardKeyword=t},expression:"accurateStandardKeyword"}}),a("span",{staticClass:"md-icon-more m-l-5 f-s-16",on:{click:e.showMoreDialog}}),a("mine-dialog",{attrs:{appendToBody:!0,dialogFormVisible:e.showMore,title:"精准搜索标准关键词",width:"900px",showClose:!0},on:{close:e.closeDialog}},[a("template",{slot:"option"},[a("el-input",{attrs:{clearable:"",placeholder:"请输入标准关键词，逗号分隔",autosize:{minRows:6,maxRows:10},type:"textarea"},model:{value:e.accurateStandardKeyword,callback:function(t){e.accurateStandardKeyword=t},expression:"accurateStandardKeyword"}})],1)],2)]],2)},et=[],tt={props:{selectRow:{type:Object,default:{}}},watch:{selectRow:{immediate:!0,handler(e){const{standardKeywordsSearchType:t,standardKeywords:a}=e;this.searchType=t||"vague","accurate"==t?this.accurateStandardKeyword=(a||[]).join(","):this.standardKeyword=a||""}}},data(){return{searchType:"accurate",searchTypeOption:[{value:"accurate",lebel:"精确搜索"},{value:"vague",lebel:"模糊搜索"}],standardKeyword:[],options:[],showMore:!1,accurateStandardKeyword:""}},methods:{showMoreDialog(){this.showMore=!0},closeDialog(){this.showMore=!1},getValue(){const e=this.searchType;return"accurate"==this.searchType?{standardKeywordsSearchType:e,standardKeywords:this.accurateStandardKeyword.trim().length>0?this.accurateStandardKeyword.trim().split(","):[]}:{standardKeywordsSearchType:e,standardKeywords:this.standardKeyword}},reset(){},changeSearchType(){this.$emit("change",!1)},async remoteMethod(e){let t=this.$store.state.indexSystemItem,{indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i}=t,n={indexType:a,firstIndexId:s,secondIndexId:r,thirdIndexId:i,size:1e3};n.names=e,n.indexTypeName=t.textName?t.textName[0]:"",this.loading=!0,this.options=(await this.$store.dispatch("stKeywordSearch",n)).records,this.loading=!1},selectAll(){for(var e=0;e<this.options.length;e++)-1==this.standardKeyword.indexOf(this.options[e].name)&&this.standardKeyword.push(this.options[e].name)}}},at=tt,st=(0,m.Z)(at,Xe,et,!1,null,"653c13e5",null),rt=st.exports,it={components:{CommonDate:p,selectedItem:w,indicatorItem:S.Z,screenItem:L,marketSegment:Y,carAllBrandAsSeriesSelect:P,carOwne:Q,measureIndex:re,restsItem:he,carSelfBrandSeriesSelect:ve,selectGroup:fe.Z,seriesCascader:be.Z,comparativeGroup:Re,inputSeriesCascader:Ee.Z,indexSystemGroup:Le.Z,province:Pe,attentionButton:Ye,textSearch:Qe,textSearch1:rt},data(){return{selectRowKey:0,resetFlag:!0,showWrap:!1,attentionValue:"全部",searchJson:{},datesegList:"1|1|1|1|0|0|0",dynamicTags:[],compareArr:[],dataSources:null,taskTypeKey:"all",allTaskDepartmentVal:"",allTaskDepartmentKey:"",allTaskInChargeVal:"",allTaskInChargeKey:"",taskName:"",comparativeObj:{},marketSegmentObj:{},marketArr:[],params:{}}},props:{data:{type:Array,default(){return[]}},taskTypeShow:{type:Boolean,default:()=>!1},selectRow:{type:Object,default(){return{carAllBrandAsSeriesSelect:{hidden:!1,default:[]},commonDate:{hidden:!1},screenItem:{hidden:!1,selectRow:{taskStatus:{},allTaskDepartment:{}}},restsItem:{hidden:!1,selectRow:{carOwne:{},measureIndex:{}}},carBrandAsSeriesSelect:{type:"brand"}}}},initSearch:{type:Boolean,default:()=>!0},indicatorSelect:{type:Boolean,default:()=>!1},selectedShow:{type:Boolean,default:()=>!0},searchBtn:{type:Boolean,default:()=>!0},show:{type:Boolean,default:()=>!1}},computed:{indicatorSelectFlag(){return!!this.indicatorSelect||this.showWrap}},watch:{selectedShow:{handler(e,t){e||(this.showWrap=!0)},immediate:!0}},created(){this.initDateSeg()},mounted(){this.$nextTick((()=>{this.init()}))},methods:{timeoutSelectedChange(){setTimeout((()=>{this.selectedChange(!0,!0)}),0)},init(){this.selectedChange(this.initSearch)},initDateSeg(){this.selectRow.commonDate&&this.selectRow.commonDate.datesegList&&(this.datesegList=this.selectRow.commonDate.datesegList)},reset(){this.selectRowKey++,this.$nextTick((()=>{var e;null===(e=this.$refs.restsItem)||void 0===e||e.getValue()}))},selectedChange(e=!1,t=!1){var a,s,r,i,n,l,o,d,c,h,m,u,p,y,g,v,f,b;this.dynamicTags=[];let w=null===(a=this.$refs.date)||void 0===a?void 0:a.getDate();const S=(null===(s=this.$refs.restsItem)||void 0===s?void 0:s.getValue())||{},D=(null===(r=this.$refs.screenItem)||void 0===r?void 0:r.getValue())||{},x=null===(i=this.$refs.indicator)||void 0===i?void 0:i.getValue(),k=null===(n=this.$refs.comparativeGroup)||void 0===n?void 0:n.getValue(),C=(null===(l=this.$refs.textSearch)||void 0===l?void 0:l.getValue())||{},T=(null===(o=this.$refs.textSearch1)||void 0===o?void 0:o.getValue())||{},A=(null===(d=this.$refs.attention)||void 0===d?void 0:d.getValue())||{};if(x&&(x.unMatch||this.dynamicTags.push({name:`指标体系：${x.textName.join("/")}`,parent:"",itself:"indicator",hidden:!1})),C.searchType||C.standardKeywords){let e=C.searchType?""+(("1"==C.searchType?"精确搜索：":"模糊搜索：")+C.searchKey):`标准关键词搜索：${C.standardKeywords.join(",")}`;this.dynamicTags.push({name:e,parent:"",itself:"textSearch",hidden:!1})}if(T.searchType||T.standardKeywords){let e=T.searchType?""+(("1"==T.searchType?"精确搜索：":"模糊搜索：")+T.searchKey):`标准关键词搜索：${T.standardKeywords.join(",")}`;this.dynamicTags.push({name:e,parent:"",itself:"textSearch1",hidden:!1})}this.getMarketSegment();const _=this.getBrandOrSeriesObj();k&&this.dynamicTags.push({name:`对比组：${k}`,itself:"comparativeGroup",hidden:!1});const N=this.setDataSources(S);this.setRestsItemText(D),this.setRestsItemText(S);let I=null!==w&&void 0!==w?w:S.dateType?JSON.parse(JSON.stringify(S)):"";if(null!==(c=this.selectRow)&&void 0!==c&&null!==(h=c.screenItem)&&void 0!==h&&null!==(m=h.selectRow)&&void 0!==m&&m.commonDate&&(I=JSON.parse(JSON.stringify(D))),I)switch(I.dateType){case"year":this.dynamicTags.push({name:`时间：${this.$moment(I.startDate).format("YYYY")}`,parent:S.dateType?"restsItem":null!==(u=this.selectRow)&&void 0!==u&&null!==(p=u.screenItem)&&void 0!==p&&null!==(y=p.selectRow)&&void 0!==y&&y.commonDate?"screenItem":"",itself:"date",hidden:!1});break;default:var O=this.$moment(I.startDate).format("YYYY-MM-DD"),R=this.$moment(I.endDate).format("YYYY-MM-DD"),E="unlimited"==I.dateType?"不限":`${O} 至 ${R}`;this.dynamicTags.push({name:`时间：${E}`,parent:S.dateType?"restsItem":null!==(g=this.selectRow)&&void 0!==g&&null!==(v=g.screenItem)&&void 0!==v&&null!==(f=v.selectRow)&&void 0!==f&&f.commonDate?"screenItem":"",itself:"date",hidden:!1})}delete S.dataSources,delete S.dateType,delete S.endDate,delete S.startDate;var L={taskStatus:D.taskStatus,departmentId:D.departmentId,endTime:I.endDate,inChargeId:D.inChargeId,startTime:I.startDate,taskName:D.taskName};this.taskTypeShow&&(L["taskType"]=D.taskType);const M={..._,marketNames:void 0==this.marketSegmentObj.marketName?null:""===this.marketSegmentObj.marketName.trim()?[]:this.marketSegmentObj.marketName.split(","),dataSources:N.dataSource?N.dataSource:null,indexType:(null===x||void 0===x?void 0:x.indexType)||null,indexTypeName:null!==x&&void 0!==x&&x.unMatch?null:(null===x||void 0===x?void 0:x.textName[0])||null,firstIndexId:(null===x||void 0===x?void 0:x.firstIndexId)||null,next:(null===x||void 0===x?void 0:x.next)||"firstIndexId",secondIndexId:(null===x||void 0===x?void 0:x.secondIndexId)||null,thirdIndexId:(null===x||void 0===x?void 0:x.thirdIndexId)||null,unMatch:null===x||void 0===x?void 0:x.unMatch,indexReverseFilter:null===x||void 0===x?void 0:x.indexReverseFilter,fourIndexId:(null===x||void 0===x?void 0:x.fourIndexId)||null,searchParams:L,...S,...D,...C,...T,...A,startDate:I.startDate?this.$moment(I.startDate).format("YYYY-MM-DD"):"",endDate:I.endDate?this.$moment(I.endDate).format("YYYY-MM-DD"):"",dateType:I.dateType||"other"};"unlimited"==M.dateType&&(M.startDate="",M.endDate=""),delete M.brandCodes,delete M.seriesCodes,delete M.modelCodes;for(let V in M)"firstIndexId"==V&&"secondIndexId"==V&&"thirdIndexId"==V||(null===M[V]||void 0===M[V])&&delete M[V];null!==(b=this.selectRow.screenItem)&&void 0!==b||delete M.searchParams,this.params=M,e&&this.$emit("search",M,t),e&&!this.show&&(this.showWrap=!1)},setRestsItemText(e){if(e.measureIndex&&this.dynamicTags.push({name:"度量指标："+("negativeMentionRate"==e.measureIndex?"负面提及率":"体验值"),parent:"restsItem",itself:"measureIndex",hidden:!1}),(0==e.carOwner||e.carOwner)&&this.dynamicTags.push({name:"是否车主："+(0==e.carOwner?"不限":1==e.carOwner?"车主":"非车主"),parent:"restsItem",itself:"carOwner",hidden:!1}),e.provinces){let t=null;e.provinces.length||(t="不限"),1===e.provinces.length&&(t=e.provinces[0]),e.provinces.length>1&&(t=e.provinces[0]+"...+"+(e.provinces.length-1)),this.dynamicTags.push({name:`省份：${t}`,parent:"restsItem",itself:"province",allName:e.provinces,hidden:!1})}if((e.names||e.nameList)&&this.dynamicTags.push({name:`关键词名称：${e.names||e.nameList.join(",")}`,parent:"restsItem",itself:"detailNames",hidden:!1}),e.keyword&&this.dynamicTags.push({name:`语料名称：${e.keyword}`,parent:"restsItem",itself:"keyword",hidden:!1}),e.standardKeyword&&this.dynamicTags.push({name:`标准关键词：${e.standardKeyword}`,parent:"restsItem",itself:"standardKeyword",hidden:!1}),e.department&&this.dynamicTags.push({name:`责任部门：${e.department}`,parent:"restsItem",itself:"department",hidden:!1}),e.charge&&this.dynamicTags.push({name:`责任人：${e.charge}`,parent:"restsItem",itself:"charge",hidden:!1}),e.lastModifierName&&this.dynamicTags.push({name:`操作人：${e.lastModifierName}`,parent:"restsItem",itself:"operator",hidden:!1}),"string"===typeof e.emotionAttribute&&this.dynamicTags.push({name:`情感属性：${e.emotionAttribute?e.emotionAttribute:"不限"}`,parent:"screenItem",itself:"emotionAttribute",hidden:!1}),"string"===typeof e.clarity&&this.dynamicTags.push({name:`清晰度：${e.clarity?e.clarity:"不限"}`,parent:"screenItem",itself:"clarity",hidden:!1}),"string"===typeof e.field&&this.dynamicTags.push({name:`所属领域：${e.field?e.field:"不限"}`,parent:"screenItem",itself:"field",hidden:!1}),"string"===typeof e.taskStatus){let t={0:"进行中",1:"普通风险","-1":"已中止",2:"超时/严重风险",9:"已完成",100:"全部"};this.dynamicTags.push({name:`任务状态：${t[e.taskStatus]}`,parent:"screenItem",itself:"taskStatus",hidden:!1})}if("string"===typeof e.taskType){let t={all:"全部",inCharge:"我负责的",support:"我支持的",assign:"我分配的",confirm:"我确认的",read:"我阅示的"};this.dynamicTags.push({name:`任务类型：${t[e.taskType]}`,parent:"screenItem",itself:"taskType",hidden:!1})}"string"===typeof e.departmentId&&this.dynamicTags.push({name:`牵头部门：${e.departmentName}`,parent:"screenItem",itself:"allTaskDepartment",hidden:!1}),"string"===typeof e.inChargeId&&this.dynamicTags.push({name:`责任人：${e.inChargeName}`,parent:"screenItem",itself:"chargeValue",hidden:!1}),"string"===typeof e.taskName&&this.dynamicTags.push({name:`任务名称：${e.taskName}`,parent:"screenItem",itself:"taskName",hidden:!1})},getBrandOrSeriesObj(){var e;let t=[],a=[],s=[],r=[],i=[],n=[],l=[];if(t=null===(e=this.$refs.carBrandAsSeriesSelect)||void 0===e?void 0:e.getValue(),t){let e=null,o=(this.$refs.carBrandAsSeriesSelect.selectRow.type,t.brand),d=t.series,c=t.model;o.forEach((e=>{a.push(e.brandCode),s.push(e.brandName)})),d.forEach((e=>{r.push(e.seriesCode),i.push(e.seriesName)})),c.forEach((e=>{n.push(e.modelCode),l.push(e.modelName)}));let h="";d.length&&(h=i,e=d.length>1?d[0]["seriesName"]+"...+"+(d.length-1):d[0]["seriesName"],this.dynamicTags.push({name:`车系：${e}`,id:"",allName:h,parent:"",itself:"carBrandAsSeriesSelect"})),o.length&&(h=s,e=o.length>1?o[0]["brandName"]+"...+"+(o.length-1):o[0]["brandName"],this.dynamicTags.push({name:`品牌：${e}`,id:"",allName:h,parent:"",itself:"carBrandAsSeriesSelect"})),c.length&&(h=l,e=c.length>1?c[0]["modelName"]+"...+"+(c.length-1):c[0]["modelName"],this.dynamicTags.push({name:`型号：${e}`,id:"",allName:h,parent:"",itself:"carBrandAsSeriesSelect"}))}return this.comparativeObj.brandName&&(s=s.concat(this.comparativeObj.brandName.split(",")),s=Array.from(new Set(s))),{brandNames:s||null,seriesNames:i||null,modelNames:l||null}},getMarketSegment(){var e;let t=null===(e=this.$refs.marketSegment)||void 0===e?void 0:e.getValue();if(this.marketSegmentObj=this.comparativeObj,t){this.marketSegmentObj=t;let e=t.marketName.split(","),a=null;e.length&&e[0]?1==e.length?a=e[0]:e.length>1&&(a=e[0]+"...+"+(e.length-1)):a="不限",this.dynamicTags.push({name:`细分市场：${a}`,parent:"",itself:"marketSegment",hidden:!1})}},screenItemChange(e){this.selectedChange()},restsItemChange(e){this.selectedChange()},setDataSources(e){if(this.dataSources=null===e||void 0===e?void 0:e.dataSources,!this.dataSources)return{};let t=JSON.parse(JSON.stringify(this.dataSources)),a=null,s=this.dataSources.length;a=s?s>1?t[0]+"...+"+(s-1):t[0]:"不限";let r={name:`数据源：${a}`,id:"",parent:"restsItem",allName:t,itself:"dataSources"};return this.dynamicTags.push(r),{dataSource:t}},cutShowWrap(){this.showWrap=!this.showWrap,this.$emit("cutShowWrap")},handleDateChange(){this.selectedChange()},hayndleSeriesChange(){},changeBrandOrSeries(e){this.selectedChange()},changeComparativeGroup(e){console.log(e),e?this.comparativeObj=e:this.selectedChange()},changeMarket(e){this.marketArr=e,this.selectedChange()},changeAttention(e){this.attentionValue=e,this.showWrap=!1,this.selectedChange(!0,!0)},tagRefresh(e,t){this.showWrap=!0,!e&&t?this.$refs[t].reset():e&&t&&this.$refs[e].resetItem(t)}}},nt=it,lt=(0,m.Z)(nt,s,r,!1,null,"0d08d701",null),ot=lt.exports},9168:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block"}},[a("el-popover",{attrs:{placement:"bottom",width:"210",trigger:"click",content:""},on:{hide:e.visibleChange}},[a("div",[a("el-input",{staticClass:"m-b-10",attrs:{placeholder:"请输入关键词"},on:{input:e.filterEl},model:{value:e.inputValue,callback:function(t){e.inputValue=t},expression:"inputValue"}}),a("div",{staticClass:"flex",staticStyle:{overflow:"hidden"}},[a("div",{staticClass:"h-280 letter-big-wrap input-brand-letter",staticStyle:{overflow:"auto"}},[a("ul",{staticClass:"p-l-0 letter-big l-h-24"},e._l(e.letterBig,(function(t,s){return a("li",{key:s,class:e.classIndex===s?"point-hand active":"point-hand",staticStyle:{"list-style-type":"none"},on:{click:function(a){return a.stopPropagation(),e.clickAnchor("toBG"+t,s)}}},[e._v(e._s(t))])})),0)]),a("el-cascader-panel",{ref:"refBrandGroup",attrs:{id:"inputSeriesPanel",props:e.defaultTypeProps,options:e.carSeriesAndBrandArr,"show-all-levels":!1,filterable:"","collapse-tags":"",clearable:""},on:{change:e.brandGroupChange},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.data;return[s.letter?a("span",{attrs:{id:"toBG"+s.label}},[e._v(e._s(s.label))]):e._e()]}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)],1),a("el-cascader",{attrs:{slot:"reference",id:"inputSeriesPanelHide",placeholder:"不限","popper-class":"el-select-dropdown-hide",size:"small",options:e.carSeriesAndBrandArr,props:e.defaultTypeProps,"collapse-tags":"",clearable:""},slot:"reference",model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)],1)},r=[],i=a(3822),n={props:{brandDisabled:{type:String,default:()=>"1"},defaultBrand:{type:Array,default:()=>[]},brandLengthFlag:{type:Boolean,default:()=>!0}},data(){return{inputValue:"",classIndex:0,defaultTypeProps:{label:"brandName",value:"brandName",multiple:!0,checkStrictly:!1},carSeriesAndBrandArr:[],value:[],letterBig:[],brandLists:[]}},watch:{value:{handler(e,t){e.length>7&&this.brandLengthFlag&&(this.$message({message:"最多只能选7个品牌",type:"warning"}),this.$nextTick((()=>{this.value=t})))}}},computed:{...(0,i.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[]})},created(){this.generateBig(),this.value=this.defaultBrand,this.init()},mounted(){let e=document.getElementById("inputSeriesPanel");e.getElementsByClassName("el-cascader-menu__wrap el-scrollbar__wrap")[0].addEventListener("scroll",(()=>{const t=e.getElementsByClassName("el-scrollbar__view el-cascader-menu__list")[0].clientHeight,a=e.getElementsByClassName("el-cascader-menu__wrap el-scrollbar__wrap")[0].scrollTop,s=a/t,r=document.getElementsByClassName("input-brand-letter")[0].getElementsByClassName("letter-big")[0].clientHeight;document.getElementsByClassName("input-brand-letter")[0].scrollTo({top:r*s,left:0,behavior:"smooth"})}))},methods:{init(){this.brandLists=this.mapBoardArr(this.brandList),this.carSeriesAndBrandArr=JSON.parse(JSON.stringify(this.brandLists))},generateBig(){for(var e=65;e<91;e++)this.letterBig.push(String.fromCharCode(e))},clickAnchor(e,t){var a;this.classIndex=t,null===(a=document.getElementById(e))||void 0===a||a.scrollIntoView()},mapBoardArr(e=[]){let t=e.reduce(((e,t)=>(t["brandFirstLetter"]=t["brandFirstLetter"].toUpperCase(),t.nature===this.brandDisabled&&this.$set(t,"disabled",!0),e[t["brandFirstLetter"]]?e[t["brandFirstLetter"]].push(t):e={...e,[t["brandFirstLetter"]]:[t]},e)),{}),a=Object.keys(t).sort(),s=[];for(let i=0;i<a.length;i++)s.push({letter:a[i],arr:t[a[i]]});let r=[];return s.forEach((e=>{var t;r.push({letter:e.letter,label:e.letter,value:e.letter,disabled:!0}),r=r.concat(null!==(t=e.arr)&&void 0!==t?t:[])})),r},filterEl(e){e?(this.carSeriesAndBrandArr=[],this.brandLists.forEach(((t,a)=>{t["brandName"]&&PinyinMatch.match(t["brandName"],e)&&this.carSeriesAndBrandArr.push(t)}))):this.carSeriesAndBrandArr=JSON.parse(JSON.stringify(this.brandLists))},brandGroupChange(e){let t=[];e.forEach((e=>{t=t.concat(e)}))},visibleChange(){let e=[];this.value.forEach((t=>{e=e.concat(t)})),this.$emit("change",e)},reset(){this.value=[],this.init()}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"585f44d2",null),c=d.exports},1028:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("span",[e._v("汇总维度：")]),a("el-select",{staticClass:"m-r-10",staticStyle:{width:"100px"},on:{change:e.rankChange},model:{value:e.indexSystemValue,callback:function(t){e.indexSystemValue=t},expression:"indexSystemValue"}},e._l(e.indexSystem,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("el-cascader",{directives:[{name:"show",rawName:"v-show",value:"一级分类"!=e.indexSystemValue,expression:"indexSystemValue != '一级分类'"}],class:e.borClass,attrs:{props:e.props,options:e.cascaderOptions,size:"mini","popper-class":"el-select-dropdown-sysm",placeholder:"请选择"+e.indexSystemValue,"collapse-tags":"",clearable:""},on:{change:e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},r=[],i=a(3822),n={data(){return{indexSystemValue:"一级分类",value:"",props:{value:"id",label:"indexName"},options:[],cascaderOptions:[],indexSystem:[{label:"一级分类",value:"一级分类"},{label:"二级分类",value:"二级分类"},{label:"三级分类",value:"三级分类"}],firstIndexId:"",borColor:"borColor"}},props:{defaultValue:{type:String,default:()=>"0"},dataTier:{type:String,default:()=>""}},watch:{indexSystemValue(e){"二级分类"===e&&this.init(0,0),"三级分类"===e&&this.init(0,1)}},computed:{...(0,i.rn)({indexSystemList:e=>e.indexSystemList&&e.indexSystemList.length?JSON.parse(JSON.stringify(e.indexSystemList)):[]}),borClass(){return this.value?"":this.borColor}},created(){"服务"===this.dataTier&&(this.indexSystem.shift(),this.indexSystemValue="二级分类"),this.initData(this.indexSystemList),this.init(0,0)},methods:{init(e,t){this.cascaderOptions=JSON.parse(JSON.stringify(this.options)),this.setOPtions(this.cascaderOptions,e,t)},initData(e){for(let t=0;t<e.length;t++)if(this.defaultValue===e[t].id){if(!this.dataTier){this.options=e[t].children;break}let a=e[t].children;for(let e=0;e<a.length;e++)if(a[e].indexName===this.dataTier){this.firstIndexId=a[e].id,this.options=a[e].children;break}}},setOPtions(e,t,a){e.forEach((e=>{if(t===a)e.children=null;else if(e.children.length){let s=0;this.setOPtions(e.children,s=t+1,a)}else e.children.length||(e.children=null)}))},handleChange(e){e.length||(this.value="");var t={firstIndexId:this.firstIndexId?this.firstIndexId:e[0]||"",secondIndexId:this.firstIndexId?e[0]||"":e[1]||"",thirdIndexId:this.firstIndexId&&e[1]||""};t.firstIndexId&&(t.next="secondIndexId"),t.secondIndexId&&(t.next="thirdIndexId"),t.thirdIndexId&&(t.next="fourIndexId"),this.$emit("change",t)},rankChange(e){this.value="","一级分类"===e&&this.$emit("change",{firstIndexId:"",secondIndexId:"",thirdIndexId:""})},reset(){this.init()}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"1c4221e1",null),c=d.exports},9188:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{key:e.showKey},[a("el-select",{ref:"indicator",staticStyle:{width:"120px","margin-right":"5px"},attrs:{"value-key":"indexName",disabled:e.unMatch,placeholder:"请选择"},on:{change:e.changeIndicator},model:{value:e.indicatorValue,callback:function(t){e.indicatorValue=t},expression:"indicatorValue"}},e._l(e.indicatorData,(function(e){return a("el-option",{key:e.indexName,attrs:{label:e.indexName,value:e}})})),1),e.itemShow&&e.indicatorData1&&e.indicatorData1.length?a("el-select",{staticStyle:{width:"200px","margin-right":"5px"},attrs:{"value-key":"indexName",disabled:e.unMatch,filterable:"","collapse-tags":"",clearable:"",placeholder:"全部一级分类"},on:{change:e.changeIndicator1},model:{value:e.indicatorValue1,callback:function(t){e.indicatorValue1=t},expression:"indicatorValue1"}},e._l(e.indicatorData1,(function(e){return a("el-option",{key:e.id,attrs:{label:e.indexName,value:e}})})),1):e._e(),e.itemShow&&e.indicatorData2&&e.indicatorData2.length?a("el-select",{staticStyle:{width:"200px","margin-right":"5px"},attrs:{"value-key":"indexName",disabled:e.unMatch,filterable:"","collapse-tags":"",clearable:"",placeholder:"全部二级分类"},on:{change:e.changeIndicator2},model:{value:e.indicatorValue2,callback:function(t){e.indicatorValue2=t},expression:"indicatorValue2"}},e._l(e.indicatorData2,(function(e){return a("el-option",{key:e.id,attrs:{label:e.indexName,value:e}})})),1):e._e(),e.itemShow&&e.indicatorData3&&e.indicatorData3.length?a("el-select",{staticStyle:{width:"200px","margin-right":"5px"},attrs:{"value-key":"indexName",disabled:e.unMatch,filterable:"","collapse-tags":"",clearable:"",placeholder:"全部三级分类"},on:{change:e.changeIndicator3},model:{value:e.indicatorValue3,callback:function(t){e.indicatorValue3=t},expression:"indicatorValue3"}},e._l(e.indicatorData3,(function(e){return a("el-option",{key:e.id,attrs:{label:e.indexName,value:e}})})),1):e._e(),e.indicatorData4&&e.indicatorData4.length&&e.itemShow?a("el-select",{staticStyle:{width:"200px","margin-right":"5px"},attrs:{"value-key":"indexName",disabled:e.unMatch,filterable:"","collapse-tags":"",clearable:"",placeholder:"全部四级分类"},on:{change:e.changeIndicator4},model:{value:e.indicatorValue4,callback:function(t){e.indicatorValue4=t},expression:"indicatorValue4"}},e._l(e.indicatorData4,(function(e){return a("el-option",{key:e.id,attrs:{label:e.indexName,value:e}})})),1):e._e(),e.selectRow.unMatchTag?a("el-checkbox",{staticClass:"m-r-6",model:{value:e.unMatch,callback:function(t){e.unMatch=t},expression:"unMatch"}},[e._v("未匹配观点")]):e._e(),e.selectRow.showIndexReverseFilter?a("el-checkbox",{staticClass:"m-r-6",model:{value:e.indexReverseFilter,callback:function(t){e.indexReverseFilter=t},expression:"indexReverseFilter"}},[e._v("反选")]):e._e(),e.indicatorSelect?a("div",{staticClass:"float-right",on:{click:e.cutShowWrap}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.showWrap,expression:"showWrap"}],staticClass:"f-s-14 f-color-2 more-cursor"},[e._v("收起条件筛选 "),a("i",{staticClass:"el-icon-arrow-up m-l-6"})]),a("span",{directives:[{name:"show",rawName:"v-show",value:!e.showWrap,expression:"!showWrap"}],staticClass:"f-s-14 f-color-2 more-cursor"},[e._v("展开条件筛选 "),a("i",{staticClass:"el-icon-arrow-down m-l-6"})])]):e._e()],1)},r=[],i=a(3822),n={data(){return{showKey:0,indicatorValue:{},indicatorData:[],indicatorValue1:{},indicatorData1:[],indicatorValue2:{},indicatorData2:[],indicatorValue3:{},indicatorData3:[],indicatorValue4:{},indicatorData4:[],indexSystemList:[],unMatch:null,indexReverseFilter:!1}},props:{selectRow:{type:Object,default:()=>{}},showWrap:{type:Boolean,default:!1},indicatorSelect:{type:Boolean},list:{type:Array,default:()=>[]}},computed:{...(0,i.rn)({indexSystemArr:e=>e.indexSystemList&&e.indexSystemList.length?JSON.parse(JSON.stringify(e.indexSystemList)):[]}),unlimited(){return this.selectRow.unlimited},itemShow(){return"不限"!=this.indicatorValue.indexName}},mounted(){this.init(this.selectRow)},methods:{init(e){this.unMatch=e.unMatch||!1,this.indexReverseFilter=e.indexReverseFilter||!1,this.indexSystemList=this.list.length?this.list:this.indexSystemArr,this.iniIndicatorData(e),this.setIndicatorData(e)},getValue(){var e,t,a,s,r,i,n,l,o,d,c,h,m;let u=[],p=[],y=this.indicatorValue.id;if(u.push(this.indicatorValue.indexName),null!==(e=this.indicatorValue1)&&void 0!==e&&e.indexName&&u.push(this.indicatorValue1.indexName),null!==(t=this.indicatorValue2)&&void 0!==t&&t.indexName&&u.push(this.indicatorValue2.indexName),null!==(a=this.indicatorValue3)&&void 0!==a&&a.indexName&&u.push(this.indicatorValue3.indexName),null!==(s=this.indicatorValue4)&&void 0!==s&&s.indexName&&u.push(this.indicatorValue4.indexName),this.unMatch)return this.$store.commit("SET_INDEX_SYSTEM_ITEM",{}),{unMatch:this.unMatch?1:0};let g={};return"不限"==(null===(r=this.indicatorValue1)||void 0===r?void 0:r.indexName)?{}:(null!==(i=this.indicatorValue1)&&void 0!==i&&i.indexName||(p=this.indicatorData1.map((e=>e.id)),g={indexType:y,textName:u,firstIndexId:"",next:"firstIndexId"}),null===(n=this.indicatorValue1)||void 0===n||!n.indexName||null!==(l=this.indicatorValue2)&&void 0!==l&&l.indexName||(p=this.indicatorData2.map((e=>e.id)),g={indexType:y,textName:u,firstIndexId:this.indicatorValue1.id,secondIndexId:"",next:"secondIndexId"}),null===(o=this.indicatorValue2)||void 0===o||!o.indexName||null!==(d=this.indicatorValue3)&&void 0!==d&&d.indexName||(p=this.indicatorData3.map((e=>e.id)),g={indexType:y,textName:u,firstIndexId:this.indicatorValue1.id,secondIndexId:this.indicatorValue2.id,thirdIndexId:"",next:"thirdIndexId"}),null===(c=this.indicatorValue3)||void 0===c||!c.indexName||null!==(h=this.indicatorValue4)&&void 0!==h&&h.indexName||(g={indexType:y,textName:u,firstIndexId:this.indicatorValue1.id,secondIndexId:this.indicatorValue2.id,thirdIndexId:this.indicatorValue3.id,fourIndexId:null,next:"fourIndexId"}),null!==(m=this.indicatorValue4)&&void 0!==m&&m.indexName&&(g={indexType:y,textName:u,firstIndexId:this.indicatorValue1.id,secondIndexId:this.indicatorValue2.id,thirdIndexId:this.indicatorValue3.id,fourIndexId:this.indicatorValue4.id,next:"fiveIndexId"}),this.indexReverseFilter&&this.selectRow.showIndexReverseFilter&&(g["indexReverseFilter"]=this.indexReverseFilter),this.$store.commit("SET_INDEX_SYSTEM_ITEM",g),g)},iniIndicatorData(e){var t;if(this.indicatorData=[],(e.indicatorID||"0"==e.indicatorID)&&(this.indicatorValue.id=e.indicatorID+""),null!==(t=e.defaultNames)&&void 0!==t&&t.length){let t=e.defaultNames;t.forEach((e=>{this.indicatorData.push(...this.indexSystemList.filter((t=>e===t.indexName)))})),this.indicatorData.length||(this.indicatorData=JSON.parse(JSON.stringify(this.indexSystemList)))}else this.indicatorData=JSON.parse(JSON.stringify(this.indexSystemList));this.unlimited&&this.indicatorData.unshift({indexName:"不限",id:"",children:[""]})},selectedChange(e){this.$emit("selectedChange")},changeIndicator(e){this.indicatorData1=e.children,this.indicatorValue1=null,this.indicatorData2=[],this.indicatorValue2=null,this.indicatorData3=[],this.indicatorValue3=null,this.indicatorData4=[],this.indicatorValue4=null,this.selectedChange()},changeIndicator1(e){this.indicatorData2=[],this.indicatorValue2=null,this.indicatorData3=[],this.indicatorValue3=null,this.indicatorData4=[],this.indicatorValue4=null,this.indicatorData2=e.children,this.selectedChange()},changeIndicator2(e){this.indicatorData3=[],this.indicatorValue3=null,this.indicatorData3=e.children,this.indicatorData4=[],this.indicatorValue4=null,this.selectedChange()},changeIndicator3(e){this.indicatorData4=[],this.indicatorValue4=null,this.indicatorData4=e.children,this.selectedChange()},changeIndicator4(e){this.selectedChange()},setIndicatorData(e){var t,a;let s=e.firstIndexId,r=e.secondIndexId,i=e.thirdIndexId,n=e.fourIndexId;if(this.indicatorValue1.id=s,this.indicatorValue2.id=r,this.indicatorValue3.id=i,this.indicatorValue4.id=n,this.indicatorData.length&&null!==(t=this.indicatorData[0])&&void 0!==t&&null!==(a=t.children)&&void 0!==a&&a.length){if(!this.indicatorValue.id)return this.indicatorValue={indexName:this.indicatorData[0].indexName,id:this.indicatorData[0].id},void(this.indicatorData1=this.indicatorData[0].children);this.setData("indicatorValue","indicatorData",0)}else this.indicatorData.length&&(this.indicatorValue={indexName:this.indicatorData[0].indexName,id:this.indicatorData[0].id})},setData(e,t,a){var s,r;let i=null;i=a?e+a:e;let n=null;if(n=a?t+a:t,null!==(s=this[i])&&void 0!==s&&s.id||"0"==(null===(r=this[i])||void 0===r?void 0:r.id))for(let l=0;l<this[n].length;l++)if(this[n][l].id==this[i].id){this[t+(a+1)]=this[n][l].children,this[i]["indexName"]=this[n][l].indexName,this.setData("indicatorValue","indicatorData",a+1);break}},cutShowWrap(){this.$emit("cutShowWrap")},reset(){Object.assign(this.$data,this.$options.data(this)),this.init({}),this.selectedChange()}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,null,null),c=d.exports},52:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-popover",{attrs:{placement:"bottom",trigger:"click","popper-class":"car-popper"},on:{hide:e.changeSeries}},[a("div",[a("el-input",{staticClass:"m-b-10 m-l-10 m-t-10",style:"market"===e.screenType||e.filterValue||e.nature?"width: 176px;":"width: 176px;margin-left:40px",attrs:{placeholder:"请输入关键词"},on:{input:e.filterEl},model:{value:e.filterValue,callback:function(t){e.filterValue=t},expression:"filterValue"}}),a("div",{staticClass:"flex p-r-6 p-b-6 p-l-6",staticStyle:{overflow:"hidden","justify-content":"space-around"}},["market"===e.screenType||e.filterValue||e.nature?e._e():a("div",{staticClass:"h-280 w-32 text-center letter-big-wrap input-series-letter",staticStyle:{overflow:"auto",background:"#ffffff","border-radius":"4px"}},[a("ul",{staticClass:"p-l-0 letter-big"},e._l(e.letterBig,(function(t,s){return a("li",{key:s,class:e.classIndex===s?"point-hand active":"point-hand",on:{click:function(a){return a.stopPropagation(),e.clickAnchor(e.anchorID+t,s)}}},[e._v(" "+e._s(t)+" ")])})),0)]),a("el-cascader-panel",{key:e.cascaderKey,ref:"cas",attrs:{id:"inputSeriesPanelCascader","popper-class":"input-series-cascader",props:e.defaultTypeProps,options:e.carSeriesAndBrandArr,"show-all-levels":!1,filterable:"","collapse-tags":"",clearable:""},on:{change:e.handleChange},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.data;return[s.hide?a("span",{staticClass:"cascader-item-hide"},[e._v(e._s(s.hide))]):"market"!=e.screenType&&s.disabled&&!s.brandName?a("span",{staticClass:"car-popper-span",attrs:{id:e.anchorID+s.label}},[e._v(e._s(s.label))]):e._e()]}}]),model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)],1),a("div",{staticClass:"ps-relative m-l-10",staticStyle:{display:"inline-block"},attrs:{slot:"reference"},slot:"reference"},[e.isSlot?a("div",{staticStyle:{width:"220px",display:"inline-block"}},[a("el-input",{staticStyle:{width:"220px"},attrs:{type:"text",readonly:"",placeholder:!e.value&&"选择车系","suffix-icon":e.inputIconClass,clearable:""}}),a("div",{staticClass:"car__tags"},[e.carArr.length>1?[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.carArr.join(","),placement:"top-start"}},[a("span",[a("el-tag",{attrs:{size:"mini",type:"info",closable:"","disable-transitions":""},on:{close:function(t){return e.handleCloseTag(e.carArr[0])}}},[e._v(e._s(e.carArr[0]))]),a("el-tag",{attrs:{size:"mini",type:"info","disable-transitions":""}},[e._v("+"+e._s(e.carArr.length-1))])],1)])]:1==e.carArr.length?[a("el-tag",{attrs:{size:"mini",type:"info",closable:"","disable-transitions":""},on:{close:function(t){return e.handleCloseTag(e.carArr[0])}}},[e._v(e._s(e.carArr[0]))])]:e._e()],2)],1):e._e(),e._t("triggerButton")],2)])},r=[],i=a(3822),n={props:{typeProps:{type:Object,default:()=>{}},defaultValue:{type:Array,default:()=>[]},screenType:{type:String,default:()=>"market"},selectLengthFlag:{type:Boolean,default:()=>!0},multiple:{type:Boolean,default:!1},hideTrigger:{type:Boolean,default:!0},isSlot:{type:Boolean,default:!0},nature:{type:Boolean,default:!1}},data(){return{anchorID:"to"+parseInt(9e3*Math.random()),checkStrictly:!1,emitPath:!1,value:[],letterBig:[],classIndex:0,carSeriesAndBrandArr:[],inputValue:[],filterValue:"",inputIconClass:"el-icon-arrow-down",seriesObj:{},msgList:[],cascaderKey:1}},computed:{...(0,i.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[],seriesList:e=>e.seriesList&&e.seriesList.length?JSON.parse(JSON.stringify(e.seriesList)):[],marketList:e=>JSON.parse(JSON.stringify(e.marketList))}),defaultTypeProps(){return{label:"value",value:"id",multiple:this.multiple,checkStrictly:this.checkStrictly,emitPath:this.emitPath}},carArr(){return""==this.value?[]:this.value}},watch:{value:{handler(e,t){e.length>7&&this.selectLengthFlag&&(this.$message.warning("最多只能选7个车系"),this.$nextTick((()=>{this.value=t})))},deep:!0,immediate:!0},screenType:{handler(e,t){this.cascaderKey++,this.init()},immediate:!0},carSeriesAndBrandArr(e){this.cascaderKey++,this.$nextTick((()=>{$(".cascader-item-hide").closest(".el-cascader-node").hide()}))}},created(){this.defaultValue.length&&(this.value=this.defaultValue),("market"==this.screenType||"series"==this.screenType&&this.multiple)&&(this.checkStrictly=!0),this.init()},mounted(){if("series"===this.screenType||"brand"===this.screenType){let e=document.getElementById("inputSeriesPanelCascader");e.getElementsByClassName("el-cascader-menu__wrap el-scrollbar__wrap")[0].addEventListener("scroll",(()=>{const t=e.getElementsByClassName("el-scrollbar__view el-cascader-menu__list")[0].clientHeight,a=e.getElementsByClassName("el-cascader-menu__wrap el-scrollbar__wrap")[0].scrollTop,s=a/t,r=document.getElementsByClassName("input-series-letter")[0].getElementsByClassName("letter-big")[0].clientHeight;document.getElementsByClassName("input-series-letter")[0].scrollTo({top:r*s,left:0,behavior:"smooth"})}))}},methods:{init(){"series"===this.screenType&&(this.filterValue="",this.msgList=this.mapBoardArr(this.brandList,this.seriesList)),"brand"===this.screenType&&(this.filterValue="",this.msgList=this.mapBoardArr(this.brandList,[])),"market"===this.screenType&&(this.filterValue="",this.msgList=this.mapMarketSeriesArr(this.marketList,this.seriesList)),this.carSeriesAndBrandArr=JSON.parse(JSON.stringify(this.msgList))},clickAnchor(e,t){var a;this.classIndex=t,null===(a=document.getElementById(e))||void 0===a||a.scrollIntoView()},mapBoardArr(e=[],t=[]){let a=e.reduce(((e,a)=>(a["brandFirstLetter"]=a["brandFirstLetter"].toUpperCase(),this.$set(a,"id",a.brandName),this.$set(a,"value",a.brandName),"brand"==this.screenType&&this.multiple?this.$set(a,"disabled",!1):this.$set(a,"disabled",this.multiple),"series"==this.screenType&&(a["children"]=[]),e[a["brandFirstLetter"]]?e[a["brandFirstLetter"]].push(a):e={...e,[a["brandFirstLetter"]]:[a]},t.forEach((e=>{this.$set(e,"id",e.seriesCode),this.$set(e,"value",e.seriesName),e["brandName"]==a["brandName"]&&(e["brandLogo"]=a["brandLogo"],a["children"].push(e))})),e)),{}),s=Object.keys(a).sort();this.letterBig=s;let r=[];for(let n=0;n<s.length;n++)r.push({letter:s[n],arr:a[s[n]]});let i=[];return r.forEach((e=>{var t;i.push({letter:e.letter,label:e.letter,value:e.letter,disabled:!0}),i=i.concat(null!==(t=e.arr)&&void 0!==t?t:[])})),this.nature&&(i=i.filter((e=>"1"===e.nature))),i},mapMarketSeriesArr(e=[],t=[]){let a=e.map((e=>(this.$set(e,"children",[]),this.$set(e,"disabled",!0),this.$set(e,"label",e.value),t.forEach((t=>{this.$set(t,"id",t.seriesCode),this.$set(t,"value",t.seriesName),this.$set(t,"label",t.seriesName),t["marketId"]==e["id"]&&e["children"].push(t)})),e)));return a},handleChange(e){let t=this.$refs.cas.getCheckedNodes();this.multiple||this.$emit("change",t[0].data)},handleCloseTag(e){const t=this.$refs.cas.getCheckedNodes();this.value=t.reduce(((t,a)=>(a["value"]!=e&&t.push(a["label"]),t)),[])},handleCloseAll(){this.value=[]},getValue(){const e=this.$refs.cas.getCheckedNodes(!0);let{brandIds:t,brandNames:a,seriesIds:s,seriesNames:r}=e.reduce(((e,t)=>{const a=this.seriesList.find((e=>e["id"]==t["value"]));return e["brandIds"].push(a["brandId"]),e["brandNames"].push(a["brandName"]),e["seriesIds"].push(a["id"]),e["seriesNames"].push(a["value"]),e}),{brandIds:[],brandNames:[],seriesIds:[],seriesNames:[]});return t=[...new Set(t)],a=[...new Set(a)],r},filterEl(e){if(!e)return"series"==this.screenType&&!this.multiple||"brand"==this.screenType?this.checkStrictly=!1:this.checkStrictly=!0,this.emitPath=!1,"brand"!=this.screenType?this.seriesList.forEach(((e,t)=>{this.$set(e,"hide",!1)})):this.brandList.forEach(((e,t)=>{this.$set(e,"hide",!1)})),void(this.carSeriesAndBrandArr=JSON.parse(JSON.stringify(this.msgList)));this.checkStrictly=!1;const t=[];"brand"!=this.screenType?this.seriesList.forEach(((a,s)=>{this.$set(a,"hide",!0),a["seriesName"]&&PinyinMatch.match(a["seriesName"],e)&&this.$set(a,"hide",!1),t.push(a)})):this.brandList.forEach(((a,s)=>{this.$set(a,"hide",!0),a["brandName"]&&PinyinMatch.match(a["brandName"],e)&&this.$set(a,"hide",!1),t.push(a)})),this.carSeriesAndBrandArr=t},changeSeries(){this.hideTrigger&&this.$emit("change",this.value)},reset(){this.init()}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"9219f5d6",null),c=d.exports},7222:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"relative"},[e._t("triggerButton"),a("el-select",{staticClass:"select-transparent",attrs:{"value-key":"brandCode","popper-class":e.popperClass},on:{change:e.change},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:!e.nature,expression:"!nature"}],staticClass:"select-group-left"},[a("div",{staticClass:"letter-big"},e._l(e.letterBig,(function(t,s){return a("div",{key:s,class:e.classIndex===s?"item active":"item",on:{click:function(a){return a.stopPropagation(),e.clickAnchor("to"+t,s)}}},[e._v(" "+e._s(t)+" ")])})),0)]),a("div",{staticClass:"select-group",style:"padding-left:"+(e.nature?0:"34px")},[a("div",{staticClass:"select-group-right"},e._l(e.options,(function(t){return a("el-option-group",{key:t.label},[a("div",{directives:[{name:"show",rawName:"v-show",value:!e.nature,expression:"!nature"}],staticClass:"el-select-group__title",attrs:{id:"to"+t.label}},[e._v(" "+e._s(t.label)+" ")]),e._l(t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.brandName,value:e}})}))],2)})),1)])])],2)},r=[],i=a(3822),n={data(){return{popperClass:"popper-class"+parseInt(9e3*Math.random()),letterBig:[],classIndex:0,options:[],value:""}},props:{nature:{type:Boolean,default:()=>!1}},computed:{...(0,i.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[]})},created(){this.iniBrand(),this.generateBig()},mounted(){const e=document.getElementsByClassName(this.popperClass)[0];e.getElementsByClassName("el-select-dropdown__wrap el-scrollbar__wrap")[0].addEventListener("scroll",(()=>{const t=e.getElementsByClassName("select-group")[0].clientHeight,a=e.getElementsByClassName("el-select-dropdown__wrap el-scrollbar__wrap")[0].scrollTop,s=a/t,r=e.getElementsByClassName("select-group-left")[0].clientHeight;e.getElementsByClassName("select-group-left")[0].style.top="-"+r*s+"px"}))},methods:{generateBig(){for(var e=65;e<91;e++)this.letterBig.push(String.fromCharCode(e))},iniBrand(){let e=JSON.parse(JSON.stringify(this.brandList)),t={};e.map((e=>{let a="";a=e.brandFirstLetter.toUpperCase(),this.nature?"1"===e.nature&&(t[a]?t[a].push(e):t[a]=[e]):t[a]?t[a].push(e):t[a]=[e]}));for(let a in t)this.nature?this.options.length?this.options[0].options=this.options[0].options.concat(t[a]):this.options.push({label:"1",options:t[a]}):this.options.push({label:a,options:t[a]});this.options=this.options.sort(((e,t)=>{let a=e.label,s=t.label;return a>s?1:a<s?-1:0}))},clickAnchor(e,t){var a;this.classIndex=t,null===(a=document.getElementById(e))||void 0===a||a.scrollIntoView()},change(e){this.$emit("change",e)}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"47089249",null),c=d.exports},7085:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"select-group flex",style:e.nature?"":"width: 400px;"},[a("div",{directives:[{name:"show",rawName:"v-show",value:!e.nature,expression:"!nature"}],staticClass:"select-group-left"},[a("div",{staticClass:"letter-big",attrs:{id:e.letterBigID}},e._l(e.letterBig,(function(t,s){return a("div",{key:s,class:e.classIndex===s?"item active":"item",on:{click:function(a){return a.stopPropagation(),e.clickAnchor("toBrand"+t,s)}}},[e._v(" "+e._s(t)+" ")])})),0)]),a("div",{staticClass:"select-group-brand",attrs:{id:e.brandBoxID}},[a("div",{staticClass:"select-group-div"},[a("ul",{staticClass:"select-group-ul select-group-box",attrs:{id:e.brandUlID}},e._l(e.carSeriesAndBrandArr,(function(t,s){return a("li",{key:s},[t.letter?a("span",{staticClass:"span-letter",attrs:{id:"toBrand"+t.letter}},[e._v(e._s(t.letter))]):a("span",{class:e.classIndexBrand===s?"span-center active":"span-center",on:{click:function(a){return e.selectBrand(t,s)}}},[e._v(e._s(t.brandName)+" "),a("i",{staticClass:"el-icon-arrow-right el-cascader-node__postfix"})])])})),0)])]),a("div",{staticClass:"select-group-series"},[a("ul",{staticClass:"select-group-ul"},e._l(e.currentSeries,(function(t,s){return a("li",{key:s,class:e.classIndexSeries===s?"active":"",on:{click:function(a){return e.selectSeries(t,s)}}},[a("span",[e._v(e._s(t.seriesName))]),e.classIndexSeries===s?a("i",{staticClass:"el-icon-check el-cascader-node__prefix"}):e._e()])})),0)])])},r=[],i=a(3822),n={data(){return{brandBoxID:"brandBox"+parseInt(9e3*Math.random()),brandUlID:"brandUl"+parseInt(9e3*Math.random()),letterBigID:"letterBig"+parseInt(9e3*Math.random()),letterBig:[],classIndex:0,classIndexBrand:null,classIndexSeries:null,carSeriesAndBrandArr:[],currentSeries:[],currentBrand:[]}},props:{defaultValue:{type:Array,default:()=>[]},nature:{type:Boolean,default:()=>!1}},computed:{...(0,i.rn)({brandList:e=>e.brandList&&e.brandList.length?JSON.parse(JSON.stringify(e.brandList)):[],seriesList:e=>e.seriesList&&e.seriesList.length?JSON.parse(JSON.stringify(e.seriesList)):[]})},created(){this.generateBig(),this.carSeriesAndBrandArr=this.mapBoardArr(this.brandList,this.seriesList)},mounted(){const e=document.getElementById(this.brandBoxID);e.addEventListener("scroll",(t=>{const a=document.getElementById(this.brandUlID).clientHeight,s=e.scrollTop,r=s/a,i=document.getElementById(this.letterBigID),n=i.clientHeight;i.style.top="-"+n*r+"px"}))},methods:{generateBig(){for(var e=65;e<91;e++)this.letterBig.push(String.fromCharCode(e))},clickAnchor(e,t){var a;this.classIndex=t,null===(a=document.getElementById(e))||void 0===a||a.scrollIntoView()},mapBoardArr(e=[],t=[]){let a=e.reduce(((e,a)=>(a["label"]=a.brandName,a["value"]=a.brandCode,a["clicked"]=!1,a["children"]=[],e[a["brandFirstLetter"]]?e[a["brandFirstLetter"]].push(a):e={...e,[a["brandFirstLetter"]]:[a]},t.forEach((e=>{e["label"]=e.seriesName,e["value"]=e.seriesCode,e["brandCode"]==a["brandCode"]&&(-1!=this.defaultValue.findIndex((t=>t["value"]==e["value"]))?(e["clicked"]=!0,a["clicked"]=!0):e["clicked"]=!1,a["children"].push(e))})),e)),{}),s=Object.keys(a).sort(),r=[];for(let n=0;n<s.length;n++)r.push({letter:s[n],arr:a[s[n]]});let i=[];return r.forEach((e=>{var t;i.push({letter:e.letter.toUpperCase(),label:e.letter,value:e.letter,disabled:!0}),i=i.concat(null!==(t=e.arr)&&void 0!==t?t:[])})),this.nature&&(i=i.filter((e=>"1"===e.nature))),i},selectBrand(e,t){this.currentSeries=e.children,this.classIndexBrand=t,this.classIndexSeries=null,this.currentBrand=e},selectSeries(e,t){this.classIndexSeries=t,this.$emit("change",this.currentBrand,e)}}},l=n,o=a(1001),d=(0,o.Z)(l,s,r,!1,null,"5c3760d2",null),c=d.exports},8632:function(e,t,a){"use strict";a.d(t,{Z:function(){return d}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{attrs:{multiple:e.multiple,"value-key":"userFullName",filterable:"","collapse-tags":"",remote:"","reserve-keyword":"",placeholder:"请输入关键词","remote-method":e.remoteMethod,loading:e.loading},on:{focus:function(t){return e.remoteMethod("")},change:e.handleSelect},model:{value:e.state,callback:function(t){e.state=t},expression:"state"}},e._l(e.options,(function(t){return a("el-option",{key:t.loginID,attrs:{label:t.userFullName,value:t}},[a("span",[e._v(e._s(t.userFullName+" ("+t.loginID+")"))]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.fullDepartmentName,placement:"top-start"}},[a("span",{staticClass:"float-right full-department-name"},[e._v(e._s(t.fullDepartmentName))])])],1)})),1)},r=[],i={props:{value:{type:[Object,Array],default:()=>({})},multiple:{type:Boolean,default:()=>!1}},data(){return{loading:!1,options:[],state:{}}},created(){this.state=this.value,Array.isArray(this.value)?this.options=this.value:this.options=[this.value]},methods:{handleSelect(e){this.$emit("handleSelect",e)},async remoteMethod(e){var t;this.loading=!0;const a=(null===(t=await this.$store.dispatch("departmentChanganUsers",{keyWords:e}))||void 0===t?void 0:t.userList)||[{organizationId:"8f4ddf6d-507a-4835-8db5-84e1480823f0",fullDepartmentName:"长安汽车_公司领导",loginID:"0100133",telNumber:"",mobileNumber:"",departmentId:"B87C767EC058A254E0532507400A9DA2",userFullName:"李宝",title:'{"description":"于2021-01-10从HR同步建立","topDeptId":"dd54e5c9-d3af-42da-bc76-d5026018d671","fullDepartmentName":"长安汽车_重庆长安凯程汽车科技有限公司_保定长客_客车专用车业务部_技术中心_总体技术室","deptId":"B87C767EC058A254E0532507400A9DA2"}',userId:"47f41149-f256-4136-a513-97632282baf2",userCode:"李宝"},{organizationId:"8f4ddf6d-507a-4835-8db5-84e1480823f0",loginID:"0103963",fullDepartmentName:"长安汽车_公司领导",telNumber:"",mobileNumber:"",departmentId:"B87C767EC0D8A254E0532507400A9DA2",userFullName:"李宝强",title:'{"description":"于2021-01-10从HR同步建立","topDeptId":"dd54e5c9-d3af-42da-bc76-d5026018d671","fullDepartmentName":"长安汽车_重庆长安凯程汽车科技有限公司_保定长客_总装二车间_制造管理室","deptId":"B87C767EC0D8A254E0532507400A9DA2"}',userId:"86ee28fc-29f1-4da1-855d-5cb5ac231897",userCode:"李宝强"},{organizationId:"8f4ddf6d-507a-4835-8db5-84e1480823f0",loginID:"5700439",fullDepartmentName:"长安汽车_公司领导",telNumber:"",mobileNumber:"",departmentId:"a19b25a2-8777-4d08-9d42-84710997a043",userFullName:"李宝印",title:'{"description":"2020.8.31更改部门名称","topDeptId":"dd54e5c9-d3af-42da-bc76-d5026018d671","fullDepartmentName":"长安汽车_重庆长安凯程汽车科技有限公司_河北长安_总装一车间","deptId":"a19b25a2-8777-4d08-9d42-84710997a043"}',userId:"27A64924C5617505E054001517DFA4B8",userCode:"李宝印"}];this.loading=!1,this.options=a.filter((t=>t.userFullName.indexOf(e)>-1||t.loginID.indexOf(e)>-1?t:void 0))}}},n=i,l=a(1001),o=(0,l.Z)(n,s,r,!1,null,"a2ae3334",null),d=o.exports},4871:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.isAsComponent?e._e():a("div",{staticClass:"public-white public-border-radius"},[a("el-tabs",{staticClass:"public-tab-menu",on:{"tab-click":e.handleClick},model:{value:e.nowTab,callback:function(t){e.nowTab=t},expression:"nowTab"}},[a("el-tab-pane",{attrs:{label:"品牌",name:"brand"}}),a("el-tab-pane",{attrs:{label:"车系",name:"series"}})],1),a("search-box",{key:e.searchKey,ref:"search",attrs:{initSearch:e.initSearch,selectRow:e.selectRow},on:{search:e.search}})],1),a("analysis",{attrs:{dateTypes:e.dateTypes,attrName:e.attrName[e.sendData.measureIndex],sendData:e.sendData,isAsComponent:e.isAsComponent,attr:e.sendData.measureIndex,data:e.allChartData,pageType:"depth_analysis",totalMenKey:"totalMentionValue",momTotalMenKey:"momTotalMentionValue"},on:{seeUserDetail:e.seeUserDetailHandle,datasourceSeeDetail:e.datasourceSeeDetail,seeDetailHandle:e.seeDetailHandle,vocTrendSeeDetailHandle:e.vocTrendSeeDetailHandle,changeVocTrendType:e.changeVocTrendTypeHandle,changeDatasource:e.changeDatasourceHandle,textDetailsChange:e.textDetailsChangeHandle,standardKeywordDetail:e.standardKeywordDetailHandle,download:e.downloadHandle,wordCloudChartClick:e.wordCloudChartHandle,requestChange:e.requestChange}})],1)},r=[],i=a(3691),n=a(3557),l={components:{analysis:i.Z,searchBox:n.Z},props:{isAsComponent:{type:Boolean,default:!1},imComeSendData:{type:Object,default(){return{}}}},data(){return{nowTab:"brand",searchKey:0,tabOptions:{brand:{id:"brand",name:"品牌"},series:{id:"series",name:"车系"}},sendData:{startDate:"2022-03-01",endDate:"2022-03-30",indexType:"0",measureIndex:"experienceValue"},attrName:{experienceValue:"体验值",negativeMentionRate:"负面提及率"},momAttr:{experienceValue:"momExperienceValue",negativeMentionRate:"momNegativeMentionRate"},allChartData:{vocTrend:{data:{},remarkData:{type:"day"},loading:!1},vocExperience:{data:{},remarkData:{},loading:!1},topQuestion:{data:[],remarkData:{selectFeelTag:"负面",selectSortType:"MENTION_DESC"},loading:!1},populationCharacteristics:{data:{},remarkData:{},loading:!1},datasourceAnalysis:{data:{},remarkData:{},loading:!1},indexAnalysis:{data:{},remarkData:{},loading:!1},textDetails:{data:{},remarkData:{},loading:!1},areaAnalysis:{data:[],remarkData:{},loading:!1},textDetailPage:{pageNum:1,pageSize:10}},initSearch:!0,textDetailPage:{pageNum:1,pageSize:10}}},computed:{selectRow(){var e={indicatorItem:{},textSearch1:{},marketSegment:{},carAllBrandAsSeriesSelect:{type:"brand",brandNames:[]},restsItem:{selectRow:{measureIndex:{},dataSources:{sources:"wai"},carOwne:{},commonDate:{}}}};return"brand"===this.nowTab?(this.$set(e.carAllBrandAsSeriesSelect,"type","brand"),this.$set(e.carAllBrandAsSeriesSelect,"brandNams",[])):(this.$set(e.carAllBrandAsSeriesSelect,"type","series"),this.$set(e.carAllBrandAsSeriesSelect,"seriesNames",[])),e},dateTypes(){return this.$moment(this.sendData.endDate).diff(this.$moment(this.sendData.startDate),"day")>92?[{id:"month",name:"月"}]:[{id:"day",name:"日"},{id:"month",name:"月"}]}},created(){this.init()},mounted(){this.initSearch||this.$refs.search.selectedChange(!0)},methods:{seeUserDetailHandle(e){var t={userName:e.userName},a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"textDetailsDrawer",sendData:t,showSendData:a,head:"原文明细",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},wordCloudChartHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t["standardKeyword"]=e.name,t["dataSources"]=e.dataSources;var a=this.$publicHandle.makeShowSendData(t,this);a["标准关键词"]=e.name;var s={show:!0,component:"textDetailsDrawer",sendData:t,showSendData:a,head:"原文明细"};this.$store.commit("SET_OVERALL_DRAWER",s)},async downloadHandle(e){const{command:t,chartType:a,tableData:s}=e;var r=!1;if("detail"==t){var i,n=[],l=[],o=this.allChartData[a].data;if("vocTrend"==a)n=["日期","正面提及率","中性提及量","负面提及量",this.attrName[this.sendData.measureIndex]],l=["keyWord","positiveMentionValue","neutralMentionValue","negativeMentionValue",this.sendData.measureIndex],i="VOC"+this.attrName[this.sendData.measureIndex]+"趋势";else if("vocExperience"==a)n=["指标","正面提及率","中性提及量","负面提及量",this.attrName[this.sendData.measureIndex]],l=["keyWord","positiveMentionValue","neutralMentionValue","negativeMentionValue",this.sendData.measureIndex],i="VOC"+this.attrName[this.sendData.measureIndex];else if("topQuestion"==a){r=!0;var d=JSON.parse(JSON.stringify(this.sendData));d["emotionAttribute"]=this.allChartData.topQuestion.remarkData.selectFeelTag,d["sortName"]=this.allChartData.topQuestion.remarkData.selectSortType,o=await this.$store.dispatch("rivalAnalysisTopList",d)||[],n=["标准关键词","情感","提及量","提及量变化","提及量环比","提及率"],l=["keyWord","emotionAttribute","totalMentionValue","momTotalMentionValue","momTotalMentionValueRate","mentionRate"],i="Top问题"}else if("datasourceAnalysis"==a)n=["指标","正面提及率","中性提及量","负面提及量",this.attrName[this.sendData.measureIndex]],l=["keyWord","positiveMentionValue","neutralMentionValue","negativeMentionValue",this.sendData.measureIndex],i="渠道组成";else if("indexAnalysis"==a){o=this.allChartData[a].data.detail;n=["排名","较上期排名","指标","提及量","提及量环比",this.attrName[this.sendData.measureIndex],this.attrName[this.sendData.measureIndex]+"环比"],l=["nowIndex","momIndex","keyWord","totalMentionValue","momTotalMentionValueRate",this.sendData.measureIndex,this.momAttr[this.sendData.measureIndex]],i="指标分析"}this.$publicHandle.downloadExcel(o,n,l,i,r)}else"clientDetail"==t&&this.$publicHandle.linkClientDetail(this.sendData,this)},init(){if(this.isAsComponent){var e=this.imComeSendData,t=e.index;for(var a in delete e.index,t)e[a]=t[a];this.sendData=e,this.getDataController()}else{this.initSearch=!1;var s=this.$route.query;if("{}"!=JSON.stringify(s))this.routeSearchParams(s);else{var r=this.$publicHandle.getStorage("depth_analysis")||{};this.nowTab=r.nowTab||this.nowTab,this.getStorage()}}},getStorage(){var e=this.$publicHandle.getStorage("depth_analysis")||{};e=e["search_"+this.nowTab]||{},"brand"==this.nowTab?(this.selectRow.indicatorItem.indicatorID=e.indexType||this.selectRow.indicatorItem.indicatorID,this.selectRow.indicatorItem.firstIndexId=e.firstIndexId||"",this.selectRow.indicatorItem.secondIndexId=e.secondIndexId||"",this.selectRow.indicatorItem.thirdIndexId=e.thirdIndexId||"",this.selectRow.carAllBrandAsSeriesSelect.brandNames=e.brandNames,this.selectRow.marketSegment.defaultValue=e.marketNames||[],this.selectRow.restsItem.selectRow.dataSources.defaultValue=e.dataSources||[],this.selectRow.restsItem.selectRow.measureIndex.defaultValue=e.measureIndex||"",this.selectRow.restsItem.selectRow.commonDate.timeseg=e.dateType,this.selectRow.restsItem.selectRow.commonDate.startTime=e.startDate,this.selectRow.restsItem.selectRow.commonDate.endTime=e.endDate,this.selectRow.textSearch1.standardKeywordsSearchType=e.standardKeywordsSearchType,this.selectRow.textSearch1.standardKeywords=e.standardKeywords):(this.selectRow.indicatorItem.indicatorID=e.indexType||this.selectRow.indicatorItem.indexType,this.selectRow.indicatorItem.firstIndexId=e.firstIndexId||"",this.selectRow.indicatorItem.secondIndexId=e.secondIndexId||"",this.selectRow.indicatorItem.thirdIndexId=e.thirdIndexId||"",this.selectRow.carAllBrandAsSeriesSelect.seriesNames=e.seriesNames,this.selectRow.carAllBrandAsSeriesSelect.brandNames=e.brandNames,this.selectRow.marketSegment.defaultValue=e.marketNames||[],this.selectRow.restsItem.selectRow.dataSources.defaultValue=e.dataSources||[],this.selectRow.restsItem.selectRow.measureIndex.defaultValue=e.measureIndex||"",this.selectRow.restsItem.selectRow.commonDate.timeseg=e.dateType,this.selectRow.restsItem.selectRow.commonDate.startTime=e.startDate,this.selectRow.restsItem.selectRow.commonDate.endTime=e.endDate,this.selectRow.textSearch1.standardKeywordsSearchType=e.standardKeywordsSearchType,this.selectRow.textSearch1.standardKeywords=e.standardKeywords)},routeSearchParams(e){var t,a,s,r;this.searchKey++;let i="brand";e.seriesNames&&(i="series"),this.nowTab=i,"week"!==e.dateType&&"day"!==e.dateType&&"month"!==e.dateType||(e.dateType="other");let n={indicatorItem:{indicatorID:e.indexType,firstIndexId:e.firstIndexId,secondIndexId:e.secondIndexId,thirdIndexId:e.thirdIndexId},marketSegment:{defaultValue:null===(t=e.marketNames)||void 0===t?void 0:t.split(",")},carAllBrandAsSeriesSelect:{type:i,brandNames:null===(a=e.brandNames)||void 0===a?void 0:a.split(","),seriesNames:null===(s=e.seriesNames)||void 0===s?void 0:s.split(","),emptyBrandFlag:!0},restsItem:{selectRow:{measureIndex:{defaultValue:e.measureIndex||""},dataSources:{defaultValue:(null===(r=e.dataSources)||void 0===r?void 0:r.split(","))||[],sources:"wai"},commonDate:{timeseg:e.dateType||"other",startTime:e.startDate,endTime:e.endDate}}}};this.selectRow=Object.assign(this.selectRow,n)},getDataController(){this.getVocExperienceTrend(),this.getVocExperience(),this.getTopQuestion(),this.getDataSource(),this.getIndexAyalysisData(),this.getTextDetails()},async getVocExperienceTrend(e){this.allChartData.vocTrend.loading=!0;try{var t=JSON.parse(JSON.stringify(this.sendData));e||(this.allChartData.vocTrend.remarkData.type=this.$publicHandle.checkTimeTooLong(t.startDate,t.endDate,this)),t["dateType"]=this.allChartData.vocTrend.remarkData.type;const a=await this.$store.dispatch("rivalAnalysisVocTrend",t);this.allChartData.vocTrend.data=a}catch(a){this.allChartData.vocTrend.data=[],console.log(a)}this.allChartData.vocTrend.loading=!1},async getVocExperience(){try{const a=await this.$store.dispatch("rivalAnalysisVoc",this.sendData);for(var e={},t=0;t<a.dataList.length;t++)e[a.dataList[t].keyWord]=a.dataList[t].keyCode;this.indexObj=e,this.$set(this.allChartData,"vocExperience",{data:a.dataList||[],remarkData:a.total})}catch(a){console.log(a)}},datasourceSeeDetail(e){var t=JSON.parse(JSON.stringify(this.sendData));t["dataSources"]=e.dataSources,t["startDate"]=e.startDate,t["endDate"]=e.endDate;var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"depthAnalysis",sendData:t,showSendData:a,head:"深度分析"};this.$store.commit("SET_OVERALL_DRAWER",s)},vocTrendSeeDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t["startDate"]=e.name,t["endDate"]=e.name,t["dateType"]=this.allChartData.vocTrend.remarkData.type||"day";var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"depthAnalysis",sendData:t,showSendData:a,head:"深度分析"};this.$store.commit("SET_OVERALL_DRAWER",s)},seeDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t[t.next]=this.indexObj[e.name];var a=["firstIndexId","secondIndexId","thirdIndexId","fourIndexId"];t.next=a[a.indexOf(t.next)+1>3?3:a.indexOf(t.next)+1];var s=this.$publicHandle.makeShowSendData(t,this),r={show:!0,component:"depthAnalysis",sendData:t,showSendData:s,head:"深度分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",r)},requestChange(e){this.allChartData.topQuestion.remarkData.selectFeelTag=e.selectFeelTag,this.allChartData.topQuestion.remarkData.selectSortType=e.selectSortType,this.getTopQuestion()},async getTopQuestion(){this.allChartData.topQuestion.loading=!0;try{var e=JSON.parse(JSON.stringify(this.sendData));e["emotionAttribute"]=this.allChartData.topQuestion.remarkData.selectFeelTag,e["sortName"]=this.allChartData.topQuestion.remarkData.selectSortType,e["topN"]=30;const t=await this.$store.dispatch("rivalAnalysisTopList",e);this.allChartData.topQuestion.data=t||[]}catch(t){console.log(t),this.allChartData.topQuestion.data=[]}this.allChartData.topQuestion.loading=!1},async getDataSource(){this.allChartData.datasourceAnalysis.loading=!0;try{for(var e=await this.$store.dispatch("rivalAnalysisDataSource",this.sendData),t=0;t<e.length;t++)e[t].dataSource=e[t].keyWord;this.$set(this.allChartData.datasourceAnalysis,"data",e||[])}catch(a){console.log(a)}this.allChartData.datasourceAnalysis.loading=!1},async changeDatasourceHandle(e){this.allChartData.datasourceAnalysis.loading=!0,await this.getDataSourceTrend(e),await this.getDataSourceWordCloud(e),this.allChartData.datasourceAnalysis.loading=!1},async getDataSourceWordCloud(e){var t=JSON.parse(JSON.stringify(this.sendData));t.dataSources=[e];var a=[];try{const e=await this.$store.dispatch("rivalAnalysisDataSourceKeyword",t);for(var s in e)a.push({name:e[s].keyWord,value:e[s].totalMentionValue});this.$set(this.allChartData.datasourceAnalysis.remarkData,"wordCloud",a)}catch(r){console.log(r),this.$set(this.allChartData.datasourceAnalysis.remarkData,"wordCloud",[])}},async getDataSourceTrend(e){var t=JSON.parse(JSON.stringify(this.sendData));t.dataSources=[e];try{const e=await this.$store.dispatch("rivalAnalysisDataSourceTrend",t);this.$set(this.allChartData.datasourceAnalysis.remarkData,"trend",e)}catch(a){console.log(a),this.$set(this.allChartData.datasourceAnalysis.remarkData,"trend",[])}},async getIndexAyalysisData(){this.allChartData.indexAnalysis.loading=!0;try{const t=await this.$store.dispatch("rivalAnalysisIndexRank",this.sendData),a=t.dataDetail||[],s=t.perDataDetail||[],r=[t.treeData]||0;var e={firstIndexId:"一级分类",secondIndexId:"二级分类",thirdIndexId:"三级分类",fourIndexId:"四级分类"};e=e[this.sendData.next],this.$set(this.allChartData,"indexAnalysis",{data:{data:a.concat(s),detail:a,detailMom:s},remarkData:r,remarkData2:e})}catch(t){console.log(t)}this.allChartData.indexAnalysis.loading=!1},async getTextDetails(){this.allChartData.textDetails.loading=!0;var e=JSON.parse(JSON.stringify(this.sendData));for(var t in this.textDetailPage)e[t]=this.textDetailPage[t];try{const t=await this.$store.dispatch("rivalAnalysisContentList",e)||{};this.allChartData.textDetails.data=t.list||[],this.allChartData.textDetails.remarkData=t||{}}catch(a){this.allChartData.textDetails.data=[],this.allChartData.textDetails.remarkData={},console.log(a)}this.allChartData.textDetails.loading=!1},search(e,t){this.sendData=JSON.parse(JSON.stringify(e));var a={};a["search_"+this.nowTab]=this.sendData,t&&(a["nowTab"]=this.nowTab,this.$publicHandle.setStorage("depth_analysis",a)),delete this.sendData.brandCodes,this.textDetailPage={pageNum:1,pageSize:10},this.getDataController()},handleClick(){this.getStorage(),this.searchKey++},changeVocTrendTypeHandle(e){this.allChartData.vocTrend.remarkData.type=e,this.getVocExperienceTrend(!0)},textDetailsChangeHandle(e){for(var t in e)this.textDetailPage[t]=e[t];this.getTextDetails()},standardKeywordDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData)),a=this.$publicHandle.makeShowSendData(t,this);t["standardKeyword"]=e,a["标准关键词"]=e;var s={show:!0,component:"keywordDetailsDrawer",sendData:t,showSendData:a,head:"标准关键词详情"};this.$store.commit("SET_OVERALL_DRAWER",s)}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,"4afc31a9",null),h=c.exports},1232:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.isAsComponent?e._e():a("div",{staticClass:"public-white public-border-radius"},[a("el-tabs",{staticClass:"public-tab-menu",on:{"tab-click":e.handleClick},model:{value:e.nowTab,callback:function(t){e.nowTab=t},expression:"nowTab"}},e._l(e.tabOptions,(function(e,t){return a("el-tab-pane",{key:t,attrs:{label:e.name,name:e.id}})})),1),(e.nowTab,a("search-center",{key:e.searchKey,attrs:{selectRow:e.selectRow},on:{search:e.search}}))],1),a("analysis",{attrs:{dateTypes:e.dateTypes,isAsComponent:e.isAsComponent,sendData:e.sendData,attr:e.sendData.measureIndex,attrName:e.attrName[e.sendData.measureIndex],data:e.allChartData,pageType:"attribution_analysis"},on:{seeIndexDetail:e.seeIndexDetail,seeAreaDetail:e.seeAreaDetail,datasourceSeeDetail:e.datasourceSeeDetail,seeDetailHandle:e.seeDetailHandle,vocTrendSeeDetailHandle:e.vocTrendSeeDetailHandle,seeUserDetail:e.seeUserDetailHandle,changeVocTrendType:e.changeVocTrendTypeHandle,changeDatasource:e.changeDatasourceHandle,textDetailsChange:e.textDetailsChangeHandle,standardKeywordDetail:e.standardKeywordDetailHandle,download:e.downloadHandle,wordCloudChartClick:e.wordCloudChartHandle,requestChange:e.requestChange}})],1)},r=[],i=a(3691),n=a(3557),l={components:{analysis:i.Z,searchBox:n.Z},props:{isAsComponent:{type:Boolean,default:!1},imComeSendData:{type:Object,default(){return{}}}},computed:{selectRow(){var e={indicatorItem:{indicatorID:"",firstIndexId:"",secondIndexId:"",thirdIndexId:""},marketSegment:{},textSearch1:{},carSelfBrandSeriesSelect:{type:"brand",brandNames:["长安引力"],emptyBrandFlag:!0},restsItem:{selectRow:{measureIndex:{},dataSources:{},carOwner:{},commonDate:{},province:{}}}};return"brand"===this.nowTab?(e.carSelfBrandSeriesSelect.type="brand",e.carSelfBrandSeriesSelect.brandNames=["长安引力"],this.$set(e.carSelfBrandSeriesSelect,"radio",!1)):(e.carSelfBrandSeriesSelect.type="model",this.$set(e.carSelfBrandSeriesSelect,"radio",!0),e.carSelfBrandSeriesSelect.brandNames=["长安引力"],e.carSelfBrandSeriesSelect.seriesNames=["CS75 PLUS"]),e},dateTypes(){return this.$moment(this.sendData.endDate).diff(this.$moment(this.sendData.startDate),"day")>92?[{id:"month",name:"月"}]:[{id:"day",name:"日"},{id:"month",name:"月"}]}},data(){return{searchKey:0,nowTab:"brand",tabOptions:{brand:{id:"brand",name:"品牌"},series:{id:"series",name:"车系"}},sendData:{},allChartData:{vocTrend:{data:{},remarkData:{type:"day"},loading:!1},vocExperience:{data:{},remarkData:{},loading:!1},topQuestion:{data:[],remarkData:{selectFeelTag:"负面",selectSortType:"MENTION_DESC"},loading:!1},populationCharacteristics:{data:{},remarkData:{},loading:!1},datasourceAnalysis:{data:{},remarkData:{},loading:!1},indexAnalysis:{data:{},remarkData:{},loading:!1},textDetails:{data:{},remarkData:{},loading:!1},areaAnalysis:{data:[],remarkData:{},loading:!1}},attrName:{experienceValue:"体验值",negativeMentionRate:"负面提及率"},momAttr:{experienceValue:"momExperienceValue",negativeMentionRate:"momNegativeMentionRate"},textDetailPage:{pageNum:1,pageSize:10}}},created(){this.init()},methods:{wordCloudChartHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t["standardKeyword"]=e.name,t["dataSources"]=e.dataSources;var a=this.$publicHandle.makeShowSendData(t,this);a["标准关键词"]=e.name;var s={show:!0,component:"textDetailsDrawer",sendData:t,showSendData:a,head:"原文明细",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},async downloadHandle(e){const{command:t,chartType:a,tableData:s}=e;if("detail"==t){var r,i=!1,n=[],l=[],o=this.allChartData[a].data;if("areaAnalysis"==a&&(n=["区域",this.attrName[this.sendData.measureIndex],this.attrName[this.sendData.measureIndex]+"环比","提及量","提及量环比"],l=["province",this.sendData.measureIndex,this.momAttr[this.sendData.measureIndex],"totalMentionValue","momTotalMentionValueRate"],r="地域分析"),"populationCharacteristics"==a){n=["标签","类型","占比"],l=["type","secondType","mentionRate"];r="人群特征",o=this.allChartData[a].data.details}if("vocTrend"==a)n=["日期","正面提及率","中性提及量","负面提及量",this.attrName[this.sendData.measureIndex]],l=["keyWord","positiveMentionValue","neutralMentionValue","negativeMentionValue",this.sendData.measureIndex],r="VOC"+this.attrName[this.sendData.measureIndex]+"趋势";else if("vocExperience"==a)n=["指标","正面提及率","中性提及量","负面提及量",this.attrName[this.sendData.measureIndex]],l=["keyWord","positiveMentionValue","neutralMentionValue","negativeMentionValue",this.sendData.measureIndex],r="VOC"+this.attrName[this.sendData.measureIndex];else if("topQuestion"==a){i=!0;var d=JSON.parse(JSON.stringify(this.sendData));d["emotionAttribute"]=this.allChartData.topQuestion.remarkData.selectFeelTag,d["sortName"]=this.allChartData.topQuestion.remarkData.selectSortType,o=await this.$store.dispatch("attributeAnalysisTopKeywordList",d)||[],n=["标准关键词","情感","提及量","提及量变化","提及量环比","提及率"],l=["keyWord","emotionAttribute","totalMentionValue","momTotalMentionValue","momTotalMentionValueRate","mentionRate"],r="Top问题"}else if("datasourceAnalysis"==a)n=["指标","正面提及率","中性提及量","负面提及量"],l=["dataSource","positiveMentionValue","neutralMentionValue","negativeMentionValue"],r="渠道组成";else if("indexAnalysis"==a){o=this.allChartData[a].data.detail;n=["排名","较上期排名","指标","提及量","提及量环比",this.attrName[this.sendData.measureIndex],this.attrName[this.sendData.measureIndex]+"环比"],l=["nowIndex","momIndex","keyWord","totalMentionValue","momTotalMentionValueRate",this.sendData.measureIndex,this.momAttr[this.sendData.measureIndex]],r="指标分析"}this.$publicHandle.downloadExcel(o,n,l,r,i)}else"clientDetail"==t&&this.$publicHandle.linkClientDetail(this.sendData,this)},init(){if(this.isAsComponent){var e=this.imComeSendData;e.measureIndex||(e.measureIndex="experienceValue");var t=e.index;for(var a in delete e.index,t)e[a]=t[a];this.sendData=e,this.getDataController()}else{var s=this.$route.query;if("{}"!=JSON.stringify(s))this.routeSearchParams(s);else{var r=this.$publicHandle.getStorage("attribution_analysis")||{};this.nowTab=r.nowTab||this.nowTab,this.getStorage()}}},getStorage(){var e=this.$publicHandle.getStorage("attribution_analysis")||{};e=e["search_"+this.nowTab]||{},"brand"==this.nowTab?(this.selectRow.indicatorItem.indicatorID=e.indexType||this.selectRow.indicatorItem.indicatorID,this.selectRow.indicatorItem.firstIndexId=e.firstIndexId||"",this.selectRow.indicatorItem.secondIndexId=e.secondIndexId||"",this.selectRow.indicatorItem.thirdIndexId=e.thirdIndexId||"",this.selectRow.marketSegment.defaultValue=e.marketNames||[],this.selectRow.carSelfBrandSeriesSelect.brandNames=e.brandNames||this.selectRow.carSelfBrandSeriesSelect.brandNames,this.selectRow.restsItem.selectRow.dataSources.defaultValue=e.dataSources||[],this.selectRow.restsItem.selectRow.measureIndex.defaultValue=e.measureIndex||"",this.selectRow.restsItem.selectRow.carOwner.defaultValue=e.carOwner,this.selectRow.restsItem.selectRow.province.defaultValue=e.provinces,this.selectRow.restsItem.selectRow.commonDate.timeseg=e.dateType,this.selectRow.restsItem.selectRow.commonDate.startTime=e.startDate,this.selectRow.restsItem.selectRow.commonDate.endTime=e.endDate,this.selectRow.textSearch1.standardKeywordsSearchType=e.standardKeywordsSearchType,this.selectRow.textSearch1.standardKeywords=e.standardKeywords):(this.selectRow.indicatorItem.indicatorID=e.indexType||this.selectRow.indicatorItem.indexType,this.selectRow.indicatorItem.firstIndexId=e.firstIndexId||"",this.selectRow.indicatorItem.secondIndexId=e.secondIndexId||"",this.selectRow.indicatorItem.thirdIndexId=e.thirdIndexId||"",this.selectRow.carSelfBrandSeriesSelect.brandNames=e.brandNames||this.selectRow.carSelfBrandSeriesSelect.brandNames,this.selectRow.carSelfBrandSeriesSelect.seriesNames=e.seriesNames||[],this.selectRow.carSelfBrandSeriesSelect.modelNames=e.modelNames||[],this.selectRow.marketSegment.defaultValue=e.marketNames||[],this.selectRow.restsItem.selectRow.dataSources.defaultValue=e.dataSources||[],this.selectRow.restsItem.selectRow.measureIndex.defaultValue=e.measureIndex||"",this.selectRow.restsItem.selectRow.carOwner.defaultValue=e.carOwner,this.selectRow.restsItem.selectRow.province.defaultValue=e.provinces,this.selectRow.restsItem.selectRow.commonDate.timeseg=e.dateType,this.selectRow.restsItem.selectRow.commonDate.startTime=e.startDate,this.selectRow.restsItem.selectRow.commonDate.endTime=e.endDate,this.selectRow.textSearch1.standardKeywordsSearchType=e.standardKeywordsSearchType,this.selectRow.textSearch1.standardKeywords=e.standardKeywords)},routeSearchParams(e){this.searchKey++,"week"!==e.dateType&&"day"!==e.dateType&&"month"!==e.dateType||(e.dateType="other");let t={},a="brand";e.modelNames?(a="model",this.nowTab="series",t={type:a,brandNames:e.brandNames.split(",")||[],defaultValue:e.modelNames.split(","),radio:!0,emptyBrandFlag:!0}):t={type:a,brandNames:e.brandNames?e.brandNames.split(","):[],radio:!1,emptyBrandFlag:!0};let s={indicatorItem:{indicatorID:e.indexType,firstIndexId:e.firstIndexId,secondIndexId:e.secondIndexId,thirdIndexId:e.thirdIndexId},marketSegment:{defaultValue:e.marketNames?e.marketNames.split(","):[]},carSelfBrandSeriesSelect:t,restsItem:{selectRow:{measureIndex:{defaultValue:e.measureIndex||""},dataSources:{defaultValue:e.dataSources?e.dataSources.split(","):[]},carOwner:{defaultValue:e.carOwner||0},commonDate:{timeseg:e.dateType||"other",startTime:e.startDate,endTime:e.endDate},province:{defaultValue:e.provinces?e.provinces.split(","):[]}}}};this.selectRow=Object.assign(this.selectRow,s)},getDataController(){this.getVocExperienceTrend(),this.getVocExperience(),this.getTopQuestion(),this.getPopulationCharacteristics(),this.getDataSource(),this.getAreaData(),this.getIndexAyalysisData(),this.getTextDetails()},async getVocExperienceTrend(e){this.allChartData.vocTrend.loading=!0;try{var t=JSON.parse(JSON.stringify(this.sendData));e||(this.allChartData.vocTrend.remarkData.type=this.$publicHandle.checkTimeTooLong(t.startDate,t.endDate,this)),t["dateType"]=this.allChartData.vocTrend.remarkData.type;const a=await this.$store.dispatch("attributeAnalysisVocExperienceTrend",t);this.allChartData.vocTrend.data=a}catch(a){this.allChartData.vocTrend.data=[],console.log(a)}this.allChartData.vocTrend.loading=!1},changeVocTrendTypeHandle(e){this.allChartData.vocTrend.remarkData.type=e,this.getVocExperienceTrend(!0)},async changeDatasourceHandle(e){this.allChartData.datasourceAnalysis.loading=!0,await this.getDatasourceMentionTrend(e),await this.getDatasourceMentionWordCloud(e),this.allChartData.datasourceAnalysis.loading=!1},seeDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t[t.next]=this.indexObj[e.name];var a=["firstIndexId","secondIndexId","thirdIndexId","fourIndexId"];t.next=a[a.indexOf(t.next)+1>3?3:a.indexOf(t.next)+1];var s=this.$publicHandle.makeShowSendData(t,this),r={show:!0,component:"analysis",sendData:t,showSendData:s,head:"详细分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",r)},seeIndexDetail(e){var t=JSON.parse(JSON.stringify(this.sendData));t[t.next]=e;var a=["firstIndexId","secondIndexId","thirdIndexId","fourIndexId"];t.next=a[a.indexOf(t.next)+1>3?3:a.indexOf(t.next)+1];var s=this.$publicHandle.makeShowSendData(t,this),r={show:!0,component:"analysis",sendData:t,showSendData:s,head:"详细分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",r)},seeAreaDetail(e){var t=JSON.parse(JSON.stringify(this.sendData));t["provinces"]=[e];var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"analysis",sendData:t,showSendData:a,head:"详细分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},datasourceSeeDetail(e){var t=JSON.parse(JSON.stringify(this.sendData));t["dataSources"]=e.dataSources,t["startDate"]=e.startDate,t["endDate"]=e.endDate;var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"analysis",sendData:t,showSendData:a,head:"详细分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},vocTrendSeeDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData));t["startDate"]=e.name,t["endDate"]=e.name,t["dateType"]=this.allChartData.vocTrend.remarkData.type||"day";var a=this.$publicHandle.makeShowSendData(t,this),s={show:!0,component:"analysis",sendData:t,showSendData:a,head:"详细分析",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},async getDatasourceMentionTrend(e){var t=JSON.parse(JSON.stringify(this.sendData));t.dataSources=[e];try{const e=await this.$store.dispatch("attributeAnalysisMentionValueTrend",t);this.$set(this.allChartData.datasourceAnalysis.remarkData,"trend",e)}catch(a){console.log(a),this.$set(this.allChartData.datasourceAnalysis.remarkData,"trend",[])}},async getDatasourceMentionWordCloud(e){var t=JSON.parse(JSON.stringify(this.sendData));t.dataSources=[e];var a=[];try{const e=await this.$store.dispatch("attributeAnalysisWordCloud",t);for(var s in e)a.push({name:s,value:e[s]});this.$set(this.allChartData.datasourceAnalysis.remarkData,"wordCloud",a)}catch(r){console.log(r),this.$set(this.allChartData.datasourceAnalysis.remarkData,"wordCloud",[])}},async getVocExperience(){this.allChartData.vocExperience.loading=!0;try{var e;const n=await this.$store.dispatch("attributeAnalysisVocExperience",this.sendData);n["momTotalMentionValueRate"]=n["totalMentionValueMom"],n["momExperienceValueRate"]=n["experienceValueMom"],n["momNegativeMentionRate"]=n["negativeMentionRateMom"];var t=JSON.parse(JSON.stringify(this.sendData)),a=this.$publicHandle.makeShowSendData(t,this),s=(null===(e=a["指标体系"])||void 0===e?void 0:e.split("-"))||[];n["nowIndexName"]=s;for(var r={},i=0;i<n.dwdEvtWorkOrderDtlEntities.length;i++)r[n.dwdEvtWorkOrderDtlEntities[i].indexName]=n.dwdEvtWorkOrderDtlEntities[i].indexId;this.indexObj=r,this.$set(this.allChartData,"vocExperience",{data:n.dwdEvtWorkOrderDtlEntities||[],remarkData:n})}catch(n){this.$set(this.allChartData,"vocExperience",{data:[],remarkData:{}}),console.log(n)}this.allChartData.vocExperience.loading=!1},requestChange(e){this.allChartData.topQuestion.remarkData.selectFeelTag=e.selectFeelTag,this.allChartData.topQuestion.remarkData.selectSortType=e.selectSortType,this.getTopQuestion()},async getTopQuestion(){this.allChartData.topQuestion.loading=!0;try{var e=JSON.parse(JSON.stringify(this.sendData));e["emotionAttribute"]=this.allChartData.topQuestion.remarkData.selectFeelTag,e["sortName"]=this.allChartData.topQuestion.remarkData.selectSortType,e["topN"]=30;const t=await this.$store.dispatch("attributeAnalysisTopKeywordList",e);this.allChartData.topQuestion.data=t||[]}catch(t){console.log(t),this.allChartData.topQuestion.data=[]}this.allChartData.topQuestion.loading=!1},async getPopulationCharacteristics(){this.allChartData.populationCharacteristics.loading=!0;try{const i=await this.$store.dispatch("attributeAnalysisFeatureAnalysis",this.sendData);var e,t=["性别","年龄段","客户分类","最近一次购车车龄","客户常驻地所在省份","最高学历"],a=[];for(var s in i)if("total"==s&&(e=i[s]),-1!=t.indexOf(s))for(var r=0;r<i[s].length;r++)a.push({type:s,secondType:i[s][r]["keyWord"],mentionRate:i[s][r]["mentionRate"]});this.$set(this.allChartData.populationCharacteristics,"data",{total:e,details:a})}catch(i){console.log(i)}this.allChartData.populationCharacteristics.loading=!1},async getDataSource(){this.allChartData.datasourceAnalysis.loading=!0;try{const e=await this.$store.dispatch("attributeAnalysisDataSourceConsitute",this.sendData);this.allChartData.datasourceAnalysis.data=e}catch(e){console.log(e)}this.allChartData.datasourceAnalysis.loading=!1},async getAreaData(){this.allChartData.areaAnalysis.loading=!0;try{const e=await this.$store.dispatch("attributeAnalysisAreaCompare",this.sendData);this.allChartData.areaAnalysis.data=e}catch(e){console.log(e)}this.allChartData.areaAnalysis.loading=!1},async getIndexAyalysisData(){this.allChartData.indexAnalysis.loading=!0;try{const s=await this.$store.dispatch("attributeAnalysisIndexAnalysis",this.sendData),r=s.dwdEvtWorkOrderDtls||[],i=s.dwdEvtWorkOrderDtlsMom||[],n=s.treeData||[];var e={firstIndexId:"一级分类",secondIndexId:"二级分类",thirdIndexId:"三级分类",fourIndexId:"四级分类"},t=["firstIndexId","secondIndexId","thirdIndexId","fourIndexId"];e=e[this.sendData.next];var a=0==t.indexOf(this.sendData.next)?this.sendData.indexTypeName:this.sendData[t[t.indexOf(this.sendData.next)-1]];this.$set(this.allChartData,"indexAnalysis",{loading:!1,data:{data:r.concat(i),detail:r,detailMom:i},remarkData:n,remarkData2:e,remarkData3:a})}catch(s){console.log(s)}this.allChartData.indexAnalysis.loading=!1},async getTextDetails(){this.allChartData.textDetails.loading=!0;var e=JSON.parse(JSON.stringify(this.sendData));for(var t in this.textDetailPage)e[t]=this.textDetailPage[t];try{const t=await this.$store.dispatch("attributeAnalysisOriginalDetails",e)||{};this.allChartData.textDetails.data=t.list||[],this.allChartData.textDetails.remarkData=t||{}}catch(a){this.allChartData.textDetails.data=[],this.allChartData.textDetails.remarkData={},console.log(a)}this.allChartData.textDetails.loading=!1},textDetailsChangeHandle(e){for(var t in e)this.textDetailPage[t]=e[t];this.getTextDetails()},standardKeywordDetailHandle(e){var t=JSON.parse(JSON.stringify(this.sendData)),a=this.$publicHandle.makeShowSendData(t,this);t["standardKeyword"]=e,a["标准关键词"]=e;var s={show:!0,component:"keywordDetailsDrawer",sendData:t,showSendData:a,head:"标准关键词详情",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",s)},search(e,t){this.sendData=JSON.parse(JSON.stringify(e)),this.sendData["changanGroup"]=!0;var a={};a["search_"+this.nowTab]=this.sendData,t&&(a["nowTab"]=this.nowTab,this.$publicHandle.setStorage("attribution_analysis",a)),delete this.sendData.brandCodes,delete this.sendData.standardKeywordsSearchType,this.sendData.brandNames.length?this.sendData.changanGroup=!1:this.sendData.changanGroup=!0,this.textDetailPage={pageNum:1,pageSize:10},this.getDataController()},handleClick(){this.getStorage(),this.searchKey++},seeUserDetailHandle(e){var t={userId:e.oneId},a={userName:e.userName},s=this.$publicHandle.makeShowSendData(a,this),r={show:!0,component:"textDetailsDrawer",sendData:t,showSendData:s,head:"原文明细",key:(new Date).getTime()};this.$store.commit("SET_OVERALL_DRAWER",r)}}},o=l,d=a(1001),c=(0,d.Z)(o,s,r,!1,null,"50039be1",null),h=c.exports},6427:function(e,t){"use strict";t["Z"]={details:{post_comments:[{label:"data_id",keys:["data_id"]},{label:"数据来源",keys:["data_source_name","is_outer"]},{label:"创建时间",keys:["create_time"]},{label:"帖子类型",keys:["posts_type"]},{label:"主贴/回帖",keys:["ifMainPost"]},{label:"帖子标题",keys:["posts_title"]},{label:"帖子内容",keys:["posts_content"]},{label:"评价内容",keys:["comment"]},{label:"分享量",keys:["share_cnt"]},{label:"浏览量",keys:["views_cnt"]},{label:"评论量",keys:["comment_cnt"]},{label:"帖子发布时间",keys:["posts_publish_time"]},{label:"帖子评论时间",keys:["comment_time"]},{label:"标准关键词",keys:["standardkeyword"]},{label:"原文链接",keys:["posts_link"]},{label:"姓名",keys:["name"]},{label:"是否车主",keys:["is_car_owner"]},{label:"客户类型",keys:["user_type"]},{label:"省-市-区",keys:["province","city","area"]},{label:"手机号",keys:["mobile"]},{label:"车架号",keys:["vin"]},{label:"车主名称",keys:["car_owner_name"]},{label:"品牌/车系/车型",keys:["brand_name","series_name","model_name"]},{label:"细分市场",keys:["market_name"]},{label:"经销商名称",keys:["dealer_name"]}],work_order:[{label:"data_id",keys:["data_id"]},{label:"数据来源",keys:["data_source_name","is_outer"]},{label:"创建时间",keys:["create_time"]},{label:"工单ID",keys:["order_id"]},{label:"来源",keys:["level1_source","level2_source"]},{label:"类型",keys:["common_type"]},{label:"分类",keys:["level1_category","level2_category","level3_category","level4_category"]},{label:"标题",keys:["title"]},{label:"内容",keys:["content"]},{label:"标准关键词",keys:["standardkeyword"]},{label:"姓名",keys:["name"]},{label:"是否车主",keys:["is_car_owner"]},{label:"客户类型",keys:["user_type"]},{label:"省-市-区",keys:["province","city","area"]},{label:"手机号",keys:["mobile"]},{label:"车架号",keys:["vin"]},{label:"车主名称",keys:["car_owner_name"]},{label:"品牌/车系/车型",keys:["brand_name","series_name","model_name"]},{label:"细分市场",keys:["market_name"]},{label:"经销商名称",keys:["dealer_name"]}],feedback:[{label:"data_id",keys:["data_id"]},{label:"数据来源",keys:["data_source_name","is_outer"]},{label:"创建时间",keys:["create_time"]},{label:"类型",keys:["common_type"]},{label:"标题",keys:["title"]},{label:"内容",keys:["content"]},{label:"业务来源",keys:["business_source"]},{label:"目录名称",keys:["catalogue"]},{label:"一级目录",keys:["primary_directory"]},{label:"标签名称",keys:["label_name"]},{label:"分类",keys:["property","property2","property3"]},{label:"原文链接",keys:["url"]},{label:"标准关键词",keys:["standardkeyword"]},{label:"姓名",keys:["name"]},{label:"是否车主",keys:["is_car_owner"]},{label:"客户类型",keys:["user_type"]},{label:"省-市-区",keys:["province","city","area"]},{label:"手机号",keys:["mobile"]},{label:"车架号",keys:["vin"]},{label:"车主名称",keys:["car_owner_name"]},{label:"品牌/车系/车型",keys:["brand_name","series_name","model_name"]},{label:"细分市场",keys:["market_name"]},{label:"经销商名称",keys:["dealer_name"]}],consulting_service:[{label:"data_id",keys:["data_id"]},{label:"数据来源",keys:["data_source_name","is_outer"]},{label:"创建时间",keys:["create_time"]},{label:"类型",keys:["common_type"]},{label:"标题",keys:["title"]},{label:"内容",keys:["content"]},{label:"标准关键词",keys:["standardkeyword"]},{label:"姓名",keys:["name"]},{label:"是否车主",keys:["is_car_owner"]},{label:"客户类型",keys:["user_type"]},{label:"省-市-区",keys:["province","city","area"]},{label:"手机号",keys:["mobile"]},{label:"车架号",keys:["vin"]},{label:"车主名称",keys:["car_owner_name"]},{label:"品牌/车系/车型",keys:["brand_name","series_name","model_name"]},{label:"细分市场",keys:["market_name"]},{label:"经销商名称",keys:["dealer_name"]}],questionnaire:[{label:"data_id",keys:["data_id"]},{label:"数据来源",keys:["data_source_name","is_outer"]},{label:"创建时间",keys:["create_time"]},{label:"业务类型",keys:["first_point"]},{label:"业务场景",keys:["second_point"]},{label:"题目/内容",keys:["title"]},{label:"答案(分数)",keys:["answer_fraction"]},{label:"答案(文本)",keys:["answer_content"]},{label:"原文链接",keys:["url"]},{label:"标准关键词",keys:["standardkeyword"]},{label:"姓名",keys:["name"]},{label:"是否车主",keys:["is_car_owner"]},{label:"客户类型",keys:["user_type"]},{label:"省-市-区",keys:["province","city","area"]},{label:"手机号",keys:["mobile"]},{label:"车架号",keys:["vin"]},{label:"车主名称",keys:["car_owner_name"]},{label:"品牌/车系/车型",keys:["brand_name","series_name","model_name"]},{label:"细分市场",keys:["market_name"]},{label:"经销商名称",keys:["dealer_name"]}]},summary:{post_comments:{title:"postsTitle",content:"postsContent",comment:"comment",answer_fraction:"answerFraction",answer_content:"answerContent"},work_order:{title:"title",content:"content",comment:"comment",answer_fraction:"answerFraction",answer_content:"answerContent"},feedback:{title:"title",content:"content",comment:"comment",answer_fraction:"answerFraction",answer_content:"answerContent"},consulting_service:{title:"title",content:"content",comment:"comment",answer_fraction:"answerFraction",answer_content:"answerContent"},questionnaire:{title:"title",content:"answer_content",comment:"comment",answer_fraction:"answerFraction",answer_content:"answerContent"}}}},8116:function(e,t,a){"use strict";var s=a(5959),r=a.n(s),i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{id:"app","element-loading-text":"获取网站基础信息中..."}},[e.routerData.length&&!e.loading?s("router-view"):e._e(),e.routerData.length&&!e.loading?s("water-mark"):e._e(),e.routerData.length||e.loading?e._e():s("no-data",{attrs:{content:"暂无体验权限"}}),s("el-drawer",{attrs:{withHeader:!1,visible:e.overallDrawerVisible,direction:"rtl","before-close":e.clearDrawer,"show-close":!1,size:"85%"}},[s("div",{staticClass:"drawer-head p-l-20 p-r-20 p-t-10"},[s("div",{staticClass:"drawer-title"},[s("span",{staticClass:"drawer-head-title"},[e._v(e._s(e.drawerHead))]),"analysis"==e.overallDrawerComponent||"depthAnalysis"==e.overallDrawerComponent?s("el-button",{staticClass:"jump-into-page",attrs:{type:"text"},on:{click:e.jumpMinePage}},[e._v(e._s("depthAnalysis"==e.overallDrawerComponent?"进入深度分析页面查看":"进入归因分析页面查看"))]):e._e(),s("span",{staticClass:"drawer-head-close el-icon-close",on:{click:e.clearDrawer}})],1),s("div",{staticClass:"show-send-data"},[e.allOverDrawer.length>1?s("el-button",{staticClass:"m-r-10",attrs:{type:"primary",size:"mini"},on:{click:e.closeDrawer}},[s("img",{staticClass:"center",attrs:{src:a(16),alt:""}}),s("span",{staticClass:"center m-l-5"},[e._v("上一步")])]):e._e(),s("span",{staticClass:"show-send-data-name"},[s("span",{directives:[{name:"show",rawName:"v-show",value:Object.keys(e.overallDrawerShowSendData).length,expression:"Object.keys(overallDrawerShowSendData).length"}]},[e._v("已选：")])]),s("div",{staticClass:"show-select-area"},e._l(e.overallDrawerShowSendData,(function(e,t,a){return s("show-select",{key:a,attrs:{type:"info",size:"small",name:t,value:e}})})),1)],1)]),s("div",{staticClass:"drawer-body p-r-20 p-b-20 p-l-20"},[s(e.overallDrawerComponent,{key:e.overallDrawerComponentKey,tag:"component",attrs:{imComeSendData:e.overallDrawerSendData,isAsComponent:!0}})],1)])],1)},n=[],l=a(1232),o=a(4871),d=a(4261),c=a(661),h=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"water-mark__box"},[e.user.length>1&&e.user[0].userName?a("div",{staticClass:"box__ul"},e._l(e.user,(function(t,s){return a("div",{key:s,staticClass:"box__li"},[a("span",[e._v(e._s(t.userName))]),a("span",[e._v("(")]),a("span",[e._v(e._s(t.userAccount))]),a("span",[e._v(") ")])])})),0):e._e()])},m=[],u={name:"WaterMark",components:{},props:{},data(){return{}},computed:{user(){return new Array(100).fill(this.$store.getters.userRecord)}},watch:{},mounted(){},methods:{}},p=u,y=a(1001),g=(0,y.Z)(p,h,m,!1,null,"6326a69c",null),v=g.exports,f={components:{analysis:l["default"],depthAnalysis:o["default"],keywordDetailsDrawer:d.Z,textDetailsDrawer:c.Z,waterMark:v},data(){return{loading:!1,drawerHistroyBody:!1}},computed:{allOverDrawer(){var e=this.$store.state.overallDrawerData;return e},nowOverDrawer(){var e=this.$store.state.overallDrawerData,t=e[e.length-1]||{};return t.key=(new Date).getTime(),this.drawerHistroyBody=!1,e[e.length-1]||{}},overallDrawerVisible(){return this.nowOverDrawer.show},drawerHead(){return this.nowOverDrawer.head},overallDrawerComponent(){return this.nowOverDrawer.component},overallDrawerSendData(){var e=this.nowOverDrawer.sendData||{},t=e.startDate||"",a=e.endDate||"";return 2!=t.split("-").length&&2!=a.split("-").length||(t=this.$moment(t).startOf("month").format("YYYY-MM-DD"),a=this.$moment(a).endOf("month").format("YYYY-MM-DD"),e.startDate=t,e.endDate=a),e},overallDrawerComponentKey(){return this.nowOverDrawer.key},overallDrawerShowSendData(){var e=this.nowOverDrawer.showSendData||{};return"keywordDetailsDrawer"==this.overallDrawerComponent&&(delete e["度量指标"],delete e["指标体系"]),e},routerData(){return this.$store.state.menuData}},watch:{$route:{handler:function(e,t){"/voc_board/insight_report"!=e.path&&"/self_help_analysis/detail_enquiry"!=e.path&&"/data_closed_loop/information_collection"!=e.path&&this.recordRoute({visitUrl:e.path,menuId:e.meta.menuId})},deep:!0}},created(){this.clearStorage(),this.init()},methods:{changeDrawerHistroyBody(){this.drawerHistroyBody=!this.drawerHistroyBody},drawerHistoryTo(e){this.$store.commit("MODIFY_OVERALL_DRAWER",e+1)},clearStorage(){const e=localStorage.getItem("version"),t={NODE_ENV:"production",VUE_APP_ENV:"prod",VUE_APP_MAP:"false",VUE_APP_OUTPUT:"prod",VUE_APP_TASK_URL:"http://cmp.changan.com/newTask/NewTaskDetails.jsf?taskId=",BASE_URL:""}.VUE_APP_VERSION;e!==t&&(localStorage.clear(),localStorage.setItem("version",t))},async init(){this.loading=!0;try{await Promise.all([this.$store.dispatch("userGetLoginUser"),this.$store.dispatch("getBrands"),this.$store.dispatch("getSeries"),this.$store.dispatch("getMarket"),this.$store.dispatch("getDataSource"),this.$store.dispatch("getBrandGroups"),this.$store.dispatch("getIndexSystem"),this.$store.dispatch("getProvinceList"),this.$store.dispatch("allTaskDepartment"),this.$store.dispatch("allTaskInCharge"),this.$store.dispatch("getChartsList")]),this.$store.dispatch("getModelList"),this.loading=!1}catch(e){console.log(e),this.loading=!1}},closeDrawer(){this.$store.commit("DELETE_OVERALL_DRAWER")},clearDrawer(){this.$store.commit("CLEAR_OVERALL_DRAWER")},jumpMinePage(){for(var e="",t=this.overallDrawerSendData,a=[],s=["startDate","endDate","firstIndexId","secondIndexId","thirdIndexId","brandNames","carOwner","dataSources","dateType","indexType","indexTypeName","marketNames","measureIndex","modelNames","provinces","seriesNames"],r=0;r<s.length;r++)if(!Array.isArray(t[s[r]])&&t[s[r]]||(Array.isArray(t[s[r]])&&t[s[r]]).length){var i=s[r]+"=";Array.isArray(t[s[r]])?i+=t[s[r]].join(","):i+=t[s[r]],a.push(i)}a=a.join("&"),"analysis"==this.overallDrawerComponent?e=this.$router.resolve({path:"/voice_of_customer/mine_product_analysis/attribution_analysis?customSearch=true&"+a}):"depthAnalysis"==this.overallDrawerComponent&&(e=this.$router.resolve({path:"/voice_of_customer/competitive_products_analysis/depth_analysis?customSearch=true&"+a})),window.open(e.href,"_blank")},recordRoute(e){this.$store.dispatch("visitLogAdd",e)}}},b=f,w=(0,y.Z)(b,i,n,!1,null,null,null),S=w.exports,D=a(311),x=a.n(D),k=a(2631);x().use(k.Z);const C=[],T=k.Z.prototype.push;k.Z.prototype.push=function(e){return T.call(this,e).catch((e=>e))};const A=new k.Z({routes:C});var _=A,N=a(7149);const I=a(9324);async function O(){var e=await N.Z.dispatch("userMenuGet");e=e.record||[],N.Z.commit("SET_MENU_DATA",e);const t=I.formatRouter(e);_.addRoutes(t),_.options.routes=t}O();var R=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"now-path"},e._l(e.data,(function(t,s){return a("span",{key:s},[e._v(" "+e._s(t)+" "),s+1!=e.data.length?a("span",{staticClass:"split"},[e._v("/")]):e._e()])})),0)},E=[],L={props:{data:{type:Array,default(){return[]}}}},M=L,V=(0,y.Z)(M,R,E,!1,null,"890e5e9c",null),B=V.exports,$=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"public-border-radius public-white public-border-size m-t-20",class:e.screenShoot?"":"chart-box"},[s("div",{staticClass:"top"},[s("div",{staticClass:"title"},[s("span",{staticClass:"public-sign m-r-6"}),s("span",{staticClass:"dynamic-title"},[e._v(e._s(e.dynamicTitle))]),s("span",[e._v(e._s(e.title))]),!e.screenShoot&&e.chartId?s("el-popover",{attrs:{width:"1000",placement:"bottom-start",trigger:"click"}},[s("div",{staticClass:"p-10"},[e.secondTooltipData.length?s("div",{staticClass:"each-tooltip-div m-b-10"},[s("h3",{staticClass:"m-b-10 f-s-14"},[e._v("指标类解释")]),s("el-table",{attrs:{size:"mini",data:e.secondTooltipData}},[s("el-table-column",{attrs:{prop:"name",width:"150",label:"名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"f-s-12"},[e._v(e._s(t.row.name))])]}}],null,!1,1788711238)}),s("el-table-column",{attrs:{prop:"value",label:"内容"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.row.value,placement:"top","popper-class":"item-tooltip"}},[s("div",{staticClass:"tooltip-content"},[e._v(e._s(t.row.value))])])]}}],null,!1,751318057)})],1)],1):e._e(),e.firstTooltipData.length?s("div",{staticClass:"each-tooltip-div"},[s("h3",{staticClass:"m-b-10 f-s-14"},[e._v("非指标类解释")]),s("el-table",{attrs:{size:"mini",data:e.firstTooltipData}},[s("el-table-column",{attrs:{prop:"name",width:"150",label:"名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"f-s-12"},[e._v(e._s(t.row.name))])]}}],null,!1,1788711238)}),s("el-table-column",{attrs:{prop:"value",label:"内容"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.row.value,placement:"top","popper-class":"item-tooltip"}},[s("div",{staticClass:"tooltip-content"},[e._v(e._s(t.row.value))])])]}}],null,!1,751318057)})],1)],1):e._e()]),s("span",{directives:[{name:"show",rawName:"v-show",value:e.tooltipShow,expression:"tooltipShow"}],staticClass:"el-icon-question voc-question m-l-10 f-s-18",attrs:{slot:"reference"},slot:"reference"})]):e._e(),s("span",{staticClass:"m-l-10 f-s-12",staticStyle:{color:"#909090","font-weight":"400"}},[e._v(e._s(e.vocExplain))]),s("div",{staticClass:"function-area piblic-white"},[e.screenShoot?e._e():e._t("search"),e.needDownLoad&&!e.screenShoot&&e.isDownload?s("el-dropdown",{attrs:{disabled:!e.isShowDropdown},on:{command:e.handleCommand}},[s("el-button",{staticClass:"public-button-width",attrs:{size:"mini"}},[s("div",[s("img",{staticClass:"m-r-5",staticStyle:{"vertical-align":"middle"},attrs:{src:e.isShowDropdown?a(9455):a(8795),alt:""}}),s("span",{staticStyle:{"vertical-align":"middle"}},[e._v("导出")])])]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.downloadChart?s("el-dropdown-item",{attrs:{command:"detail"}},[e._v("报表导出")]):e._e(),e.downloadDetail?s("el-dropdown-item",{attrs:{command:"clientDetail"}},[e._v("明细导出")]):e._e()],1)],1):e._e()],2)],1)]),s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"p-l-20 p-r-20"},[e.loading?s("div",[s("no-data",{attrs:{content:"获取数据中..."}})],1):[e.isShowDropdown?s("div",[e._t("content")],2):s("div",[s("no-data")],1)]],2)])},F=[],P={props:{title:{type:String,default:""},chartId:{type:String,default:""},dynamicTitle:{type:String,default:""},data:{type:[Object,Array]},downloadChart:{type:Boolean,default:!0},downloadDetail:{type:Boolean,default:!0},loading:{type:Boolean},tooltipShow:{type:Boolean,default:!0},downloadData:{type:Array},needDownLoad:{type:Boolean,default:!0},tooltipArr:{type:Array,default:()=>[]},vocExplain:{type:String,default:()=>""}},computed:{isShowDropdown(){return this.loading?!this.loading:!!this.data&&(Array.isArray(this.data)?0!=this.data.length:this.data.data&&0!=this.data.data.length)},screenShoot(){return this.$store.state.screenShoot},firstTooltipData(){var e=this.$store.state.chartsList[this.chartId]||{value:{}};return e.value[0]||[]},secondTooltipData(){var e=this.$store.state.chartsList[this.chartId]||{value:{}};return e.value[1]||[]},isDownload(){let e=this.$store.state.user||{};return e=e.record||{},"1"===e.isDownload}},watch:{tooltipArr:{handler(e){},immediate:!0}},methods:{handleCommand(e){this.$emit("download",e)}}},Z=P,K=(0,y.Z)(Z,$,F,!1,null,"5a59af63",null),j=K.exports,H=a(3795),J=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"logo-container",style:{marginBottom:e.isCollapse?"0":""}},[e._m(0)]),a("div",{staticClass:"menu-container"},[a("el-menu",{ref:"menu",attrs:{"unique-opened":!0,collapse:e.isCollapse,"default-active":e.nowPath,router:"","background-color":"#212B36","text-color":"#C2CDD6","active-text-color":"#FFFFFF"}},[e._l(e.navData,(function(e,t){return[a("side-bar-item",{key:t,attrs:{data:e,index:t}})]}))],2)],1)])},Y=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"sidebar-logo-link"},[s("img",{staticClass:"sidebar-logo",attrs:{src:a(6949),alt:""}})])}],W={computed:{nowPath(){var e;let t=this.$route.path;return-1!=t.indexOf("/voc_board/insight_report")?(e="/voice_of_customer/voc_board",t=t.split("/"),t=t.slice(0,t.length-1).join("/")):-1!=t.indexOf("/self_help_analysis/detail_enquiry")?(e="/voice_of_customer/self_help_analysis",t=t.split("/"),t=t.slice(0,t.length-1).join("/")):-1!=t.indexOf("/data_closed_loop")?(e="/voice_of_customer/data_closed_loop",t=t.split("/"),t=t.slice(0,t.length-1).join("/")):-1==t.indexOf("/integrated_management/index_system_management")&&-1==t.indexOf("/integrated_management/data_source_management")||(e="/voice_of_customer/integrated_management",t=t.split("/"),t=t.slice(0,t.length-1).join("/")),this.$nextTick((()=>{this.$refs.menu.open(e)})),t!==this.$route.path?t:this.$route.path},navData(){let e=[];const t=this.$store.state.menuData;return t.forEach((t=>{-1!==this.nowPath.indexOf(t.path)&&(e=e.concat(t.children))})),e},isCollapse(){return this.$store.state.isCollapse}}},U=W,G=(0,y.Z)(U,J,Y,!1,null,"6c292246",null),z=G.exports,q=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.data.children&&e.data.children.length&&!e.data.page?a("el-submenu",{key:e.index,attrs:{index:e.data.path}},[a("template",{slot:"title"},[e.data.meta.icon?a("img",{staticClass:"m-r-8",staticStyle:{width:"14px"},attrs:{src:e.data.meta.icon}}):e._e(),a("span",{staticClass:"title",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.data.meta.title))])]),e._l(e.data.children,(function(e,t){return a("side-bar-item",{key:t,attrs:{data:e,index:t}})}))],2):a("el-menu-item",{key:e.index,attrs:{index:e.data.path}},[e.data.meta.icon?a("img",{staticClass:"m-r-8",staticStyle:{width:"14px"},attrs:{src:e.data.meta.icon}}):e._e(),a("span",{staticClass:"title",attrs:{slot:"title"},slot:"title"},[e._v(e._s(e.data.meta.title))])])],1)},Q=[],X={props:{data:{type:Object,default(){return{}}},index:{type:Number,default:1}}},ee=X,te=(0,y.Z)(ee,q,Q,!1,null,"531dd77a",null),ae=te.exports,se=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.data?a("div",{staticClass:"tips"},[e._v("* "+e._s(e.data))]):e._e()},re=[],ie={props:{data:{type:String,default:""}}},ne=ie,le=(0,y.Z)(ne,se,re,!1,null,"c4dfebe2",null),oe=le.exports,de=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"top-head"},[s("el-button",{staticClass:"m-r-5",attrs:{size:"small",type:"text"},on:{click:e.changeMenu}},[e.isCollapse?s("img",{attrs:{src:a(4342),alt:""}}):s("img",{attrs:{src:a(1699),alt:""}})]),s("now-path",{attrs:{data:e.nowPathData}}),s("tips",{staticClass:"m-l-10",attrs:{data:e.routeTips}}),s("div",{staticClass:"user-info"},[s("el-dropdown",{attrs:{size:"medium",placement:"top"},on:{command:e.handleCommand}},[s("div",{staticClass:"dropdown-link"},[s("span",{staticClass:"m-r-10"},[e._v(e._s(e.user))]),s("span",{staticClass:"el-icon-arrow-down"})]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[s("el-dropdown-item",{attrs:{command:"logout"}},[e._v("退出登录")])],1)],1)],1)],1)},ce=[],he={computed:{routeMeta(){return this.$route.meta||{}},nowPathData(){return this.routeMeta.routeArr||[]},routeTips(){return this.routeMeta.menuTips||""},isCollapse(){return this.$store.state.isCollapse},user(){var e=this.$store.state.user||{},t=e.record||{},a=t.userName||"";return a}},methods:{changeMenu(){var e=this.$store.state.isCollapse;this.$store.commit("SET_IS_COLLAPSE",!e)},async handleCommand(e){try{await this.$store.dispatch("getLogoutUrl")}catch(t){console.log(t)}}}},me=he,ue=(0,y.Z)(me,de,ce,!1,null,"8f919966",null),pe=ue.exports,ye=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"feel-tag"},["正面"==e.data?a("div",{staticClass:"public-inline-block green-tag  p-l-8 p-r-8"},[e._v(e._s(e.showData||e.data))]):e._e(),"中性"==e.data?a("div",{staticClass:"public-inline-block blue-tag  p-l-8 p-r-8"},[e._v(e._s(e.showData||e.data))]):e._e(),"负面"==e.data?a("div",{staticClass:"public-inline-block red-tag  p-l-8 p-r-8"},[e._v(e._s(e.showData||e.data))]):e._e(),"灰色"==e.data?a("div",{staticClass:"public-inline-block gray-tag  p-l-8 p-r-8"},[e._v(e._s(e.showData||e.data))]):e._e()])},ge=[],ve={props:{data:{type:String,default:""},showData:{type:String,default:""}}},fe=ve,be=(0,y.Z)(fe,ye,ge,!1,null,"31fa5b15",null),we=be.exports,Se=a(3557),De=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"public-border-top"},[a("div",{staticClass:"triangle"}),e._t("content")],2)},xe=[],ke={},Ce=(0,y.Z)(ke,De,xe,!1,null,"ffd5ba78",null),Te=Ce.exports,Ae=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"no-data",class:e.customClass,style:{"min-height":e.minHeight+"px"}},[s("img",{staticClass:"img",attrs:{src:a(8216),alt:""}}),s("span",{staticClass:"no-data-content"},[e._v(e._s(e.content))]),e._t("noDataHandleButton")],2)},_e=[],Ne={props:{content:{type:String,default:"暂无数据"},customClass:{type:String},minHeight:{type:[String,Number],default:"395"}}},Ie=Ne,Oe=(0,y.Z)(Ie,Ae,_e,!1,null,"c0fa6b74",null),Re=Oe.exports,Ee=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"each-show-selected",style:e.bg?"background: rgba(225, 229, 234, .8);":""},[a("span",{staticClass:"name"},[e._v(e._s(e.name)+"：")]),a("el-tooltip",{attrs:{placement:"bottom"}},[a("span",{staticClass:"value"},[e._v(e._s(e.value))]),a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.value)+" ")])])],1)},Le=[],Me={props:{name:{type:String,default:""},value:{type:[Array,String]},bg:{type:Boolean,default:()=>!0}}},Ve=Me,Be=(0,y.Z)(Ve,Ee,Le,!1,null,"1fd3bbc4",null),$e=Be.exports,Fe=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{staticClass:"dialog",class:{"no-body-padding":e.noBodyPadding,"common-padding":e.commonPadding},attrs:{"append-to-body":e.appendToBody,"close-on-click-modal":e.clickModal,"close-on-press-escape":e.pressEscape,center:e.center,title:e.title,visible:e.dialogFormVisibleFlag,width:e.width,"show-close":e.showClose,top:e.top},on:{close:e.dilogClose}},[e._t("title",null,{slot:"title"}),a("div",{staticClass:"dialog-body"},[e._t("option")],2),e._t("button")],2)],1)},Pe=[],Ze={props:{clickModal:{type:Boolean,default:!0},pressEscape:{type:Boolean,default:!0},dialogFormVisible:{type:Boolean,default:!1},appendToBody:{type:Boolean,default:!1},center:{type:Boolean,default:!1},modal:{type:Boolean,default:!0},showClose:{type:Boolean,default:!1},title:{type:String,default:""},width:{type:String,default:"0"},modalFlag:{type:Boolean,default:!0},noBodyPadding:Boolean,commonPadding:Boolean,top:{type:String,default:"7vh"}},computed:{dialogFormVisibleFlag:{get(){return this.dialogFormVisible},set(e){this.$emit("close",e)}}},data(){return{}},methods:{dilogClose(){this.$emit("close")}}},Ke=Ze,je=(0,y.Z)(Ke,Fe,Pe,!1,null,"0d5ee75e",null),He=je.exports,Je=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("span",{staticClass:"mine-split"},[e._v("|")])},Ye=[],We={},Ue=(0,y.Z)(We,Je,Ye,!1,null,"66bb5971",null),Ge=Ue.exports,ze=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-pagination",{staticClass:"pagination",attrs:{background:"","current-page":e.nowPage,"page-sizes":e.pageSizes,"page-size":e.nowPageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalTableDataLength},on:{"size-change":e.sizeChangeHandle,"current-change":e.currentChangeHandle}})},qe=[],Qe={props:{nowPage:{type:Number,default:1},nowPageSize:{type:Number,default:10},totalTableDataLength:{type:Number,default:0},pageSizes:{type:Array,default(){return[10,20,50,100]}}},methods:{sizeChangeHandle(e){this.$emit("change",{page:1,nowPageSize:e})},currentChangeHandle(e){this.$emit("change",{page:e,nowPageSize:this.nowPageSize})}}},Xe=Qe,et=(0,y.Z)(Xe,ze,qe,!1,null,null,null),tt=et.exports,at=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v(e._s("否"==e.data?"内部":"外部"))])},st=[],rt={props:{data:{type:String,default:"否"}}},it=rt,nt=(0,y.Z)(it,at,st,!1,null,null,null),lt=nt.exports,ot=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"flex-center"},[s("div",{staticClass:"mine-step"},e._l(e.data,(function(t,r){return s("div",{key:r,staticClass:"each-mine-step",class:t.id==e.nowStep?"active":"",on:{click:function(a){return e.stepClick(t.id)}}},["success"!=t.status?s("div",{staticClass:"circle"},[e._v(e._s(r+1))]):s("img",{staticClass:"finish-icon",attrs:{src:a(658),alt:""}}),s("div",{staticClass:"step-name",style:{color:"success"==t.status?"rgba(0, 0, 0, 0.85)":""}},[e._v(e._s(t.name))]),r+1!=e.data.length?s("div",{staticClass:"line"}):e._e()])})),0)])},dt=[],ct={props:{data:{type:Array,default(){return[]}},nowStep:{type:String,default:"create"}},methods:{stepClick(e){this.$emit("stepClick",e)}}},ht=ct,mt=(0,y.Z)(ht,ot,dt,!1,null,"47d8b465",null),ut=mt.exports,pt=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploadLoading,expression:"uploadLoading"}],staticClass:"upload-demo",attrs:{"element-loading-text":"努力上传中",multiple:e.multiple,drag:e.drag,accept:e.fileTypes.join(","),"auto-upload":!1,"on-change":e.handleChange,action:"","on-remove":e.handleRemove,"show-file-list":!0,"file-list":e.fileList}},[e.drag?e._e():[a("el-button",{attrs:{slot:"trigger",size:"mini",type:"primary",icon:"el-icon-upload"},slot:"trigger"},[e._v("上传文件")]),e.needDownload?a("el-button",{staticClass:"download-button",attrs:{size:"mini",icon:"el-icon-download",loading:e.downloadLoading},on:{click:e.download}},[e._v("下载模板")]):e._e()],e.drag?[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")]),a("span",{staticClass:"el-upload__tip block-tips",attrs:{slot:"tip"},slot:"tip"},[e._v(e._s(e.mineTips))])])]:e._e()],2),e.uploadLoading?a("el-progress",{attrs:{percentage:e.uploadPercentage}}):e._e(),e.drag&&e.needDownload?a("el-button",{staticClass:"download-button-drag",attrs:{size:"mini",icon:"el-icon-download",loading:e.downloadLoading},on:{click:e.download}},[e._v("下载模板")]):e._e()],1)},yt=[],gt={props:{multiple:{type:Boolean,default:!1},mineTips:{type:String,default:""},fileTypes:{type:Array,default(){return[".txt",".csv",".xls",".xlsx"]}},drag:{type:Boolean,default:!0},needDownload:{type:Boolean,default:!0},fileTemplateUrl:{type:String,default:""},isOss:{type:Boolean,default:!1},fileLimit:{type:[Number,String]}},data(){return{fileList:[],downloadLoading:!1,uploadLoading:!1,fileSplitTimes:0,successUploadTimes:0}},computed:{uploadPercentage(){var e=this.successUploadTimes/this.fileSplitTimes;return Object.is(e,NaN)?0:100*parseFloat(e).toFixed(2)}},methods:{async download(){this.$publicHandle.processEnvAPI(this.fileTemplateUrl)},handleRemove(){this.$emit("remove")},judgeFileType(e){var t="."+e.split(".")[1],a=this.fileTypes.indexOf(t);return-1!=a||(this.$message({type:"error",message:`只能上传${this.fileTypes.join("，")}的文件`}),!1)},getFileMd5(e){var t=e.raw,a=new BMF;return new Promise((async(e,s)=>{a.md5(t,(async(t,a)=>{t?s(t):e(a)}))}))},judgeFileSize(e){return void 0==this.fileLimit||(!(e.size>1024*this.fileLimit*1024)||this.$message({type:"error",message:`只能上传${this.fileLimit}M大小的文件`}))},async handleChange(e,t){if(this.judgeFileType(e.name))if(this.judgeFileSize(e)||(this.fileList=JSON.parse(JSON.stringify(this.fileList))),this.isOss){var a=await this.createFileTask(e);a?(this.fileList=[t[t.length-1]],this.$emit("uploadChange",a)):this.fileList=t.length-2>=0?[t[t.length-2]]:[]}else this.multiple?this.fileList=t:this.fileList=[t[t.length-1]];else this.fileList=JSON.parse(JSON.stringify(this.fileList))},async createFileTask(e){this.uploadLoading=!0,this.successUploadTimes=0;try{const a=await this.getFileMd5(e),s=new FormData;s.append("fileSize",e.size),s.append("filemd5",a),s.append("originalFileName",e.name);const r=await this.$store.dispatch("fileinfoFileInfoUploadCreate",s);var t=await this.sliceFile(e,r.id,a);return this.uploadLoading=!1,t}catch(a){console.log(a),this.uploadLoading=!1}},async sliceFile(e,t,a){var s=!1,r=e.raw;const i=3145728;this.fileSplitTimes=Math.ceil(e.size/i);for(var n=0;n<this.fileSplitTimes;n++){const l=r.slice(n*i,(n+1)*i),o=new FormData;o.append("file",l),o.append("fileSize",e.size),o.append("filemd5",a),o.append("fileId",t),o.append("originalFileName",e.name),s=await this.mineUpload(o)}return s&&(s=t),s},async mineUpload(e){try{return await this.$store.dispatch("fileinfoFileInfoUpload",e),this.successUploadTimes++,!0}catch(t){return!1}}}},vt=gt,ft=(0,y.Z)(vt,pt,yt,!1,null,"592e640a",null),bt=ft.exports,wt=a(3532);x().component("nowPath",B),x().component("chartBox",j),x().component("showCompare",H.Z),x().component("sideBar",z),x().component("sideBarItem",ae),x().component("tips",oe),x().component("topHead",pe),x().component("feelTag",we),x().component("searchCenter",Se.Z),x().component("publicBorderTop",Te),x().component("noData",Re),x().component("showSelect",$e),x().component("mineDialog",He),x().component("mineSplit",Ge),x().component("searchBox",Se.Z),x().component("minePage",tt),x().component("mineOuter",lt),x().component("mineStep",ut),x().component("mineUpload",bt),x().component("keyWords",wt.Z);var St=a(5806);Vue.use(r()),Vue.config.productionTip=!1,Vue.prototype.$echarts=echarts,Vue.prototype.$moment=moment,Vue.prototype.$publicHandle=St.Z,new Vue({router:_,store:N.Z,render:e=>e(S)}).$mount("#app")},9324:function(e,t,a){"use strict";a.r(t),a.d(t,{formatRouter:function(){return r}});var s=a(7149);const r=e=>{const t=(e,s)=>{e.forEach((e=>{r[e.path]={father:s,obj:e};var i=e.meta.icon;e.meta.menuData=e.menuData,e.meta.menuId=e.menuId,-1!=i.indexOf("svg")&&(e.meta.icon=a(9956)(`./${i}`));var n=e.component;0!=n.indexOf("/")&&(n="/"+n),e.component=e=>a.e(563).then(function(){var t=[a(4563)(`./reports${n}`)];e.apply(null,t)}.bind(this))["catch"](a.oe),e.children&&e.children.length&&t(e.children,e.path)}))},r={};t(e,"");var i=[{path:"/",redirect:e[0].children[0].children[0].path}];for(let a=0;a<e.length;a++)i.push({path:e[a].path,redirect:e[a].children[0].children[0].path});return i.push({path:"*",component:e=>a.e(839).then(function(){var t=[a(1839)];e.apply(null,t)}.bind(this))["catch"](a.oe)}),i=i.concat(e),s.Z.commit("SET_ROUTER_OBJ",r),i}},7149:function(e,t,a){"use strict";a.d(t,{Z:function(){return R}});var s=a(311),r=a.n(s),i=a(3822);var n,l=a(5959);const o=axios.create({baseURL:{NODE_ENV:"production",VUE_APP_ENV:"prod",VUE_APP_MAP:"false",VUE_APP_OUTPUT:"prod",VUE_APP_TASK_URL:"http://cmp.changan.com/newTask/NewTaskDetails.jsf?taskId=",BASE_URL:""}.VUE_APP_API_BASE,withCredentials:!0,timeout:3e4,withCredentials:!0,headers:{"X-Requested-With":"XMLHttpRequest"}});o.interceptors.request.use((function(e){return e.headers["Content-Type"]="application/json",e.data instanceof FormData&&(e.headers["Content-Type"]="application/x-www-form-urlencoded"),e})),o.interceptors.response.use((function(e){if("application/force-download"==e.headers["content-type"])return{headers:e.headers,data:e.data};const t=e.data.code,a=e.data.success,s=e.data.data;if(1==t||a)return s;if(e.request.responseURL&&-1!=e.request.responseURL.indexOf("/cas/login"))window.location.reload();else{if("/getLogoutUrl"!=e.config.url)return n&&n.close(),n=(0,l.Message)({type:"error",message:e.data.msg}),Promise.reject("接口返回为错");window.location.href=e.data}}),(function(e){return 401==e.response.status?window.location.reload():(n&&n.close(),n=(0,l.Message)({type:"error",message:e||"未知错误"})),Promise.reject(e)})),axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";var d=o,c=a(5806),h={async overviewDashboardBrandSummary4ChanganWithIndex(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/brandSummary4ChanganWithIndex",method:"POST",data:t});return a},async overviewDashboardBrandSummaryWithIndex(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/brandSummaryWithIndex",method:"POST",data:t});return a},async overviewDashboardBrandSummary(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/brandSummary",method:"POST",data:t});return a},async overviewDashboardBrandSummary4Changan(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/brandSummary4Changan",method:"POST",data:t});return a},async overviewDashboardTerritoryDistribute(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/territoryDistribute",method:"POST",data:t});return a},async overviewDashboardTerritoryDistribute4Changan(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/territoryDistribute4Changan",method:"POST",data:t});return a},async overviewDashboardCustomerVocExperience4ChanganGroup(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/customerVocExperience4ChanganGroup",method:"POST",data:t});return a},async overviewDashboardCustomerVocExperience(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/customerVocExperience",method:"POST",data:t});return a},async overviewDashboardKeywordTopList(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/keywordTopList",method:"POST",data:t});return a},async overviewDashboardCustomerTrendExperience(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/overview/dashboard/customerTrendExperience",method:"POST",data:t});return a}},m={async getBrands(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/brands",method:"GET"});return e.commit("SET_BRAND",a),a},async getSeries(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/series",method:"GET"});return e.commit("SET_SERIES",a),a},async getMarket(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/market",method:"GET"});return e.commit("SET_MARKET",a),a},async getDataSource(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/dataSource",method:"GET"});return e.commit("SET_DATA_SOURCE",a),a},async getBrandGroups(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/brandGroups",method:"GET"});return e.commit("SET_BRAND_GROUPS",a),a},async getIndexSystem(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/indexSystem",method:"GET"});var s={};return c.Z.changeTreeToObj(s,a,!1),e.commit("SET_INDEX_SYSTEMOBJ",s),e.commit("SET_INDEX_SYSTEM",a),a},async getProvinceList(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/provinceList",method:"GET"});return e.commit("SET_PROVICE_LIST",a),a},async getModelList(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/model",method:"GET"});return e.commit("SET_MODEL_LIST",a),a},async allTaskDepartment(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/api/m/task/getAllTaskDepartment",method:"GET"});return e.commit("SET_ALL_TASK_DEPARTMENT",a),a},async allTaskInCharge(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/api/m/task/getAllTaskInCharge",method:"GET"});return e.commit("SET_ALL_TASK_CHARGE",a),a},async getChartsList(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/getChartsList?chartsId="+t,method:"GET"});return a},async getChartsList(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/car/getChartsList",method:"GET"});e.commit("SET_CHARTS_LIST",a)},async getLogoutUrl(e,t){const a=await d({url:"/getLogoutUrl",method:"GET"});return a}},u={async problemLocationSeriesCustomerExperienceDistribute(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/problemLocation/series/customerExperienceDistribute",method:"POST",data:t});return a},async problemLocationSceneCustomerExperienceDistribute(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/problemLocation/scene/customerExperienceDistribute",method:"POST",data:t});return a}},p={async selfBrandCompareBrandCustomerExperienceTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/brand/customerExperienceTrend",method:"POST",data:t});return a},async selfBrandCompareSeriesCustomerExperienceTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/series/customerExperienceTrend",method:"POST",data:t});return a},async selfBrandCompareBrandMentionValueTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/brand/mentionValueTrend",method:"POST",data:t});return a},async selfBrandCompareSeriesMentionValueTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/series/mentionValueTrend",method:"POST",data:t});return a},async selfBrandCompareBrandSourceAnalysis(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/brand/sourceAnalysis",method:"POST",data:t});return a},async selfBrandCompareSeriesSourceAnalysis(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/series/sourceAnalysis",method:"POST",data:t});return a},async selfBrandCompareBrandTopKeywordList(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/brand/topKeywordList",method:"POST",data:t});return a},async selfBrandCompareSeriesTopKeywordList(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/series/topKeywordList",method:"POST",data:t});return a},async selfBrandCompareBrandVocExperience(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/brand/vocExperience",method:"POST",data:t});return a},async selfBrandCompareSeriesVocExperience(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/selfBrandCompare/series/vocExperience",method:"POST",data:t});return a}},y={async attributeAnalysisVocExperienceTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/vocExperienceTrend",method:"POST",data:t});return a},async attributeAnalysisVocExperience(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/vocExperience",method:"POST",data:t});return a},async attributeAnalysisTopKeywordList(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/topKeywordList",method:"POST",data:t});return a},async attributeAnalysisDataSourceConsitute(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/dataSourceConsitute",method:"POST",data:t});return a},async attributeAnalysisMentionValueTrend(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/mentionValueTrend",method:"POST",data:t});return a},async attributeAnalysisWordCloud(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/wordCloud",method:"POST",data:t});return a},async attributeAnalysisAreaCompare(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/areaCompare",method:"POST",data:t});return a},async attributeAnalysisIndexAnalysis(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/indexAnalysis",method:"POST",data:t});return a},async attributeAnalysisFeatureAnalysis(e,t){t=c.Z.objectToFormData(t);const a=d({url:"/attribute/analysis/featureAnalysis",method:"POST",data:t});return a},async attributeAnalysisOriginalDetails(e,t){const a=d({url:"/attribute/analysis/originalDetails",method:"POST",data:t});return a},async detailStKeywordDetail(e,t){const a=d({url:"/selfAnalysis/detail/getStKeywordDetail",method:"POST",data:t});return a},async detailStKeywordTopSeries(e,t){const a=d({url:"/selfAnalysis/detail/getStKeywordTopSeries",method:"POST",data:t});return a},async detailStKeywordDataSource(e,t){const a=d({url:"/selfAnalysis/detail/StKeywordDataSource",method:"POST",data:t});return a}},g={async rivalExperienceBrand(e,t){const a=d({url:"/rival/industry/experience/brand",method:"POST",data:t});return a},async rivalExperienceSeries(e,t){const a=d({url:"/rival/industry/experience/series",method:"POST",data:t});return a},async rivalBrandDetail(e,t){const a=d({url:"/rival/industry/brandDetail",method:"POST",data:t});return a},async rivalSeriesDetail(e,t){const a=d({url:"/rival/industry/seriesDetail",method:"POST",data:t});return a},async rivalContrastVocBrand(e,t){const a=d({url:"/rival/contrast/voc/brand",method:"POST",data:t});return a},async rivalContrastVocGroup(e,t){const a=d({url:"/rival/contrast/voc/group",method:"POST",data:t});return a},async rivalContrastVocIndustry(e,t){const a=d({url:"/rival/contrast/voc/industry",method:"POST",data:t});return a},async rivalContrastVocSeries(e,t){const a=d({url:"/rival/contrast/voc/series",method:"POST",data:t});return a},async rivalVocTrendBrand(e,t){const a=d({url:"/rival/voc/trend/brand",method:"POST",data:t});return a},async rivalVocTrendGroup(e,t){const a=d({url:"/rival/voc/trend/group",method:"POST",data:t});return a},async rivalVocTrendIndustry(e,t){const a=d({url:"/rival/voc/trend/industry",method:"POST",data:t});return a},async rivalVocTrendSeries(e,t){const a=d({url:"/rival/voc/trend/series",method:"POST",data:t});return a},async rivalContrastBrand(e,t){const a=d({url:"/rival/contrast/top/brand",method:"POST",data:t});return a},async rivalContrastSeries(e,t){const a=d({url:"/rival/contrast/top/series",method:"POST",data:t});return a},async rivalContrastDataSourceBrand(e,t){const a=d({url:"/rival/contrast/dataSource/brand",method:"POST",data:t});return a},async rivalContrastDataSourceSeries(e,t){const a=d({url:"/rival/contrast/dataSource/series",method:"POST",data:t});return a},async rivalContrastTopBrand(e,t){const a=d({url:"/rival/contrast/top/brand",method:"POST",data:t});return a},async rivalContrastTopSeries(e,t){const a=d({url:"/rival/contrast/top/series",method:"POST",data:t});return a},async rivalContrastVoc(e,t){const a=d({url:"/rival/contrast/voc",method:"POST",data:t});return a},async rivalContrastVocBrand(e,t){const a=d({url:"/rival/contrast/voc/brand",method:"POST",data:t});return a},async rivalContrastVocGroup(e,t){const a=d({url:"/rival/contrast/voc/group",method:"POST",data:t});return a},async rivalContrastVocIndustry(e,t){const a=d({url:"/rival/contrast/voc/industry",method:"POST",data:t});return a},async rivalContrastVocSeries(e,t){const a=d({url:"/rival/contrast/voc/series",method:"POST",data:t});return a},async rivalAnalysisVocTrend(e,t){const a=d({url:"/rival/analysis/voc/trend",method:"POST",data:t});return a},async rivalAnalysisVoc(e,t){const a=d({url:"/rival/analysis/voc",method:"POST",data:t});return a},async rivalAnalysisTopList(e,t){const a=d({url:"/rival/analysis/topList",method:"POST",data:t});return a},async rivalAnalysisIndexRank(e,t){const a=d({url:"/rival/analysis/index/rank",method:"POST",data:t});return a},async rivalAnalysisDataSource(e,t){const a=d({url:"/rival/analysis/dataSource",method:"POST",data:t});return a},async rivalAnalysisDataSourceKeyword(e,t){const a=d({url:"/rival/analysis/dataSource/keyword",method:"POST",data:t});return a},async rivalAnalysisDataSourceTrend(e,t){const a=d({url:"/rival/analysis/dataSource/trend",method:"POST",data:t});return a},async rivalAnalysisContentList(e,t){const a=d({url:"/rival/analysis/content/list",method:"POST",data:t});return a}},v={async insightExperience(e,t){const a=d({url:"/insight/experience",method:"POST",data:t});return a},async insightPopulation(e,t){const a=d({url:"/insight/population",method:"POST",data:t});return a},async insightTsukkomi(e,t){const a=d({url:"/insight/tsukkomi",method:"POST",data:t});return a},async insightRanking(e,t){const a=d({url:"/insight/ranking",method:"POST",data:t});return a},async insightTopList(e,t){const a=d({url:"/insight/TopList",method:"POST",data:t});return a},async insightRivalCompareBrand(e,t){const a=d({url:"/insight/rivalCompare/brand",method:"POST",data:t});return a},async insightRivalCompareSeries(e,t){const a=d({url:"/insight/rivalCompare/series",method:"POST",data:t});return a},async insightAreaCompare(e,t){const a=d({url:"/insight/areaCompare",method:"POST",data:t});return a}},f={async detailStKeywordDetails(e,t){const a=d({url:"/selfAnalysis/detail/stKeywordDetails",method:"POST",data:t});return a},async detailStKeywordAttention(e,t){const a=d({url:"/selfAnalysis/detail/stKeywordAttention",method:"POST",data:t});return a},async detailAddStKeywordDetailsTask(e,t){const a=d({url:"/selfAnalysis/detail/addStKeywordDetailsTask",method:"POST",data:t});return a},async detailOriginalDetails(e,t){const a=d({url:"/selfAnalysis/detail/originalDetails",method:"POST",data:t});return a},async detailAddOriginalDetailsTask(e,t){const a=d({url:"/selfAnalysis/detail/addOriginalDetailsTask",method:"POST",data:t});return a},async exportTaskListTasks(e,t){const a=d({url:"/exportTask/listTasks",method:"POST",data:t});return a},async exportTaskDownload(e,t){const a=d({url:"/selfAnalysis/selfExportAnalysis/downloadTemplate",method:"GET"});return a},async exportTaskDeleteTask(e,t){const a=d({url:"/exportTask/deleteTask?ids="+t,method:"GET"});return a},async taskCreateWithKtm(e,t){const a=d({url:"/api/m/task/createWithKtm",method:"POST",data:t});return a},async detailChargeList(e,t){const a=d({url:"/selfAnalysis/detail/getChargeList?kw="+t,method:"GET"});return a},async detailDepartmentList(e,t){const a=d({url:"/selfAnalysis/detail/getDepartmentList?kw="+t,method:"GET"});return a},async corpusAddCorpus(e,t){const a=d({url:"/corpus/addCorpus",method:"POST",data:t});return a},async stKeywordSearch(e,t){t["size"]=1e3,""===t.indexTypeName&&(t.indexTypeName="全领域业务");const a=d({url:"/selfAnalysis/detail/stKeywordSearch",method:"POST",data:t});return a},async keywordQueryKwByCorpus(e,t){const a=d({url:"/selfAnalysis/detail/queryKwByCorpus?corpus="+t,method:"GET"});return a},async errorCheck(e,t){const a=d({url:"/api/m/error-check",method:"POST",data:t});return a}},b={async uploadAnalysisFile(e,t){const a=d({url:"/selfAnalysis/selfExportAnalysis/uploadAnalysisFile",method:"POST",data:t});return a},async getAnalysisProgress(e,t){const a=await d({url:"/selfAnalysis/selfExportAnalysis/getAnalysisProgress/?batchId="+t.batchId,method:"GET"});return a},async getAnalysisResult(e,t){const a=d({url:"/selfAnalysis/selfExportAnalysis/getAnalysisResult",method:"POST",data:t});return a}},w={async mDataSourceDetailFullDataSourceDetail(e,t){const a=d({url:"/api/m/data-source-detail/fullDataSourceDetail",method:"GET",data:t});return a},async dataSourceScoreAllDataSourceScore(e,t){const a=d({url:"/api/m/data-source-score/allDataSourceScore",method:"GET"});return a},async questionnaireImportPage(e,t){const a=d({url:"/api/m/questionnaire-import/page",method:"POST",data:t});return a},async questionnaireImportDetailPage(e,t){const a=d({url:"/api/m/questionnaire-import-detail/page",method:"POST",data:t});return a},async questionnaireImportUploadFile(e,t){const a=d({url:"/api/m/questionnaire-import/uploadFile",method:"POST",data:t});return a},async questionnaireImportDownload(e,t){const a=d({url:`/api/m/questionnaire-import/download/${t}`,method:"GET"});return a},async questionnaireImportId(e,t){const a=d({url:`/api/m/questionnaire-import/${t.id}`,method:"GET"});return a},async dataSourceScoreImportUploadFile(e,t){const a=d({url:"/api/m/data-source-score-import/uploadFile",method:"post",data:t});return a},async dataSourceScoreImportId(e,t){const a=d({url:`/api/m/data-source-score-import/${t.id}`,method:"GET"});return a},async dataSourceScoreImportGetImportDetailsId(e,t){const a=d({url:`/api/m/data-source-score-import/getImportDetails/${t.id}`,method:"GET"});return a},async dataSourceScoreDataSourceScoreDetailId(e,t){const a=d({url:`/api/m/data-source-score/dataSourceScoreDetail/${t.id}`,method:"GET"});return a},async dataSourceScoreImportCalSourceScoreId(e,t){const a=d({url:`/api/m/data-source-score-import/calSourceScore/${t.id}`,method:"GET"});return a},async dataSourceScoreDataSourceScore(e,t){const a=d({url:`/api/m/data-source-score/dataSourceScore/${t.id}`,method:"GET"});return a},async dataSourceScoreImportApplyId(e,t){var a=new FormData;a.append("type",t.type);const s=d({url:`/api/m/data-source-score-import/apply/${t.id}`,data:a,method:"PUT"});return s},async dataSourceScoreImportGetDataSourceScoreTmp(e,t){const a=d({url:"/api/m/data-source-score-import/getDataSourceScoreTmp",method:"GET"});return a},async dataSourceScoreImportDownloadTemplate(e,t){const a=d({url:"/api/m/data-source-score-import/downloadTemplate",method:"GET"});return a},async keywordPage(e,t){const a=d({url:"/api/m/keyword/page",method:"POST",data:t});return a},async errorCheckPage(e,t){const a=d({url:"/api/m/error-check/page",method:"POST",data:t});return a},async keywordRemoveLogicalByIds(e,t){const a=d({url:"/api/m/keyword/removeLogicalByIds",method:"DELETE",data:t});return a},async errorCheckDealErrorCheckBatch(e,t){const a=d({url:"/api/m/error-check/dealErrorCheckBatch",method:"POST",data:t});return a},async keywordUpdateStandardByIds(e,t){const a=d({url:"/api/m/keyword/updateStandardByIds",method:"PUT",data:t});return a},async originDetailGetOriginDetail(e,t){const a=d({url:`/api/m/origin-detail/getOriginDetailByDocId/${t.docId}?dataSource=${t.dataSource}`,method:"GET"});return a},async keywordExportExcel(e,t){const a=d({url:"/api/m/keyword/exportExcel",method:"POST",data:t});return a},async queryKeywordUserData(e,t){const a=d({url:"/api/m/keyword/queryKeywordUserData?kw="+t,method:"GET"});return a},async fileImportId(e,t){const a=d({url:`/api/m/file-import/${t.id}`,method:"GET"});return a}},S={async taskQueryByAll(e,t){const a=d({url:"/api/m/task/queryByAll",method:"POST",data:t});return a},async taskPageByMy(e,t){const a=d({url:"/api/m/task/pageByMy",method:"POST",data:t});return a},async taskNameId(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/api/m/task/"+t,method:"GET"});return a},async effectTraceId(e,t){const a=await d({url:"/api/m/task/effectTrace/"+t.sendData+"?trackTime="+t.trackTime,method:"GET"});return a},async deleteTaskNameId(e,t){t=c.Z.objectToFormData(t);const a=await d({url:"/api/m/task/deleteWithKtm/"+t,method:"DELETE"});return a}},D={async getAccountList(e,t){const a=await d({url:"/user/list",method:"post",data:t});return a},async getAddUser(e,t){const a=await d({url:"/user/addUser",method:"post",data:t});return a},async getDeleteUser(e,t){const a=await d({url:"/user/deleteUser",method:"post",data:t});return a},async getSaveUser(e,t){const a=await d({url:"/user/saveUser",method:"post",data:t});return a}},x={async userMenuGet(e,t){const a=d({url:"/user/menu/get",method:"GET",data:t});return a},async getUserByToken(e,t){const a=await d({url:"/changan/department/getUserByToken",method:"GET"});return a},async departmentChanganUsers(e,t){const a=d({url:"/changan/department/getChanganUsers?keyWords="+t.keyWords,method:"GET"});return a},async operationLogAllOperationPage(e){const t=d({url:"/operationLog/getAllOperationPage",method:"POST"});return t},async operationLogListLogs(e,t){const a=d({url:"/operationLog/listLogs",method:"POST",data:t});return a},async visitLogAdd(e,t){const a=d({url:"/visitLog/add",method:"POST",data:t});return a},async userGetLoginUser(e,t){const a=await d({url:"/user/getLoginUser",method:"GET"})||{};return e.commit("SET_USER",a),a},async changanDepartmentGetRoleList(e,t){const a=await d({url:"/changan/department/getRoleList",method:"GET"});return a},async changanDepartmentGetUserRole(e,t){const a=await d({url:`/changan/department/getUserRole?loginId=${t}`,method:"GET"});return a}},k={async subscribeDataList(e,t){const a=d({url:"/subscribe/dataList",method:"POST",data:t});return a},async subscribeDataPage(e,t){const a=d({url:"/subscribe/dataPage",method:"POST",data:t});return a},async subscribeResultDataList(e,t){const a=d({url:"/subscribe/resultDataList",method:"POST",data:t});return a},async subscribeResultDataPage(e,t){const a=d({url:"/subscribe/resultDataPage",method:"POST",data:t});return a},async subscribeCreatorList(e,t){let a={};a.value=t;const s=d({url:"/subscribe/getCreatorList",method:"POST",data:a});return s},async subscribeAddSubscribe(e,t){const a=d({url:"/subscribe/addSubscribe",method:"POST",data:t});return a},async subscribeCopySubscribe(e,t){const a=d({url:"/subscribe/copySubscribe",method:"POST",data:t});return a},async subscribeUpdSubscribe(e,t){const a=d({url:"/subscribe/updSubscribe",method:"POST",data:t});return a},async subscribeDelSubscribe(e,t){const a=d({url:"/subscribe/delSubscribe",method:"POST",data:t});return a}},C={async apiOriginDetail(e,t){const a=d({url:"/api/m/origin-detail/getOriginDetail",method:"POST",data:t});return a},async addOriginDetailTask(e,t){const a=d({url:"/api/m/origin-detail/addOriginDetailTask",method:"POST",data:t});return a},async getIndexSystemManageList(e,t){const a=d({url:"/indexSystemManage/getIndexSystemList",method:"GET"});return a},async getIndexSystemItemManageList(e,t){const a=d({url:"/indexSystemManage/getIndexSystemItemList",method:"POST",data:t});return a},async indexSystemManageAddIndexSystem(e,t){const a=d({url:"/indexSystemManage/addIndexSystem",method:"POST",data:t});return a},async indexSystemManageUpdateIndexSystem(e,t){const a=d({url:"/indexSystemManage/updateIndexSystem",method:"POST",data:t});return a},async indexSystemManageDeleteIndexSystem(e,t){const a=d({url:"/indexSystemManage/deleteIndexSystem",method:"POST",data:t});return a},async indexSystemManageUpdateStatus(e,t){const a=d({url:"/indexSystemManage/updateStatus?id="+t.dataId+"&type="+t.enabled,method:"GET"});return a},async indexSystemManageGetIndexSystemTree(e,t){const a=d({url:"/indexSystemManage/getIndexSystemTree",method:"GET"});return a},async indexSystemManageGetIndexSystemItemTree(e,t){const a=d({url:"/indexSystemManage/getIndexSystemItemTree?indexId="+t.dataId,method:"GET"});return a},async indexSystemManageAddIndexSystemItem(e,t){const a=d({url:"/indexSystemManage/addIndexSystemItem",method:"POST",data:t});return a},async indexSystemManageUpdateIndexSystemItem(e,t){const a=d({url:"/indexSystemManage/updateIndexSystemItem",method:"POST",data:t});return a},async indexSystemManageDeleteIndexSystemItem(e,t){const a=d({url:"/indexSystemManage/deleteIndexSystemItem",method:"POST",data:t});return a},async indexSystemManageUploadIndexItemFile(e,t){const a=d({url:"/indexSystemManage/uploadIndexItemFile",method:"POST",data:t});return a},async indexSystemManageSaveIndexSystemItems(e,t){const a=d({url:"/indexSystemManage/saveIndexSystemItems",method:"POST",data:t});return a},async indexSystemManageMergeIndexSystemItem(e,t){const a=d({url:"/indexSystemManage/mergeIndexSystemItem",method:"POST",data:t});return a},async indexSystemManageExportIndexSystemItemList(e,t){const a=d({url:"/indexSystemManage/exportIndexSystemItemList",method:"POST",data:t});return a}},T={async corpusAddCorpus(e,t){const a=d({url:"/corpus/addCorpus",method:"POST",data:t});return a},async corpusAuditFinsh(e,t){const a=d({url:"/corpus/auditFinsh",method:"POST",data:t});return a},async corpusDataList(e,t){const a=d({url:"/corpus/dataList",method:"POST",data:t});return a},async corpusGetStatusCnt(e,t){const a=d({url:"/corpus/getStatusCnt",method:"POST",data:t});return a},async corpusKeywordDrill(e,t){const a=d({url:"/corpus/keywordDrill",method:"POST",data:t});return a},async corpusMove2Black(e,t){const a=d({url:"/corpus/move2Black",method:"POST",data:t});return a},async corpusSubmitKeywordDrill(e,t){const a=d({url:"/corpus/submitKeywordDrill",method:"POST",data:t});return a},async corpusUpdateBatch(e,t){const a=d({url:"/corpus/updateBatch",method:"POST",data:t});return a},async corpusSubmitBatch(e,t){const a=d({url:"/corpus/submitBatch",method:"POST",data:t});return a},async corpusCancelBlack(e,t){const a=d({url:"/corpus/cancelBlack",method:"POST",data:t});return a},async corpusGetImportFileList(e,t){const a=d({url:"/corpus/getImportFileList",method:"POST",data:t});return a},async corpusDeleteFileById(e,t){const a=d({url:"/corpus/deleteFileById",method:"POST",data:t});return a},async corpusKeywordImport(e,t){const a=d({url:"/corpus/keywordImport",method:"POST",data:t});return a},async corpusImportResult(e,t){const a=d({url:"/corpus/importResult",method:"POST",data:t});return a},async corpusDownloadResult(e,t){const a=await d({url:"/corpus/downloadResult",method:"GET",data:t}),s=a,r=s.headers["content-disposition"].split(";")[1];var i=r.split("=")[1];const n=new Blob([s.data]),l=URL.createObjectURL(n),o=document.createElement("a");o.href=l,o.download=i,o.click(),o.remove()}},A={async indexManageStKeywordStKeywordManageSearch(e,t){const a=d({url:"/indexManage/stKeyword/stKeywordManageSearch",method:"POST",data:t});return a},async indexManageStKeywordList(e,t){const a=d({url:"/indexManage/stKeyword/list",method:"POST",data:t});return a},async indexManageStKeywordDeleteStKeywords(e,t){const a=d({url:"/indexManage/stKeyword/deleteStKeywords",method:"POST",data:t});return a},async indexManageStKeywordGetDepartmentList(e,t){const a=d({url:"/indexManage/stKeyword/getDepartmentList",method:"GET",params:t});return a},async indexManageStKeywordGetChargeList(e,t){const a=d({url:"/indexManage/stKeyword/getChargeList?kw="+t,method:"GET"});return a},async indexManageStKeywordMergeStKeywords(e,t){const a=d({url:"/indexManage/stKeyword/mergeStKeywords",method:"POST",data:t});return a},async indexManageStKeywordSaveStKeywords(e,t){const a=d({url:"/indexManage/stKeyword/saveStKeywords",method:"POST",data:t});return a},async indexManageStKeywordUpdateStKeywords(e,t){const a=d({url:"/indexManage/stKeyword/updateStKeywords",method:"POST",data:t});return a},async indexManageStKeywordUpdateStKeywordsPart(e,t){const a=d({url:"/indexManage/stKeyword/updateStKeywordsPart",method:"POST",data:t});return a},async indexManageStKeywordExportExcel(e,t){const a=d({url:"/indexManage/stKeyword/exportExcel",method:"POST",data:t});return a},async indexManageStKeywordUpdateStKeywordBatch(e,t){const a=d({url:"/indexManage/stKeyword/updateStKeywordBatch",method:"POST",data:t});return a},async indexManageStKeywordUploadFile(e,t){const a=d({url:"/indexManage/stKeyword/uploadFile",method:"POST",data:t});return a}},_={async researchReportUpload(e,t){const a=await d({url:"/research-report/upload",method:"post",data:t});return a},async researchReportDelete(e,t){const a=await d({url:`/research-report/${t.dataId}`,method:"delete"});return a},async researchReport(e,t){const a=await d({url:"/research-report/search",method:"post",data:t});return a}},N=a(4806),I=a.n(N),O=Object.assign(_,A,h,m,u,p,y,g,v,f,S,x,k,b,w,D,C,T);r().use(i.ZP);var R=new i.ZP.Store({state:{brandList:null,seriesList:null,marketList:null,sourceList:null,brandGroupsList:null,indexSystemList:null,indexSystemObj:null,indexSystemItem:null,provinceList:null,allTaskDepartmentList:null,allTaskInChargeList:null,modelList:null,overallDrawerData:[],screenShoot:!1,menuData:"",routerObj:"",chartsList:[],isCollapse:!1,user:""},getters:{userWaterMark:e=>`${I().get(e,"user.record.userName")}(${I().get(e,"user.record.userAccount")})`,userRecord:e=>I().get(e,"user.record")},mutations:{SET_ROUTER_OBJ:(e,t)=>{e.routerObj=t},SET_MENU_DATA:(e,t)=>{e.menuData=t},SET_SCREEN_SHOOT:(e,t)=>{e.screenShoot=t},SET_BRAND:(e,t)=>{e.brandList=t},SET_SERIES:(e,t)=>{e.seriesList=t},SET_MARKET:(e,t)=>{e.marketList=t},SET_DATA_SOURCE:(e,t)=>{e.sourceList=t},SET_BRAND_GROUPS:(e,t)=>{e.brandGroupsList=t},SET_INDEX_SYSTEM:(e,t)=>{e.indexSystemList=t},SET_INDEX_SYSTEMOBJ:(e,t)=>{e.indexSystemObj=t},SET_INDEX_SYSTEM_ITEM:(e,t)=>{e.indexSystemItem=t},SET_PROVICE_LIST:(e,t)=>{e.provinceList=t},SET_MODEL_LIST:(e,t)=>{e.modelList=t},SET_ALL_TASK_DEPARTMENT:(e,t)=>{e.allTaskDepartmentList=t},SET_ALL_TASK_CHARGE:(e,t)=>{e.allTaskInChargeList=t},MODIFY_OVERALL_DRAWER:(e,t)=>{e.overallDrawerData.splice(t)},SET_OVERALL_DRAWER:(e,t)=>{e.overallDrawerData.push(t)},DELETE_OVERALL_DRAWER:(e,t)=>{var a=e.overallDrawerData.length;e.overallDrawerData.splice(a-1,1)},CLEAR_OVERALL_DRAWER:(e,t)=>{e.overallDrawerData=[]},SET_CHARTS_LIST:(e,t)=>{for(var a={},s=0;s<t.length;s++){var r=t[s].chartsId,i=t[s].name,n=t[s].value,l=t[s].indexType,o=t[s].chartsName;void 0==a[r]&&(a[r]={chartsId:r,chartsName:o,value:{1:[],0:[]}}),l&&a[r]["value"][l].push({name:i,value:n})}e.chartsList=a},SET_IS_COLLAPSE:(e,t)=>{e.isCollapse=t},SET_USER:(e,t)=>{e.user=t}},actions:O,modules:{}})},3346:function(e){e.exports={data(){return{feelTagOptions:[{key:"",label:"全部"},{key:"正面",label:"正面"},{key:"中性",label:"中性"},{key:"负面",label:"负面"}],pageNumOptions:[{key:10,label:"显示10条"},{key:20,label:"显示20条"},{key:30,label:"显示30条"}],sortTypeOptions:[{key:"MENTION_DESC",label:"按提及量排序"},{key:"MENTION_ADD_DESC",label:"按提及量变化排序"},{key:"MENTION_CYCLE_DESC",label:"按提及量环比排序"},{key:"MENTION_RATE_DESC",label:"按提及率排序"}]}}}},5806:function(e,t,a){"use strict";a(8675),a(3462);var s=a(5959);t["Z"]={makeDataUnit(e){e=Math.abs(e);var t=!1,a="";return(e>1e4||e<-1e4)&&(e/=1e4,a="万",t=!0,(e>=1e3||e<=-1e3)&&(e/=1e3,a="千万",t=!0,(e>=10||e<=-10)&&(e/=10,a="亿",t=!0))),t&&(e=this.toFixTwo(e)),e=this.Thousandth(e),"NaN"==e.toString()?"-":e+a},formatNum(e){return e=this.toFixTwo(e),e=this.Thousandth(e),e},waterPrint(e,t,a){const s=e.getContext("2d");s.rotate(-25*Math.PI/180),s.font="14px",s.fillStyle="rgba(156, 162, 169, 0.3)",s.textAlign="center",s.textBaseline="middle";for(let n=.5*e.width*-1;n<e.width;n+=200)for(let t=0;t<1.5*e.height;t+=150)s.fillText(a,n,t);let r=e.toDataURL("image/png");const i=document.createElement("a");i.href=r,i.setAttribute("download",t),i.click()},formatPercent(e){return e=parseFloat(e),"NaN"==e.toString()?"-":(e*=100,e=this.toFixTwo(e),e=this.Thousandth(e),e)},Thousandth(e){if(void 0!=e){var t=/\d{1,3}(?=(\d{3})+$)/g;return(e+"").replace(t,"$&,")}return"-"},arrangeLineData(e,t,a,s){for(var r={},i=0;i<t.length;i++)for(var n=e[t[i]],l=0;l<n.length;l++)void 0==r[n[l][a]]&&(r[n[l][a]]={indexName:n[l][a],indexid:n[l]["indexId"]}),r[n[l][a]][t[i]]=n[l][s];for(var l in n=[],r)n.push(r[l]);return n},toFixTwo(e){return"NaN"==parseFloat(e).toString()?"-":parseFloat(e).toFixed(2)},changeFirstLetter(e){for(var t=e.split(" "),a=0;a<t.length;a++)t[a]=t[a][0].toUpperCase()+t[a].substring(1,t[a].length);var s=t.join(" ");return s},objectToFormData(e){return e},changeOverallDrawer(e,t,a,s,r){var i={show:t,title:a,component:s,sendData:r};e.$store.commit("SET_OVERALL_DRAWER",i)},downloadExcel(e,t,a,r,i,n=!0){if(!e.length)return(0,s.Message)({type:"error",message:"暂无数据可供下载！"});i&&t.unshift("排名");let l=JSON.parse(JSON.stringify(e));var o=[t];if(!l||!l[0])return!1;for(var d=0;d<l.length;d++){var c=[];i&&c.push(d+1);for(var h=0;h<a.length;h++)"negativeMentionValue"!=a[h]&&"negativeMentionRate"!=a[h]||(l[d][a[h]]=Math.abs(l[d][a[h]])),-1==a[h].indexOf("Rate")&&-1==a[h].indexOf("momValue")||(l[d][a[h]]=n?this.formatPercent(l[d][a[h]])+"%":this.formatNum(l[d][a[h]])),"experienceValue"==a[h]&&(l[d][a[h]]=this.formatNum(l[d][a[h]])),c.push(l[d][a[h]]);o.push(c)}var m=XLSX.utils.aoa_to_sheet(o);this.openDownloadDialog(this.sheet2blob(m),r+".xlsx")},sheet2blob(e,t){t=t||"sheet1";var a={SheetNames:[t],Sheets:{}};a.Sheets[t]=e;var s={bookType:"xlsx",bookSST:!1,type:"binary"},r=XLSX.write(a,s),i=new Blob([n(r)],{type:"application/octet-stream"});function n(e){for(var t=new ArrayBuffer(e.length),a=new Uint8Array(t),s=0;s!=e.length;++s)a[s]=255&e.charCodeAt(s);return t}return i},openDownloadDialog(e,t){"object"==typeof e&&e instanceof Blob&&(e=URL.createObjectURL(e));var a,s=document.createElement("a");s.href=e,s.download=t||"",window.MouseEvent?a=new MouseEvent("click"):(a=document.createEvent("MouseEvents"),a.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null)),s.dispatchEvent(a)},findIndexId(e,t){for(var a=["firstIndexId","secondIndexId","thirdIndexId","fourIndexId"],s=0;s<e.length;s++)t[a[s]]=e[s];t.next=a[e.length]},changeTreeToObj(e,t,a){for(var s=0;s<t.length;s++){var r=t[s].id,i=t[s].indexName,n=!1===a?r:a+"_"+r;if(e[n]={name:i},t[s].children&&t[s].children.length){if(!1===a)var l=t[s].id;else l=a;this.changeTreeToObj(e,t[s].children,l)}}},makeShowSendData(e,t){var a,s,r,i,n,l=[],o={experienceValue:"体验值",negativeMentionRate:"负面提及率"},d={};(e.startDate&&e.endDate&&(d["时间"]=e["startDate"]+"~"+e["endDate"]),e.brandNames&&e.brandNames.length&&(d["品牌"]="string"===typeof e.brandNames?e.brandNames:e.brandNames.join(",")),e.measureIndex&&(d["度量指标"]=o[e.measureIndex]),e.standardKeyword&&(d["标准关键词"]=e.standardKeyword),e.indexTypeName||(e.indexTypeName="全领域业务"),d["指标体系"]=e.indexTypeName,e.carOwner&&(2!=e.carOwner&&1!=e.carOwner||(d["是否车主"]=2==e.carOwner?"否":1==e.carOwner?"是":"不限")),e.seriesNames&&e.seriesNames.length&&(d["车系"]="string"===typeof e.seriesNames?e.seriesNames:e.seriesNames.join(",")),e.dataSource&&e.dataSource.length&&(d["任务来源"]=e.dataSource.join(",")),e.modelNames&&e.modelNames.length&&(d["车型"]="string"===typeof e.modelNames?e.modelNames:e.modelNames.join(",")),e.marketNames&&e.marketNames.length&&(d["细分市场"]="string"===typeof e.marketNames?e.marketNames:e.marketNames.join(",")),e.provinces&&e.provinces.length&&(d["省份"]="string"===typeof e.provinces?e.provinces:e.provinces.join(",")),e.dataSources&&e.dataSources.length&&(d["数据源"]="string"===typeof e.dataSources?e.dataSources:e.dataSources.join(",")),e.firstIndexId&&1==e.firstIndexId.split(",").length)&&l.push(null===(a=t.$store.state.indexSystemObj[e.indexType+"_"+e.firstIndexId])||void 0===a?void 0:a["name"]);e.secondIndexId&&1==e.secondIndexId.split(",").length&&l.push(null===(s=t.$store.state.indexSystemObj[e.indexType+"_"+e.secondIndexId])||void 0===s?void 0:s["name"]);e.thirdIndexId&&1==e.thirdIndexId.split(",").length&&l.push(null===(r=t.$store.state.indexSystemObj[e.indexType+"_"+e.thirdIndexId])||void 0===r?void 0:r["name"]);e.fourIndexId&&1==e.fourIndexId.split(",").length&&l.push(null===(i=t.$store.state.indexSystemObj[e.indexType+"_"+e.fourIndexId])||void 0===i?void 0:i["name"]);(e.clarity&&(d["清晰度"]=e.clarity),e.emotionAttribute&&(d["情感属性"]=e.emotionAttribute),e.userName&&(d["用户"]=e.userName),e.field&&(d["所属领域"]=e.field),l.length)&&(e.indexTypeName&&(d["指标体系"]=[e.indexTypeName].concat(l)),d["指标体系"]=null===(n=d["指标体系"])||void 0===n?void 0:n.join("-"));return d},checkTimeTooLong(e,t,a){var s=a.$moment(t).format("x"),r=a.$moment(e).format("x"),i=26784e5;return s-r>i?"month":"day"},processEnvAPI(e){window.open(e)},processDetailEnvAPI(e){var t="";t="http://cmp.changan.com/newTask/NewTaskDetails.jsf?taskId="+e+"&SpecialOrgID=8f4ddf6d-507a-4835-8db5-84e1480823f0&_randomicity=972",window.open(t)},compareDate(e,t){var a,s=new Date(e),r=new Date(t);return s<r?(a=!1,a):(a=!0,a)},formatDate(e){let t=new Date(e),a=t.getFullYear(),s=t.getMonth()+1;s=s<10?"0"+s:s;let r=t.getDate();return r=r<10?"0"+r:r,a+"-"+s+"-"+r},setStorage(e,t){localStorage[e]=JSON.stringify(t||{})},getStorage(e){var t=localStorage[e];if(t)try{t=JSON.parse(t)}catch(a){console.log("localStorage取失败："+a),t=!1}else t=!1;return t},linkClientDetail(e,t){var a=JSON.parse(JSON.stringify(e));for(var s in a)Array.isArray(a[s])&&(a[s]=a[s].join(","));let r=t.$router.resolve({path:"/voice_of_customer/self_help_analysis/detail_enquiry/client_detail",query:{...a,typeRoute:"client_detail"}});window.open(r.href,"_blank")},doSubmit(e,t){let a=!0;return t.$refs[e].validate((e=>{a=!!e})),a||t.$message({showClose:!0,message:"请检查必填项是否为空以及输入是否正确！",type:"warning"}),a}}},9956:function(e,t,a){var s={"./competitive_products_analysis.svg":2920,"./data_closed_loop.svg":8756,"./fifth.svg":4838,"./first.svg":9625,"./fourth.svg":439,"./integrated_management.svg":3502,"./mine_product_analysis.svg":2908,"./report.svg":4087,"./second.svg":286,"./self_help_analysis.svg":8511,"./third.svg":559,"./voc_board.svg":1875};function r(e){var t=i(e);return a(t)}function i(e){if(!a.o(s,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return s[e]}r.keys=function(){return Object.keys(s)},r.resolve=i,e.exports=r,r.id=9956},16:function(e,t,a){"use strict";e.exports=a.p+"img/back.8441bd15.svg"},9455:function(e,t,a){"use strict";e.exports=a.p+"img/download.28853881.svg"},8795:function(e,t,a){"use strict";e.exports=a.p+"img/download_disabled.abf64fd7.svg"},658:function(e,t,a){"use strict";e.exports=a.p+"img/finish.7238a66b.svg"},2920:function(e,t,a){"use strict";e.exports=a.p+"img/competitive_products_analysis.480cae28.svg"},8756:function(e,t,a){"use strict";e.exports=a.p+"img/data_closed_loop.2f506254.svg"},4838:function(e,t,a){"use strict";e.exports=a.p+"img/fifth.8cad264f.svg"},9625:function(e,t,a){"use strict";e.exports=a.p+"img/first.9565c2e7.svg"},439:function(e,t,a){"use strict";e.exports=a.p+"img/fourth.ade50d13.svg"},3502:function(e,t,a){"use strict";e.exports=a.p+"img/integrated_management.2f2adf3c.svg"},2908:function(e,t,a){"use strict";e.exports=a.p+"img/mine_product_analysis.4395a7a5.svg"},4087:function(e,t,a){"use strict";e.exports=a.p+"img/report.d7ea964a.svg"},286:function(e,t,a){"use strict";e.exports=a.p+"img/second.b41f3d3e.svg"},8511:function(e,t,a){"use strict";e.exports=a.p+"img/self_help_analysis.cf7288c7.svg"},559:function(e,t,a){"use strict";e.exports=a.p+"img/third.49dbf848.svg"},1875:function(e,t,a){"use strict";e.exports=a.p+"img/voc_board.8b2d5858.svg"},1699:function(e,t,a){"use strict";e.exports=a.p+"img/hide.f098da37.svg"},4342:function(e,t,a){"use strict";e.exports=a.p+"img/open.03e877a0.svg"},693:function(e,t,a){"use strict";e.exports=a.p+"img/text-detail-empty.653d2319.svg"},8216:function(e,t,a){"use strict";e.exports=a.p+"img/noDataImg.91b995a1.png"},2285:function(e,t,a){"use strict";e.exports=a.p+"img/ren_qun_qun_ti.98fff211.png"},6949:function(e){"use strict";e.exports="data:image/png;base64,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"},5959:function(e){"use strict";e.exports=ELEMENT},311:function(e){"use strict";e.exports=Vue}},t={};function a(s){var r=t[s];if(void 0!==r)return r.exports;var i=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(i.exports,i,i.exports,a),i.loaded=!0,i.exports}a.m=e,function(){var e=[];a.O=function(t,s,r,i){if(!s){var n=1/0;for(c=0;c<e.length;c++){s=e[c][0],r=e[c][1],i=e[c][2];for(var l=!0,o=0;o<s.length;o++)(!1&i||n>=i)&&Object.keys(a.O).every((function(e){return a.O[e](s[o])}))?s.splice(o--,1):(l=!1,i<n&&(n=i));if(l){e.splice(c--,1);var d=r();void 0!==d&&(t=d)}}return t}i=i||0;for(var c=e.length;c>0&&e[c-1][2]>i;c--)e[c]=e[c-1];e[c]=[s,r,i]}}(),function(){a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,{a:t}),t}}(),function(){a.d=function(e,t){for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,s){return a.f[s](e,t),t}),[]))}}(),function(){a.u=function(e){return"js/"+e+"."+{563:"feab6ac5",839:"1aa42d44"}[e]+".js"}}(),function(){a.miniCssF=function(e){return"css/"+e+"."+{563:"48ae3a88",839:"c6ff4edb"}[e]+".css"}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="changan_voc_code:";a.l=function(s,r,i,n){if(e[s])e[s].push(r);else{var l,o;if(void 0!==i)for(var d=document.getElementsByTagName("script"),c=0;c<d.length;c++){var h=d[c];if(h.getAttribute("src")==s||h.getAttribute("data-webpack")==t+i){l=h;break}}l||(o=!0,l=document.createElement("script"),l.charset="utf-8",l.timeout=120,a.nc&&l.setAttribute("nonce",a.nc),l.setAttribute("data-webpack",t+i),l.src=s),e[s]=[r];var m=function(t,a){l.onerror=l.onload=null,clearTimeout(u);var r=e[s];if(delete e[s],l.parentNode&&l.parentNode.removeChild(l),r&&r.forEach((function(e){return e(a)})),t)return t(a)},u=setTimeout(m.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=m.bind(null,l.onerror),l.onload=m.bind(null,l.onload),o&&document.head.appendChild(l)}}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){a.p=""}(),function(){var e=function(e,t,a,s){var r=document.createElement("link");r.rel="stylesheet",r.type="text/css";var i=function(i){if(r.onerror=r.onload=null,"load"===i.type)a();else{var n=i&&("load"===i.type?"missing":i.type),l=i&&i.target&&i.target.href||t,o=new Error("Loading CSS chunk "+e+" failed.\n("+l+")");o.code="CSS_CHUNK_LOAD_FAILED",o.type=n,o.request=l,r.parentNode.removeChild(r),s(o)}};return r.onerror=r.onload=i,r.href=t,document.head.appendChild(r),r},t=function(e,t){for(var a=document.getElementsByTagName("link"),s=0;s<a.length;s++){var r=a[s],i=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(i===e||i===t))return r}var n=document.getElementsByTagName("style");for(s=0;s<n.length;s++){r=n[s],i=r.getAttribute("data-href");if(i===e||i===t)return r}},s=function(s){return new Promise((function(r,i){var n=a.miniCssF(s),l=a.p+n;if(t(n,l))return r();e(s,l,r,i)}))},r={143:0};a.f.miniCss=function(e,t){var a={563:1,839:1};r[e]?t.push(r[e]):0!==r[e]&&a[e]&&t.push(r[e]=s(e).then((function(){r[e]=0}),(function(t){throw delete r[e],t})))}}(),function(){var e={143:0};a.f.j=function(t,s){var r=a.o(e,t)?e[t]:void 0;if(0!==r)if(r)s.push(r[2]);else{var i=new Promise((function(a,s){r=e[t]=[a,s]}));s.push(r[2]=i);var n=a.p+a.u(t),l=new Error,o=function(s){if(a.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var i=s&&("load"===s.type?"missing":s.type),n=s&&s.target&&s.target.src;l.message="Loading chunk "+t+" failed.\n("+i+": "+n+")",l.name="ChunkLoadError",l.type=i,l.request=n,r[1](l)}};a.l(n,o,"chunk-"+t,t)}},a.O.j=function(t){return 0===e[t]};var t=function(t,s){var r,i,n=s[0],l=s[1],o=s[2],d=0;if(n.some((function(t){return 0!==e[t]}))){for(r in l)a.o(l,r)&&(a.m[r]=l[r]);if(o)var c=o(a)}for(t&&t(s);d<n.length;d++)i=n[d],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(c)},s=self["webpackChunkchangan_voc_code"]=self["webpackChunkchangan_voc_code"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=a.O(void 0,[998],(function(){return a(8116)}));s=a.O(s)})();