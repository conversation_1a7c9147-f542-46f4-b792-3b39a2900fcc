<!DOCTYPE html>
<html lang="en">
<link rel="stylesheet" href="//apps.bdimg.com/libs/jqueryui/1.10.4/css/jquery-ui.min.css">
<script
        src="https://code.jquery.com/jquery-3.6.0.min.js"
        integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="
        crossorigin="anonymous"></script>
<script src="//code.jquery.com/ui/1.10.4/jquery-ui.js"></script>
<link rel="stylesheet" href="jqueryui/style.css">
<link rel="stylesheet" href="//unpkg.com/layui@2.6.8/dist/css/layui.css">
<script src="//unpkg.com/layui@2.6.8/dist/layui.js"></script>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键刷数</title>
</head>
<body>

<h1 style="padding-left: 20px;">一键刷数</h1>
<div style="width: 50%;float: left;padding-left: 20px;box-sizing: border-box; ">
    <h4>ES:</h4>
    <div>
        当前es查询别名，指向索引版本号： <span id="currQuery"></span>
    </div>

    <div>
        当前es写入别名，指向索引版本号： <span id="currWrite"></span>
    </div>

    <div>
        <input id="createIndex"/>
        <button id="submitCreateIndex">新建索引</button>
    </div>

    <div>
        <input id="switchQueryIndex"/>
        <button id="submitSwitchQuery">切换查询别名指向</button>
    </div>
    <div>
        <input id="switchWriteIndex"/>
        <button id="submitSwitchWrite">切换写入别名指向</button>
    </div>
    <h4>调度任务：</h4>
    <div>
        <div>
            <span>账号名：</span>
            <input id="username"/>
        </div>
        <div>
            <span>密码：</span>
            <input id="password" type="password"/>
        </div>
        <div>
            <button onclick="login()">登录</button>
        </div>
    </div>
    <h5></h5>
    <div>
        <textarea style="width: 300px" id="taskName" placeholder='请输入任务信息，如果是多个请用 "," 分割' rows="5" cols="8"></textarea><br/>
        <label for="from">开始日期：</label>
        <input type="text" id="from" name="from"><br/>
        <label for="to">结束日期：</label>
        <input type="text" id="to" name="to"><br/>
        <button id="submitJobParams">设置任务参数</button>
    </div>
    <h5></h5>
    <div>
        <textarea style="width: 300px" id="groupId" placeholder='请输入任务组信息，如果是多个请用 "," 分割' rows="5" cols="8"></textarea>
        <br>
        <button id="submitJobStart">启动任务组</button>
    </div>

</div>

<div style="width: 50%;float: left">
    <h4>已存在ES索引</h4>
    <div id="esIndex"></div>
</div>

</body>
<script type="text/javascript">
    var Base64 = {
        _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", encode: function (e) {
            var t = "";
            var n, r, i, s, o, u, a;
            var f = 0;
            e = Base64._utf8_encode(e);
            while (f < e.length) {
                n = e.charCodeAt(f++);
                r = e.charCodeAt(f++);
                i = e.charCodeAt(f++);
                s = n >> 2;
                o = (n & 3) << 4 | r >> 4;
                u = (r & 15) << 2 | i >> 6;
                a = i & 63;
                if (isNaN(r)) {
                    u = a = 64
                } else if (isNaN(i)) {
                    a = 64
                }
                t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
            }
            return t
        }, decode: function (e) {
            var t = "";
            var n, r, i;
            var s, o, u, a;
            var f = 0;
            e = e.replace(/[^A-Za-z0-9\+\/\=]/g, "");
            while (f < e.length) {
                s = this._keyStr.indexOf(e.charAt(f++));
                o = this._keyStr.indexOf(e.charAt(f++));
                u = this._keyStr.indexOf(e.charAt(f++));
                a = this._keyStr.indexOf(e.charAt(f++));
                n = s << 2 | o >> 4;
                r = (o & 15) << 4 | u >> 2;
                i = (u & 3) << 6 | a;
                t = t + String.fromCharCode(n);
                if (u != 64) {
                    t = t + String.fromCharCode(r)
                }
                if (a != 64) {
                    t = t + String.fromCharCode(i)
                }
            }
            t = Base64._utf8_decode(t);
            return t
        }, _utf8_encode: function (e) {
            e = e.replace(/\r\n/g, "\n");
            var t = "";
            for (var n = 0; n < e.length; n++) {
                var r = e.charCodeAt(n);
                if (r < 128) {
                    t += String.fromCharCode(r)
                } else if (r > 127 && r < 2048) {
                    t += String.fromCharCode(r >> 6 | 192);
                    t += String.fromCharCode(r & 63 | 128)
                } else {
                    t += String.fromCharCode(r >> 12 | 224);
                    t += String.fromCharCode(r >> 6 & 63 | 128);
                    t += String.fromCharCode(r & 63 | 128)
                }
            }
            return t
        }, _utf8_decode: function (e) {
            var t = "";
            var n = 0;
            var r = c1 = c2 = 0;
            while (n < e.length) {
                r = e.charCodeAt(n);
                if (r < 128) {
                    t += String.fromCharCode(r);
                    n++
                } else if (r > 191 && r < 224) {
                    c2 = e.charCodeAt(n + 1);
                    t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                    n += 2
                } else {
                    c2 = e.charCodeAt(n + 1);
                    c3 = e.charCodeAt(n + 2);
                    t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                    n += 3
                }
            }
            return t
        }
    }
    var queryEsAlertCount = 0;
    var writeEsAlertCount = 0;
    var intervalId;
    var layer;
    var loadingIndex;
    var tree;
    layui.use('layer', function () {
        layer = layui.layer;
    });

    layui.use('tree', function(){
        tree = layui.tree;

        //渲染
        var esIndex = tree.render({
            elem: '#esIndex',
            data: [],
            id: 'esIndexId'
        });
    });
    $(function () {
        getCurrentData(); // 第一次加载数据
        getEsIndex(); // 第一次加载数据
        // 开启定时任务，时间间隔为3000 ms。
        setInterval(function () {
            getCurrentData();
        }, 60000);

        setInterval(function () {
            getEsIndex();
        }, 30000);

        $("#from").datepicker({
            dateFormat: 'yy-mm-dd',
            defaultDate: "-1w",
            changeMonth: true,
            numberOfMonths: 2,
            onClose: function (selectedDate) {
                $("#to").datepicker("option", "minDate", selectedDate);
            }
        });
        $("#to").datepicker({
            dateFormat: 'yy-mm-dd',
            defaultDate: "-1d",
            changeMonth: true,
            numberOfMonths: 2,
            onClose: function (selectedDate) {
                $("#from").datepicker("option", "maxDate", selectedDate);
            }
        });
    })

    function getCurrentData() {
        $.ajax({
            url: '/api/ops/brush-count/get-current-query-index',
            type: 'GET',
            cache: false,
            processData: false,
            contentType: false,
            beforeSend: function (XMLHttpRequest) {
            },
            success: function (data, textStatus) {
                if (data.code !== 1) {
                    if (queryEsAlertCount < 3) {
                        alert("获取当前es查询别名指向索引版本号失败，错误信息为：" + data.msg);
                        queryEsAlertCount++;
                    }
                } else {
                    $("#currQuery").text(data.data);
                    queryEsAlertCount = 0;
                }
            },
            complete: function (XMLHttpRequest, textStatus) {

            },
            error: function (XMLHttpRequest, textStatus,
                             errorThrown) {
                alert("获取当前es查询别名指向索引版本号失败，错误信息为：" + textStatus)
            }
        })

        $.ajax({
            url: '/api/ops/brush-count/get-current-write-index',
            type: 'GET',
            cache: false,
            processData: false,
            contentType: false,
            beforeSend: function (XMLHttpRequest) {
            },
            success: function (data, textStatus) {
                if (data.code !== 1) {
                    if (writeEsAlertCount < 3) {
                        alert("获取当前es写入别名索引版本号失败，错误信息为：" + data.msg);
                        writeEsAlertCount++;
                    }
                } else {
                    $('#currWrite').text(data.data);
                    writeEsAlertCount = 0;
                }
            },
            complete: function (XMLHttpRequest, textStatus) {

            },
            error: function (XMLHttpRequest, textStatus,
                             errorThrown) {
                alert("获取当前es写入别名索引版本号失败，错误信息为：" + textStatus)
            }
        })
    }

    $("#submitCreateIndex")
        .on(
            "click",
            function () {
                $
                    .ajax({
                        url: '/api/ops/brush-count/create-index?indexName=' + $('#createIndex').val(),
                        type: 'GET',
                        cache: false,
                        processData: false,
                        contentType: false,
                        beforeSend: function (XMLHttpRequest) {

                        },
                        success: function (data, textStatus) {
                            if (data.code !== 1) {
                                alert("创建版本号为" + $('#createIndex').val() + "的es索引失败，错误信息为：" + data.msg)
                            } else {
                                intervalId = setInterval(function () {
                                    getCreateIndexStatus($('#createIndex').val(), data.data)
                                }, 3000);
                                loadingIndex = layer.load(1, {
                                    shade: [0.5, 'lightgrey'] //0.1透明度的白色背景
                                });
                            }
                        },
                        complete: function (XMLHttpRequest, textStatus) {

                        },
                        error: function (XMLHttpRequest, textStatus,
                                         errorThrown) {
                            alert("创建版本号为" + $('#createIndex').val() + "的es索引失败，错误信息为：" + textStatus)
                        }
                    });
            });


    $("#submitSwitchQuery")
        .on(
            "click",
            function () {
                $
                    .ajax({
                        url: "/api/ops/brush-count/switch-query-index?indexName=" + $('#switchQueryIndex').val(),
                        type: 'GET',
                        cache: false,
                        processData: false,
                        contentType: false,
                        beforeSend: function (XMLHttpRequest) {
                        },
                        success: function (data, textStatus) {
                            if (data.code !== 1) {
                                alert("切换es查询别名索引为版本号： " + $('#switchQueryIndex').val() + " 失败，错误信息为：" + data.msg)
                            } else {
                                if (data.data === true) {
                                    alert("切换es查询别名索引为版本号：" + $('#switchQueryIndex').val() + " 成功")
                                } else {
                                    alert("切换es查询别名索引为版本号：" + $('#switchQueryIndex').val() + " 失败")
                                }
                            }
                        },
                        complete: function (XMLHttpRequest, textStatus) {

                        },
                        error: function (XMLHttpRequest, textStatus,
                                         errorThrown) {
                            alert("切换es查询别名索引为版本号：" + $('#switchQueryIndex').val() + " 失败, 错误信息为：" + textStatus)
                        }
                    });
            });

    $("#submitSwitchWrite")
        .on(
            "click",
            function () {
                $
                    .ajax({
                        url: "/api/ops/brush-count/switch-write-index?indexName=" + $('#switchWriteIndex').val(),
                        type: 'GET',
                        cache: false,
                        processData: false,
                        contentType: false,
                        beforeSend: function (XMLHttpRequest) {
                        },
                        success: function (data, textStatus) {
                            if (data.code !== 1) {
                                alert("切换es写入别名索引为版本号： " + $('#switchWriteIndex').val() + " 失败，错误信息为：" + data.msg)
                            } else {
                                if (data.data === true) {
                                    alert("切换es写入别名索引为版本号：" + $('#switchWriteIndex').val() + " 成功")
                                } else {
                                    alert("切换es写入别名索引为版本号：" + $('#switchWriteIndex').val() + " 失败")
                                }
                            }
                        },
                        complete: function (XMLHttpRequest, textStatus) {
                        },
                        error: function (XMLHttpRequest, textStatus,
                                         errorThrown) {
                            alert("切换es写入别名索引为版本号：" + $('#switchWriteIndex').val() + " 失败, 错误信息为：" + textStatus)
                        }
                    });
            });

    $("#submitJobParams").on(
        "click",
        function () {
            $
                .ajax({
                    url: "/api/ops/brush-count/set-schedule-params",
                    type: 'POST',
                    cache: false,
                    processData: false,
                    data: JSON.stringify({
                        "taskName": $('#taskName').val(),
                        "startTime": $('#from').val(),
                        "endTime": $('#to').val(),
                    }),
                    contentType: "application/json;charset=utf-8",
                    beforeSend: function (XMLHttpRequest) {
                    },
                    success: function (data, textStatus) {
                        if (data.code !== 1) {
                            alert(data.msg)
                        } else {
                            if (data.data === true) {
                                alert("设置任务参数成功")
                            } else {
                                alert("设置任务参数失败")
                            }
                        }
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus,
                                     errorThrown) {
                        alert("设置任务参数失败：" + textStatus)
                    }
                })
        });

    $("#submitJobStart").on(
        "click",
        function () {
            $
                .ajax({
                    url: "/api/ops/brush-count/run-schedule-task",
                    type: 'POST',
                    cache: false,
                    processData: false,
                    data: JSON.stringify({
                        "groupId": $('#groupId').val()
                    }),
                    contentType: "application/json;charset=utf-8",
                    beforeSend: function (XMLHttpRequest) {
                    },
                    success: function (data, textStatus) {
                        if (data.code !== 1) {
                            alert(data.msg)
                        } else {
                            if (data.data === true) {
                                alert("启动任务成功")
                            } else {
                                alert("启动任务失败")
                            }
                        }
                    },
                    complete: function (XMLHttpRequest, textStatus) {

                    },
                    error: function (XMLHttpRequest, textStatus,
                                     errorThrown) {
                        alert("启动任务失败，错误信息为：" + textStatus)
                    }
                })
        });


    function login() {
        $
            .ajax({
                url: "/api/ops/brush-count/simulation-login",
                type: 'POST',
                cache: false,
                processData: false,
                data: JSON.stringify({
                    "userName": $('#username').val(),
                    "password": Base64.encode($('#password').val())
                }),
                contentType: "application/json;charset=utf-8",
                beforeSend: function (XMLHttpRequest) {
                },
                success: function (data, textStatus) {
                    if (data.code !== 1) {
                        alert(data.msg)
                    } else {
                        alert("登录成功")
                    }
                },
                complete: function (XMLHttpRequest, textStatus) {

                },
                error: function (XMLHttpRequest, textStatus,
                                 errorThrown) {
                    alert("登陆失败，错误信息为：" + textStatus)
                }
            })
    }


    function getCreateIndexStatus(indexName, jobName) {
        $
            .ajax({
                url: "/api/ops/brush-count/get-index-status?indexName=" + jobName,
                type: 'GET',
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function (XMLHttpRequest) {
                },
                success: function (data, textStatus) {
                    if (data.code !== 1) {
                        alert("获取索引 " + indexName + " 创建状态失败，错误信息为：" + data.msg);
                    } else {
                        if (data.data.code === "2") {
                            layer.close(loadingIndex);
                            clearInterval(intervalId);
                            alert("索引 " + indexName + " 创建成功");
                        } else if (data.data.code === "-1" || data.data.code === "-2") {
                            layer.close(loadingIndex);
                            clearInterval(intervalId);
                            alert("索引 " + indexName + " 创建失败， 错误信息为：" + data.data.message);
                        }
                    }
                },
                complete: function (XMLHttpRequest, textStatus) {

                },
                error: function (XMLHttpRequest, textStatus,
                                 errorThrown) {
                    layer.close(loadingIndex);
                    clearInterval(intervalId);
                    alert("获取索引 " + indexName + " 创建状态失败，错误信息为：" + textStatus)
                }
            })
    }

    function getEsIndex() {
        $
            .ajax({
                url: "/api/ops/brush-count/get-index-list-regular",
                type: 'GET',
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function (XMLHttpRequest) {
                },
                success: function (data, textStatus) {
                    if (data.code !== 1) {
                        layer.msg('获取失败索引列表失败，错误信息为：' + data.msg);
                    } else {
                        var dataList = data.data;
                        var renderData = [];
                        for (var i = 0; i < dataList.length; i++) {
                            renderChildren = [];
                            for (var j = 0; j < dataList[i].value.length; j++) {
                                renderChildren.push({
                                    "title": dataList[i].value[j],
                                })
                            }
                            renderData.push({
                                "title": dataList[i].key,
                                "children": renderChildren
                            })
                        }

                        tree.reload('esIndexId', {
                            data: renderData
                        });
                    }
                },
                complete: function (XMLHttpRequest, textStatus) {

                },
                error: function (XMLHttpRequest, textStatus,
                                 errorThrown) {
                    alert("登陆失败，错误信息为：" + textStatus)
                }
            })
    }
</script>
</html>
