<!DOCTYPE html>
<html lang="zh-CN" xml:lang="en">
  <head>
    <meta charset="utf-8" />
    <title>观星台模拟登陆</title>
    <script
      src="./jquery.min.js"
      type="text/javascript"
    ></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        font-family: '微软雅黑';
      }
      body {
        background-color: #13314a;
      }
      ul,
      li {
        list-style: none;
      }
      .login-tab {
        position: relative;
        z-index: 5;
        padding: 0 10px;
        background: #e9ebed;
        font-size: 14px;
      }
      .bi-login {
        position: absolute;
        top: 50%;
        margin-top: -154px;
        right: 100px;
        background: #e9ebed;
        color: #3c3c3c;
      }
      .login-section {
        padding: 25px 25px 35px;
      }
      .login-title {
        font-size: 18px;
        text-align: center;
      }
      .login-note {
        position: relative;
        text-align: center;
        margin-top: 10px;
      }
      .login-sign {
        position: relative;
        z-index: 1;
        padding: 0 10px;
        background: #e9ebed;
        font-size: 14px;
      }
      .login-hr {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 1px;
        background: #a3a5a6;
      }
      .login-group {
        position: relative;
      }
      .group-input {
        height: 40px;
        font-size: 14px;
        width: 280px;
        border: 0px;
        padding: 0 10px;
      }
      #companyUrlValue {
        cursor: pointer;
      }
      .group-mar-1 {
        margin-top: 15px;
      }
      .group-mar-2 {
        margin-top: 10px;
      }
      .login-btn {
        display: block;
        border: 0px;
        margin: 20px auto 0;
        background: #35b2ea;
        color: #fff;
        padding: 10px 0;
        font-size: 18px;
        cursor: pointer;
        text-align: center;
        width: 300px;
        vertical-align: middle;
        text-decoration: none;
      }
      .companyList {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        height: 100%;
        overflow: hidden scroll;
        background: #fff;
      }
      .companyList ul {
        height: 244px;
        overflow-y: auto;
        padding: 20px 25px;
      }
      .companyList li {
        margin-top: 10px;
        cursor: pointer;
      }
      .companyList li:first-child {
        margin-top: 0;
      }
      .companyList li:hover,
      .companyList li.hover {
        color: #35b2ea;
      }
      .bi-footer {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 82px;
        width: 100%;
        background: #fff;
        line-height: 82px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="bi-login">
      <form action="/login" method="post" id="loginform">
        <div class="login-section">
          <p class="login-title">模拟登录</p>
          <div class="login-note">
            <span class="login-sign">SIGN IN</span>
            <div class="login-hr"></div>
          </div>
          <div class="login-group group-mar-1" id="companyUrlValue">
            <div class="group-line"></div>
            <label class="hidden-label" for="companyUrl">公司URL:</label>
            <input
              type="text"
              readonly
              value="demo"
              id="company"
              class="group-input"
            />
            <input type="hidden" value="1" id="companyId" name="companyId" />
          </div>
          <div class="login-group group-mar-2">
            <div class="group-line"></div>
            <label class="hidden-label" for="username">模拟账号:</label>
            <input type="text" id="username" class="group-input" name="username"
            /><br/>
          </div>
          <div class="login-group group-mar-2">
            <label class="hidden-label" for="username">账号密码:</label>
            <input type="password" id="password" class="group-input" name="password"
            /><br />
            <font id="errMsg" color="red" style="display: none;size: 6">密码错误</font>
          </div>
          <a href="javascript:;" onclick="setUser();" class="login-btn">登录</a>
        </div>
      </form>
      <div id="companyList" class="companyList" style="display: none">
        <ul>
          <li value="1" class="hover">demo</li>
        </ul>
      </div>
    </div>
    <div class="bi-footer" id="bi-footer">
      <ul class="footer-list">
        <li class="inlineMid" id="footer-copyright"></li>
      </ul>
    </div>
    <script type="text/javascript">
      var username = document.getElementById('username');
      var company = document.getElementById('company');
      var companyId = document.getElementById('companyId');
      var companyList = document.getElementById('companyList');
      var currentYear = new Date().getFullYear();
      // var copyRightText = 'COPYRIGHT (©) 2016-'+currentYear+' 深圳美云智数科技有限公司';
      var copyRightText = '美云智数';
      var copyRightTextNode = document.createTextNode(copyRightText);
      document
        .getElementById('footer-copyright')
        .appendChild(copyRightTextNode);

      companyUrlValue.onclick = function () {
        companyList.style.display = 'block';
      };
      //getCompanyList();
      companyList.onclick = function (e) {
        var e = window.event || e;
        e.stopPropagation();
        if (e.target.nodeName == 'LI') {
          for (
            var i = 0, len = companyList.getElementsByTagName('li').length;
            i < len;
            i++
          )
            companyList.getElementsByTagName('li')[i].className = '';
          e.target.className = 'hover';
          company.value = e.target.innerText;
          companyId.value = e.target.getAttribute('value');
          companyList.style.display = 'none';
        }
      };

      document.onkeydown = function (e) {
        var e = window.event || e;
        if (e.keyCode == 13 && companyList.style.display == 'none') {
          setUser();
        }
      };

      function setUser() {
   	  	$.ajax({
          url: '/verPassword',
          type: 'POST',
          dataType: 'json',
          data: {'password':$('#password').val()},
          async: true,
          success: function (res) {
            let data = res.code;
            if (data == 1) {
            	document.getElementById('loginform').submit();
        	    return false;
            }else{
            	document.getElementById('errMsg').style.display = 'block';
            }
          },
          error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest);
            return false;
          }
        });
        // document.cookie = "username=" + username.value;
        // document.cookie = "companyUrl=" + companyUrl.value;
        // document.location.href = "/";
      }
      function verPassword() {
          $.ajax({
            url: '/verPassword',
            type: 'POST',
            dataType: 'json',
            data: {password:document.getElementById('password')},
            async: true,
            success: function (res) {
              let data = res.data;
              alert(data);
              if (data.length) {
                return true;
              }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
              console.log(XMLHttpRequest);
              return false;
            }
          });
      }
      function getCompanyList() {
        $.ajax({
          url: 'http://localhost:8686/company/listAll',
          type: 'POST',
          dataType: 'json',
          async: true,
          success: function (res) {
            let data = res.data.record || [];
            if (data.length) {
              let listHtml = '';
              data.forEach((element) => {
                if (element.id === 5) {
                  company.value =
                    element.companyName + '(' + element.authUrl + ')';
                  companyId.value = element.id;
                  listHtml += `<li class="hover" value="${element.id}">${element.companyName}(${element.authUrl})</li>`;
                } else {
                  listHtml += `<li value="${element.id}">${element.companyName}(${element.authUrl})</li>`;
                }
              });
              $('#companyList').html(listHtml);
            }
          },
          error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest);
          }
        });
      }
      
    </script>
  </body>
</html>
