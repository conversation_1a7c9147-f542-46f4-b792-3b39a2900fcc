#\u9ed8\u8ba4\u6570\u636e\u6e90
spring.datasource.dynamic.primary=cas
#\u6570\u636e\u6e90
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*************************************************************************************************************************
spring.datasource.username=db_bdu_vocu_web
spring.datasource.password=t5tvorGyvXllvBEo

#cas\u6570\u636e\u6e90
spring.datasource.dynamic.datasource.cas.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.cas.url=*************************************************************************************************************************
spring.datasource.dynamic.datasource.cas.username=db_bdu_vocu_web
spring.datasource.dynamic.datasource.cas.password=t5tvorGyvXllvBEo

#?????
spring.datasource.dynamic.datasource.nlp.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.nlp.url=*************************************************************************************************************************
spring.datasource.dynamic.datasource.nlp.username=db_bdu_vocu_nlp
spring.datasource.dynamic.datasource.nlp.password=u0gO6GA6JjbZRy80

spring.datasource.dynamic.datasource.xmyBase.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.xmyBase.url=******************************************************************************************************************************
spring.datasource.dynamic.datasource.xmyBase.username=twx_base
spring.datasource.dynamic.datasource.xmyBase.password=m6K2ihTIIKFw

#redis session
spring.session.store-type=redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.database=4
spring.redis.password=wTmGsEglR1gk

#\u5fae\u4fe1\u63a8\u9001\u914d\u7f6e
weixin.corpid=wx2b418a3d21bf8228
weixin.corpsecret=qBAREm6m4QYf996Ue1DDW6v5hfBpH7OpvRX82Gd-Xgg
weixin.agentid=1000302
#
weixin.accessToken.url=http://************:10005/cgi-bin/gettoken?corpid=%s&corpsecret=%s
weixin.send.message.url=http://************:10005/cgi-bin/message/send?access_token=%s
weixin.subscribe.keyword=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\r\n\u63d0\u53ca\u91cfTOP3\u95ee\u9898\u4e3a\uff1a{top3Mention}\r\n\u63d0\u53ca\u91cf\u73af\u6bd4\u53d8\u5316TOP3\u4e3a\uff1a{top3Cycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01"
weixin.subscribe.person.experience=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\uff0c\u4f53\u9a8c\u503c\uff1a{experience}\uff0c\u4f53\u9a8c\u503c\u73af\u6bd4\uff1a{experienceCycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01
weixin.subscribe.person.mentionRate=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\uff0c\u8d1f\u9762\u63d0\u53ca\u7387\uff1a{experience}\uff0c\u8d1f\u9762\u63d0\u53ca\u7387\u73af\u6bd4\uff1a{experienceCycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01

#thymeleaf
spring.thymeleaf.cache=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.prefix=classpath:page/
spring.thymeleaf.encoding=UTF-8
spring.resources.static-locations=classpath:page/,file:/u01/vocu/image/
spring.boot.admin.notify.mail.enabled=false

#\u65e5\u5fd7\u7ea7\u522b
logging.level.com.meicloud=DEBUG

#\u5355\u70b9\u767b\u5f55\u9000\u51faURL
cas.logout.url=https://caddm.changan.com.cn/cas/logout?service=
cas.server.url.prefix=https://caddm.changan.com.cn/cas
#\u7528\u6237\u767d\u540d\u5355
cas.user.names=
cas.user.unauthorized_url=unauthorized.html
#\u4e0d\u62e6\u622a\u8d44\u6e90
cas.ignore_pattern=/es/overrideDWD/before|/es/overrideDM/before|/es/overrideDM/after|/unauthorized.html|.*.js|.*.jsp|.*.ttf|.*.png|.*.ico|/css/*|/fonts/*|/img/*|/js/*|/security/*

#es \u8fde\u63a5
es.v7.host=***********|***********|***********|***********
es.v7.port=60002
es.v7.protocol=http
es.v7.username=bdu
es.v7.password=RnMzdYo34eDbD5ee
es.v7.index.keyword.mention.day=bdu-vocu-keyword-mention-day-query
es.v7.index.keyword.mention.day.write=bdu-vocu-keyword-mention-day
es.v7.index.keyword.mention.month=bdu-vocu-keyword-mention-month-query
es.v7.index.keyword.mention.month.write=bdu-vocu-keyword-mention-month
es.v7.index.keyword.mention.year=bdu-vocu-keyword-mention-year-query
es.v7.index.keyword.mention.year.write=bdu-vocu-keyword-mention-year
es.v7.index.keyword.model.mention.day=bdu-vocu-keyword-model-mention-day-query
es.v7.index.keyword.model.mention.day.write=bdu-vocu-keyword-model-mention-day
es.v7.index.dm.region.keyword.mention.year=bdu-vocu-keyword-mention-region-year-query
es.v7.index.dm.region.keyword.mention.year.write=bdu-vocu-keyword-mention-region-year
es.v7.index.dm.region.keyword.mention.month=bdu-vocu-keyword-mention-region-month-query
es.v7.index.dm.region.keyword.mention.month.write=bdu-vocu-keyword-mention-region-month
es.v7.index.dm.region.keyword.mention.day=bdu-vocu-keyword-mention-region-day-query
es.v7.index.dm.region.keyword.mention.day.write=bdu-vocu-keyword-mention-region-day
es.v7.index.dm.brand.keyword.mention.day=bdu-vocu-brand-keyword-mention-day-query
es.v7.index.dm.brand.keyword.mention.day.write=bdu-vocu-brand-keyword-mention-day
es.v7.index.dm.brand.mention.day=bdu-vocu-brand-mention-day-query
es.v7.index.dm.brand.mention.day.write=bdu-vocu-brand-mention-day
es.v7.index.dm.brand.mention.month=bdu-vocu-brand-mention-month-query
es.v7.index.dm.brand.mention.month.write=bdu-vocu-brand-mention-month
es.v7.index.dm.brand.mention.year=bdu-vocu-brand-mention-year-query
es.v7.index.dm.brand.mention.year.write=bdu-vocu-brand-mention-year
#\u4eba\u7fa4\u7279\u5f81
es.v7.index.dm.customer.person.dtl=bdu-vocu-customer-persona-dtl-query
es.v7.index.dm.customer.person.dtl.write=bdu-vocu-customer-persona-dtl
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u65e5\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.day=bdu-vocu-keyword-mention-total-day-query
es.v7.index.dm.keyword.mention.total.day.write=bdu-vocu-keyword-mention-total-day
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.month=bdu-vocu-keyword-mention-total-month-query
es.v7.index.dm.keyword.mention.total.month.write=bdu-vocu-keyword-mention-total-month
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.year=bdu-vocu-keyword-mention-total-year-query
es.v7.index.dm.keyword.mention.total.year.write=bdu-vocu-keyword-mention-total-year
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.work.order.dtl=bdu-vocu-work-order-query
es.v7.index.dwd.evt.work.order.dtl.write=bdu-vocu-work-order
# es\ufffd\ufffd\u046f\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.consult.dtl=bdu-vocu-consult-query
es.v7.index.dwd.evt.consult.dtl.write=bdu-vocu-consult
# es\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.opinion.dtl=bdu-vocu-opinion-query
es.v7.index.dwd.evt.opinion.dtl.write=bdu-vocu-opinion
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.posts.comment.dtl=bdu-vocu-posts-comment-query
es.v7.index.dwd.evt.posts.comment.dtl.write=bdu-vocu-posts-comment
# es\ufffd\u02be\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.questionnaire.dtl=bdu-vocu-questionnaire-query
es.v7.index.dwd.evt.questionnaire.dtl.write=bdu-vocu-questionnaire
# es\ufffd\ufffd\u03f8\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd
es.v7.index.dwd.evt.dtl=${es.v7.index.dwd.evt.work.order.dtl},${es.v7.index.dwd.evt.consult.dtl},\
  ${es.v7.index.dwd.evt.opinion.dtl},${es.v7.index.dwd.evt.posts.comment.dtl},${es.v7.index.dwd.evt.questionnaire.dtl}
es.v7.index.prefix=bdu-vocu-
es.v7.highlight.fragment_size=200
es.v7.highlight.number_of_fragments=5

# KTM
changan.ktm.url=cmp.changan.com
changan.login.url=cmp.changan.com
changan.login.accessToken=rescenter/rest/resRestApi/v2/oAuth2GetAccessToken
changan.login.auth2login=rescenter/rest/resRestApi/v2/oAuth2Login
changan.login.appId=f72eb2f8-b054-4332-bea9-7db8bf62eaac
changan.login.secret=98996d2c-307d-432d-aa9a-cc7709074e04
changan.login.loginId=000002
changan.ktm.task.get=ws/rest/task/v1/getTask?taskId=%s
changan.ktm.task.create=ws/rest/task/v1/createTask
changan.ktm.task.delete=ws/rest/task/v1/deleteTask
changan.ktm.domain.query=ws/rest/task/v1/getTaskListByDomain?domainId=%s&health=%d
changan.ktm.domain.id=f3ebec90-7145-4306-a8b1-3194f3945e84

# \u83b7\u53d6\u90e8\u95e8\u6811
changan.departmentTree.uri=/rescenter/rest/resRestApi/v2/getDepartmentTree
# \u83b7\u53d6\u7528\u6237\u5217\u8868
changan.userList.uri=/rescenter/rest/resRestApi/v2/getUserListBySearch
# \u83b7\u53d6\u7528\u6237\u5217\u8868(\u6240\u6709)
changan.allUserInfo.uri=/rescenter/rest/resRestApi/v2/getAllUserInfo
# \u5f97\u5230\u5f53\u524d\u90e8\u95e8\u4e0b\u7684\u6240\u6709\u7528\u6237
changan.departmentUsers.uri=/rescenter/rest/resRestApi/v2/getUserListInDept
# \u6839\u636e\u7528\u6237\u767b\u5f55\u540d\u83b7\u53d6\u7528\u6237\u4fe1\u606f
changan.userInfoById.uri=/rescenter/rest/resRestApi/v2/getUserByLoginId
# \u6839\u636e\u7528\u6237\u767b\u5f55\u540d\u83b7\u53d6\u7528\u6237\u4fe1\u606f
changan.userInfoByToken.uri=/rescenter/rest/resRestApi/v2/getUserByToken
#\u767b\u5f55token\u8ba4\u8bc1
changan.checkIdentityToken.uri=/rescenter/rest/resRestApi/v2/checkIdentityToken
changan.getUserByToken.uri=/rescenter/rest/resRestApi/v2/getUserByToken
# \u957f\u5b89\u96c6\u56e2\u7684\u516c\u53f8id
changan.default.companyId.id=8f4ddf6d-507a-4835-8db5-84e1480823f0
# \u83b7\u53d6\u8fd1\u671f\u79bb\u804c\u7684\u4eba\u5458\u4fe1\u606f
changan.disable.user.uri=/rescenter/rest/resRestApi/v2/getDisableUserListByTime

file.upload.path=/u01/vocu/file/upload
file.tmp.path=/u01/vocu/file/tmp

aliyun.oss.file.endPoint=oss-cn-chongqing-ch-d01-a.ops.dip.cacloud.com
aliyun.oss.file.keyId=b4cHgkRd2TZ3OIui
aliyun.oss.file.keySecret=gqTpoiJegFrkkPDyGxUZ7UW91ErZlo
aliyun.oss.file.bucket=shuju-ubd-prod

##\u7b97\u6cd5URL
algorithm.analyzer.baseUrl=http://************:8110
algorithm.findKeyWord.url=/changan/voc/findKeyWord
algorithm.findKeyWord.batch.url=/changan/voc/findKeyWordBatch
algorithm.analyzer.url=/changan/voc/analyzer

# ?????????
algorithm.dataSourceScore.baseUrl=http://************:8100/changan/voc/weight?batchId=%d
#MSS URL
mss.url=http://************:18081

#\u5b9a\u65f6\u65e5\u671f\u4efb\u52a1\u914d\u7f6e\u6587\u4ef6
SubscribeServiceImpl.syncDayResult=0 0 10 * * ? 
SubscribeServiceImpl.syncWeekResult=0 0 10 * * ? 
SubscribeServiceImpl.syncMonthResult=0 0 10 * * ? 

KeywordCorpusServiceImpl.noResultContentDrill=0 0 0/3 * * ? 
KeywordCorpusServiceImpl.system.wordcnt=4

SyncTableServiceImpl.syncData=0 0 23 * * ? 
TaskServiceImpl.updateTaskDetailSchedule=0 */30 * * * ?

monitor.web.url=http://***********:8686,http://***********:8686
monitor.nlp.dataSources=\u901a\u7528\u7b97\u6cd5|\u6c7d\u8f66\u4e4b\u5bb6-\u7528\u6237\u53d1\u5e16,\u95ee\u7b54\u7c7b\u578b\u7b97\u6cd5|\u957f\u5b89\u767e\u79d1-\u95ee\u7b54,\u667a\u6167\u5c0f\u5b89\u7b97\u6cd5|\u8f66\u673a\u7aef-\u667a\u6167\u5c0f\u5b89,\u95ee\u5377-\u76f4\u8bc4\u7b97\u6cd5|\u957f\u5b89\u6c7d\u8f66\u76f4\u8bc4,\u95ee\u5377-GQRS\u7b97\u6cd5|GQRS,\u53e3\u7891\u8bc4\u5206\u7b97\u6cd5|\u6c7d\u8f66\u4e4b\u5bb6-\u53e3\u7891\u8bc4\u5206,\u8054\u7edc\u4e2d\u5fc3\u70ed\u7ebf\u670d\u52a1\u7b97\u6cd5|\u8054\u7edc\u4e2d\u5fc3\u70ed\u7ebf\u670d\u52a1,\u957f\u5b89\u5546\u57ce\u8bc4\u4ef7\u7b97\u6cd5|\u957f\u5b89\u5546\u57ce\u8bc4\u4ef7,\u4f53\u9a8c\u5b98-\u6d3b\u52a8\u7b97\u6cd5|\u4f53\u9a8c\u5b98-\u6d3b\u52a8,\u7ebf\u4e0b\u95ee\u5377\u5bfc\u5165\u7b97\u6cd5|\u7ebf\u4e0b\u95ee\u5377\u5bfc\u5165
monitor.nlp.java.url=http://************:8110,http://************:8110
monitor.nlp.java.status.url=/changan/voc/health-check
monitor.nlp.python.url=http://10.16.1.156:8100,http://10.16.1.157:8100

schedule.job.domain=10.64.23.15:18081
schedule.job.runTaskUrl=MSS/runJob/reRunGroup
schedule.job.queryParameter=MSS/job/queryParameter?rownum=10&pagenum=1&jobId=%s
schedule.job.insertAndupdateParameter=MSS/job/insertAndupdateParameter
schedule.job.mssHome=MSS/callback?client_name=CasClient
schedule.job.casServerUrl=https://caddm.changan.com.cn/cas/login
schedule.job.cookiePath=/MSS
schedule.job.userInfo=MSS/account/getUserInfo

data-source.config.esTypeMap[\u5de5\u5355]=${es.v7.index.dwd.evt.work.order.dtl}
data-source.config.esTypeMap[\u54a8\u8be2]=${es.v7.index.dwd.evt.consult.dtl}
data-source.config.esTypeMap[\u610f\u89c1\u53cd\u9988]=${es.v7.index.dwd.evt.opinion.dtl}
data-source.config.esTypeMap[\u5e16\u5b50\u8bc4\u4ef7]=${es.v7.index.dwd.evt.posts.comment.dtl}
data-source.config.esTypeMap[\u95ee\u5377]=${es.v7.index.dwd.evt.questionnaire.dtl}