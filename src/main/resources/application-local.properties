#\u9ed8\u8ba4\u6570\u636e\u6e90
spring.datasource.dynamic.primary=cas
#\u6570\u636e\u6e90
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=***************************************************************************************************************************************
spring.datasource.username=ENC(+W3e5cdiEMKpW8pOCsFcPefLjfpv8h2Y7Lfg/0jc03R7UDtP/iQT4ZGJ0jb2v/kx)
spring.datasource.password=ENC(/zeZwg3aGzzYhgfNQvqXZfd+dK3/ZWbjbVkjymDbNaBKSvZTlve/8f6wzRxkvqOA)

#cas\u6570\u636e\u6e90
spring.datasource.dynamic.datasource.cas.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.cas.url=****************************************************************************************************************************************
spring.datasource.dynamic.datasource.cas.username=program
spring.datasource.dynamic.datasource.cas.password=xtSE2DL4

#?????
spring.datasource.dynamic.datasource.nlp.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.nlp.url=********************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.nlp.username=ENC(+W3e5cdiEMKpW8pOCsFcPefLjfpv8h2Y7Lfg/0jc03R7UDtP/iQT4ZGJ0jb2v/kx)
spring.datasource.dynamic.datasource.nlp.password=ENC(/zeZwg3aGzzYhgfNQvqXZfd+dK3/ZWbjbVkjymDbNaBKSvZTlve/8f6wzRxkvqOA)

#redis session
spring.session.store-type=redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.database=6
spring.redis.password=gxt_redis_8uhb7ygv

#\u5fae\u4fe1\u63a8\u9001\u914d\u7f6e
weixin.corpid=wx5a67f0d3a5abcc49
weixin.corpsecret=_VeWzqjLjf5-ibKt9XA6AND0dBAPe73Su-HIVekWius
weixin.agentid=1
#
weixin.accessToken.url=https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s
weixin.send.message.url=https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s
weixin.subscribe.keyword=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\r\n\u63d0\u53ca\u91cfTOP3\u95ee\u9898\u4e3a\uff1a{top3Mention}\r\n\u63d0\u53ca\u91cf\u73af\u6bd4\u53d8\u5316TOP3\u4e3a\uff1a{top3Cycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01"
weixin.subscribe.person.experience=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\uff0c\u4f53\u9a8c\u503c\uff1a{experience}\uff0c\u4f53\u9a8c\u503c\u73af\u6bd4\uff1a{experienceCycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01
weixin.subscribe.person.mentionRate=\u3010VOC\u8ba2\u9605\u63a8\u9001\u3011\u60a8\u8ba2\u9605\u7684\u201c{title}\u201d\uff0c\u672c\u671f\uff08{dateRange}\uff09 \u7b80\u62a5\u5982\u4e0b\uff1a\r\n\u603b\u63d0\u53ca\u91cf\uff1a{totalMention}\uff0c\u63d0\u53ca\u91cf\u73af\u6bd4\uff1a{mentionRate}\uff0c\u8d1f\u9762\u63d0\u53ca\u7387\uff1a{experience}\uff0c\u8d1f\u9762\u63d0\u53ca\u7387\u73af\u6bd4\uff1a{experienceCycle}\r\n\u66f4\u591a\u8be6\u60c5\u8bf7\u767b\u5f55PC\u7aefDDM\u7cfb\u7edf\u2014\u542f\u660e\u661f\u2014\u4fe1\u606f\u8ba2\u9605-\u6211\u7684\u8ba2\u9605\uff0c\u8fdb\u884c\u67e5\u770b\uff01

#\u65e5\u5fd7\u7ea7\u522b
logging.level.com.meicloud=INFO
#\u5355\u70b9\u767b\u5f55\u9000\u51faURL
cas.logout.url=https://caddm.changan.com.cn/cas/logout?service=
cas.server.url.prefix=https://caddm.changan.com.cn/cas
cas.user.names=67893
cas.user.unauthorized_url=unauthorized.html
#\u4e0d\u62e6\u622a\u8d44\u6e90
cas.ignore_pattern=.*

#thymeleaf
spring.thymeleaf.cache=false
spring.thymeleaf.suffix=.html
spring.thymeleaf.prefix=classpath:page/
spring.thymeleaf.encoding=UTF-8
spring.resources.static-locations=classpath:page/,file:/apps/micros/changan-voc3.0/image
spring.boot.admin.notify.mail.enabled=false

#es \u8fde\u63a5
es.v7.host=************
es.v7.port=60000
es.v7.protocol=http
es.v7.username=
es.v7.password=
es.v7.index.keyword.mention.day.write=dmp-voc-keyword-mention-day
es.v7.index.dm.brand.keyword.mention.day.write=dm-brand-keyword-mention-day
es.v7.index.keyword.mention.month.write=dmp-voc-keyword-mention-month
es.v7.index.keyword.mention.year.write=dmp-voc-keyword-mention-year
es.v7.index.keyword.model.mention.day.write=dmp-voc-keyword-model-mention-day
es.v7.index.dm.region.keyword.mention.year.write=dmp-voc-keyword-mention-region-year
es.v7.index.dm.region.keyword.mention.month.write=dmp-voc-keyword-mention-region-month
es.v7.index.dm.region.keyword.mention.day.write=dmp-voc-keyword-mention-region-day
es.v7.index.dm.brand.mention.day.write=dmp-voc-brand-mention-day
es.v7.index.dm.brand.mention.month.write=dmp-voc-brand-mention-month
es.v7.index.dm.brand.mention.year.write=dmp-voc-brand-mention-year
#\u4eba\u7fa4\u7279\u5f81
es.v7.index.dm.customer.person.dtl.write=dmp-voc-customer-persona-dtl
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u65e5\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.day.write=dmp-voc-keyword-mention-total-day
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.month.write=dmp-voc-keyword-mention-total-month
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.year.write=dmp-voc-keyword-mention-total-year
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.work.order.dtl.write=dmp-voc-work-order
# es\ufffd\ufffd\u046f\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.consult.dtl.write=dmp-voc-consult
# es\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.opinion.dtl.write=dmp-voc-opinion
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.posts.comment.dtl.write=dmp-voc-posts-comment
# es\ufffd\u02be\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.questionnaire.dtl.write=dmp-voc-questionnaire
es.v7.index.keyword.mention.day=dmp-voc-keyword-mention-day-query
es.v7.index.dm.brand.keyword.mention.day=dm-brand-keyword-mention-day-query
es.v7.index.keyword.mention.month=dmp-voc-keyword-mention-month-query
es.v7.index.keyword.mention.year=dmp-voc-keyword-mention-year-query
es.v7.index.keyword.model.mention.day=dmp-voc-keyword-model-mention-day-query
es.v7.index.dm.region.keyword.mention.year=dmp-voc-keyword-mention-region-year-query
es.v7.index.dm.region.keyword.mention.month=dmp-voc-keyword-mention-region-month-query
es.v7.index.dm.region.keyword.mention.day=dmp-voc-keyword-mention-region-day-query
es.v7.index.dm.brand.mention.day=dmp-voc-brand-mention-day-query
es.v7.index.dm.brand.mention.month=dmp-voc-brand-mention-month-query
es.v7.index.dm.brand.mention.year=dmp-voc-brand-mention-year-query
#\u4eba\u7fa4\u7279\u5f81
es.v7.index.dm.customer.person.dtl=dmp-voc-customer-persona-dtl-query
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u65e5\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.day=dmp-voc-keyword-mention-total-day-query
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.month=dmp-voc-keyword-mention-total-month-query
#\u603b\u5173\u952e\u8bcd\u63d0\u53ca\u91cf\u6708\u5ea6\u8868
es.v7.index.dm.keyword.mention.total.year=dmp-voc-keyword-mention-total-year-query
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.work.order.dtl=dmp-voc-work-order-query
# es\ufffd\ufffd\u046f\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.consult.dtl=dmp-voc-consult-query
# es\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.opinion.dtl=dmp-voc-opinion-query
# es\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.posts.comment.dtl=dmp-voc-posts-comment-query
# es\ufffd\u02be\ufffd\ufffd\ufffd\u03f8
es.v7.index.dwd.evt.questionnaire.dtl=dmp-voc-questionnaire-query
# es\ufffd\ufffd\u03f8\ufffd\ufffd\ufffd\ufffd\ufffd\ufffd
es.v7.index.dwd.evt.dtl=${es.v7.index.dwd.evt.work.order.dtl},${es.v7.index.dwd.evt.consult.dtl},\
  ${es.v7.index.dwd.evt.opinion.dtl},${es.v7.index.dwd.evt.posts.comment.dtl},${es.v7.index.dwd.evt.questionnaire.dtl}
es.v7.index.prefix=dmp-voc-
es.v7.highlight.fragment_size=200
es.v7.highlight.number_of_fragments=5


# KTM
changan.api.protocol=http://
changan.ktm.url=cmpuat.changan.com
changan.login.url=cmpuat.changan.com
changan.login.accessToken=rescenter/rest/resRestApi/v2/oAuth2GetAccessToken
changan.login.auth2login=rescenter/rest/resRestApi/v2/oAuth2Login
changan.login.appId=f72eb2f8-b054-4332-bea9-7db8bf62eaac
changan.login.secret=98996d2c-307d-432d-aa9a-cc7709074e04
changan.login.loginId=000002
changan.ktm.task.get=ws/rest/task/v1/getTask?taskId=%s
changan.ktm.task.create=ws/rest/task/v1/createTask
changan.ktm.task.delete=ws/rest/task/v1/deleteTask
changan.ktm.domain.query=ws/rest/task/v1/getTaskListByDomain?domainId=%s&health=%d
changan.ktm.domain.id=f3ebec90-7145-4306-a8b1-3194f3945e84

# \u83b7\u53d6\u90e8\u95e8\u6811
changan.departmentTree.uri=/rescenter/rest/resRestApi/v2/getDepartmentTree
# \u83b7\u53d6\u7528\u6237\u5217\u8868
changan.userList.uri=/rescenter/rest/resRestApi/v2/getUserListBySearch
# \u83b7\u53d6\u7528\u6237\u5217\u8868(\u6240\u6709)
changan.allUserInfo.uri=/rescenter/rest/resRestApi/v2/getAllUserInfo
# \u5f97\u5230\u5f53\u524d\u90e8\u95e8\u4e0b\u7684\u6240\u6709\u7528\u6237
changan.departmentUsers.uri=/rescenter/rest/resRestApi/v2/getUserListInDept
# \u6839\u636e\u7528\u6237\u767b\u5f55\u540d\u83b7\u53d6\u7528\u6237\u4fe1\u606f
changan.userInfoById.uri=/rescenter/rest/resRestApi/v2/getUserByLoginId
# \u6839\u636e\u7528\u6237\u767b\u5f55\u540d\u83b7\u53d6\u7528\u6237\u4fe1\u606f
changan.userInfoByToken.uri=/rescenter/rest/resRestApi/v2/getUserByToken
#\u767b\u5f55token\u8ba4\u8bc1
changan.checkIdentityToken.uri=/rescenter/rest/resRestApi/v2/checkIdentityToken
changan.getUserByToken.uri=/rescenter/rest/resRestApi/v2/getUserByToken
# \u957f\u5b89\u96c6\u56e2\u7684\u516c\u53f8id
changan.default.companyId.id=8f4ddf6d-507a-4835-8db5-84e1480823f0
# \u83b7\u53d6\u8fd1\u671f\u79bb\u804c\u7684\u4eba\u5458\u4fe1\u606f
changan.disable.user.uri=/rescenter/rest/resRestApi/v2/getDisableUserListByTime


file.upload.path=/apps/micros/changan-voc3.0/file/upload
file.tmp.path=/apps/micros/changan-voc3.0/file/tmp
#file.upload.path=E:\\Test
#file.tmp.path=E:\\Test


aliyun.oss.file.endPoint=oss-cn-chongqing-ch-d01-a.res.dip.cacloud.com/shuju-ubd-test
aliyun.oss.file.keyId=IDb4cHgkRd2TZ3OIui
aliyun.oss.file.keySecret=SecretgqTpoiJegFrkkPDyGxUZ7UW91ErZlo
aliyun.oss.file.bucket=shuju-ubd-test

##\u7b97\u6cd5URL
#algorithm.analyzer.baseUrl=http://************:8100
algorithm.analyzer.baseUrl=http://***********:8100
algorithm.findKeyWord.url=/changan/voc/findKeyWord
algorithm.findKeyWord.batch.url=/changan/voc/findKeyWordBatch
algorithm.analyzer.url=/changan/voc/analyzer

# ?????????
algorithm.dataSourceScore.baseUrl=http://***********:8100/changan/voc/weight?batchId=%d

#MSS URL
mss.url=http://***********:18081

#\u5b9a\u65f6\u65e5\u671f\u4efb\u52a1\u914d\u7f6e\u6587\u4ef6
SubscribeServiceImpl.syncDayResult=0 0 10 * * ? 
SubscribeServiceImpl.syncWeekResult=0 0 10 * * ? 
SubscribeServiceImpl.syncMonthResult=0 0 10 * * ? 

KeywordCorpusServiceImpl.noResultContentDrill=0 0 0/1 * * ?  
KeywordCorpusServiceImpl.system.wordcnt=4

SyncTableServiceImpl.syncData=0 0 23 * * ? 

TaskServiceImpl.updateTaskDetailSchedule=0 */30 * * * ?

monitor.web.url=http://127.0.0.1:8686,http://10.74.154.35:8686,http://10.74.154.32:8686
monitor.nlp.dataSources=\u901a\u7528\u7b97\u6cd5|\u6c7d\u8f66\u4e4b\u5bb6-\u7528\u6237\u53d1\u5e16,\u95ee\u7b54\u7c7b\u578b\u7b97\u6cd5|\u957f\u5b89\u767e\u79d1-\u95ee\u7b54,\u667a\u6167\u5c0f\u5b89\u7b97\u6cd5|\u8f66\u673a\u7aef-\u667a\u6167\u5c0f\u5b89,\u95ee\u5377-\u76f4\u8bc4\u7b97\u6cd5|\u957f\u5b89\u6c7d\u8f66\u76f4\u8bc4,\u95ee\u5377-GQRS\u7b97\u6cd5|GQRS,\u53e3\u7891\u8bc4\u5206\u7b97\u6cd5|\u6c7d\u8f66\u4e4b\u5bb6-\u53e3\u7891\u8bc4\u5206,\u8054\u7edc\u4e2d\u5fc3\u70ed\u7ebf\u670d\u52a1\u7b97\u6cd5|\u8054\u7edc\u4e2d\u5fc3\u70ed\u7ebf\u670d\u52a1,\u957f\u5b89\u5546\u57ce\u8bc4\u4ef7\u7b97\u6cd5|\u957f\u5b89\u5546\u57ce\u8bc4\u4ef7,\u4f53\u9a8c\u5b98-\u6d3b\u52a8\u7b97\u6cd5|\u4f53\u9a8c\u5b98-\u6d3b\u52a8,\u7ebf\u4e0b\u95ee\u5377\u5bfc\u5165\u7b97\u6cd5|\u7ebf\u4e0b\u95ee\u5377\u5bfc\u5165
monitor.nlp.java.url=http://***********:8100,http://10.16.1.172:8100
monitor.nlp.java.status.url=/changan/voc/health-check
monitor.nlp.python.url=http://10.16.1.156:8100,http://10.16.1.157:8100

schedule.job.domain=***********:18081
#schedule.job.domain=vocu.changan.com.cn:8000
schedule.job.runTaskUrl=MSS/runJob/reRunGroup
schedule.job.queryParameter=MSS/job/queryParameter?rownum=10&pagenum=1&jobId=%s
schedule.job.insertAndupdateParameter=MSS/job/insertAndupdateParameter
schedule.job.mssHome=MSS/callback?client_name=CasClient
#schedule.job.mssHome=index.html
schedule.job.casServerUrl=https://caddm.changan.com.cn/cas/login
schedule.job.cookiePath=/MSS
#schedule.job.cookiePath=/
schedule.job.userInfo=MSS/account/getUserInfo

data-source.config.esTypeMap[\u5de5\u5355]=${es.v7.index.dwd.evt.work.order.dtl}
data-source.config.esTypeMap[\u54a8\u8be2]=${es.v7.index.dwd.evt.consult.dtl}
data-source.config.esTypeMap[\u610f\u89c1\u53cd\u9988]=${es.v7.index.dwd.evt.opinion.dtl}
data-source.config.esTypeMap[\u5e16\u5b50\u8bc4\u4ef7]=${es.v7.index.dwd.evt.posts.comment.dtl}
data-source.config.esTypeMap[\u95ee\u5377]=${es.v7.index.dwd.evt.questionnaire.dtl}