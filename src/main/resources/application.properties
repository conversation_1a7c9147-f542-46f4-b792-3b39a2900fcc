spring.profiles.active=local

server.port=8686
spring.application.name=voc3.0
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=1000MB
server.servlet.context-path=/
server.tomcat.remoteip.remote-ip-header=X-Forwarded-For
server.tomcat.remoteip.protocol-header=X-Forwarded-Proto
server.tomcat.remoteip.protocol-header-https-value=https

logging.level.org.springframework.boot.autoconfigure=error

#JSON\u683c\u5f0f\u5316
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.default-property-inclusion=NON_NULL

jasypt.encryptor.password=voC@2022
#
swagger.production=true
login.user.password=voc#24658
swagger.basic.enable=true
swagger.basic.username=voc
swagger.basic.password=voc#24658
#\u767b\u5f55\u65b9\u5f0f\uff1asimple\u6216\u8005cas
login.type=simple
cas.server.host.url=http://k.meicloud.com/login/sso
cas.server.host.login_url=${cas.server.host.url}/login
cas.server.host.logout_url=${cas.server.host.url}/logout?service=${app.server.host.url}
app.server.host.url=http://127.0.0.1:9527
app.login.url=/index
app.logout.url=/logout
minio.url=http://************:9000
minio.user=minioadmin
minio.password=minioadmin

#mybatis
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.type-aliases-package=com.meicloud.voc.**.entity
mybatis-plus.mapper-locations=classpath:/mapper/*.xml

#\u6570\u636e\u6e90\u7c7b\u578b
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
##### \u8fde\u63a5\u6c60\u914d\u7f6e #######
# \u8fde\u63a5\u6c60\u7684\u914d\u7f6e\u4fe1\u606f
# \u521d\u59cb\u5316\u5927\u5c0f\uff0c\u6700\u5c0f\uff0c\u6700\u5927
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.maxActive=20
# \u914d\u7f6e\u83b7\u53d6\u8fde\u63a5\u7b49\u5f85\u8d85\u65f6\u7684\u65f6\u95f4
spring.datasource.druid.maxWait=60000
# \u914d\u7f6e\u95f4\u9694\u591a\u4e45\u624d\u8fdb\u884c\u4e00\u6b21\u68c0\u6d4b\uff0c\u68c0\u6d4b\u9700\u8981\u5173\u95ed\u7684\u7a7a\u95f2\u8fde\u63a5\uff0c\u5355\u4f4d\u662f\u6beb\u79d2
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# \u914d\u7f6e\u4e00\u4e2a\u8fde\u63a5\u5728\u6c60\u4e2d\u6700\u5c0f\u751f\u5b58\u7684\u65f6\u95f4\uff0c\u5355\u4f4d\u662f\u6beb\u79d2
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=select 'x'
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
# \u6253\u5f00PSCache\uff0c\u5e76\u4e14\u6307\u5b9a\u6bcf\u4e2a\u8fde\u63a5\u4e0aPSCache\u7684\u5927\u5c0f
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
# \u8fc7\u6ee4\u5668\u8bbe\u7f6e\uff08\u7b2c\u4e00\u4e2astat\u5f88\u91cd\u8981\uff0c\u6ca1\u6709\u7684\u8bdd\u4f1a\u76d1\u63a7\u4e0d\u5230SQL\uff09
spring.datasource.druid.filters=stat,wall,log4j2
##### WebStatFilter\u914d\u7f6e #######
#\u542f\u7528StatFilter
spring.datasource.druid.web-stat-filter.enabled=true
#\u6dfb\u52a0\u8fc7\u6ee4\u89c4\u5219
spring.datasource.druid.web-stat-filter.url-pattern=/*
#\u6392\u9664\u4e00\u4e9b\u4e0d\u5fc5\u8981\u7684url
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*
#\u5f00\u542fsession\u7edf\u8ba1\u529f\u80fd
spring.datasource.druid.web-stat-filter.session-stat-enable=true
#\u7f3a\u7701sessionStatMaxCount\u662f1000\u4e2a
spring.datasource.druid.web-stat-filter.session-stat-max-count=1000
##### StatViewServlet\u914d\u7f6e #######
#\u542f\u7528\u5185\u7f6e\u7684\u76d1\u63a7\u9875\u9762
spring.datasource.druid.stat-view-servlet.enabled=true
#\u5185\u7f6e\u76d1\u63a7\u9875\u9762\u7684\u5730\u5740
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
#\u5173\u95ed Reset All \u529f\u80fd
spring.datasource.druid.stat-view-servlet.reset-enable=false
#\u8bbe\u7f6e\u767b\u5f55\u7528\u6237\u540d
spring.datasource.druid.stat-view-servlet.login-username=admin
#\u8bbe\u7f6e\u767b\u5f55\u5bc6\u7801
spring.datasource.druid.stat-view-servlet.login-password=123
#\u767d\u540d\u5355\uff08\u5982\u679callow\u6ca1\u6709\u914d\u7f6e\u6216\u8005\u4e3a\u7a7a\uff0c\u5219\u5141\u8bb8\u6240\u6709\u8bbf\u95ee\uff09
spring.datasource.druid.stat-view-servlet.allow=
#\u9ed1\u540d\u5355\uff08deny\u4f18\u5148\u4e8eallow\uff0c\u5982\u679c\u5728deny\u5217\u8868\u4e2d\uff0c\u5c31\u7b97\u5728allow\u5217\u8868\u4e2d\uff0c\u4e5f\u4f1a\u88ab\u62d2\u7edd\uff09
spring.datasource.druid.stat-view-servlet.deny=

#knife4j\u914d\u7f6e
knife4j.enable=true
knife4j.setting.enableFooter=false
knife4j.setting.enableFooterCustom=true
knife4j.setting.footerCustomContent=Copyright 2016 - 2021 Meicloud. All Rights Reserved
spring.boot.admin.notify.mail.enabled=false

#\u6807\u51c6\u5173\u952e\u8bcd\u4e0a\u4f20\u914d\u7f6e
standard-keyword.upload.config.nullErrorMsg=\u4e0d\u4e3a\u7a7a
standard-keyword.upload.config.recurStandardKeywordErrorMsg=\u6587\u4ef6\u4e2d\u5b58\u5728\u91cd\u590d\u7684\u6807\u51c6\u5173\u952e\u8bcd
standard-keyword.upload.config.standardKeywordErrorMsg=\u60c5\u611f\u5c5e\u6027\u4e3a\u4e2d\u6027\uff0c\u6807\u51c6\u5173\u952e\u8bcd\u4ee5\u201c\u54a8\u8be2/\u9648\u8ff0\u201d\u5f00\u5934\uff0c\u5426\u5219\uff0c\u4e0d\u4ee5\u201c\u54a8\u8be2/\u9648\u8ff0\u201d\u5f00\u5934
standard-keyword.upload.config.standardKeywordNotExistErrorMsg=\u5e93\u4e2d\u5b58\u5728\u76f8\u540c\u6807\u51c6\u5173\u952e\u8bcd
standard-keyword.upload.config.emotionErrorMsg=\u60c5\u611f\u5c5e\u6027\u53d6\u503c\u8303\u56f4\u4ec5\u6709\uff1a\u6b63\u9762\u3001\u8d1f\u9762\u3001\u4e2d\u6027
standard-keyword.upload.config.clarityErrorMsg=\u6e05\u6670\u5c5e\u6027\u53d6\u503c\u4ec5\u6709\uff1a\u6e05\u6670\u3001\u6a21\u7cca
standard-keyword.upload.config.fieldErrorMsg=\u9886\u57df\u5c5e\u6027\u53d6\u503c\u8303\u56f4\u4ec5\u6709\uff1a\u4ea7\u54c1\u8bbe\u8ba1\u3001\u4ea7\u54c1\u8d28\u91cf\u3001\u8425\u9500\u670d\u52a1
standard-keyword.upload.config.indexErrorMsg=\u56db\u7ea7\u5206\u7c7b\u6216\u56db\u7ea7\u5206\u7c7bID\u53ef\u80fd\u4e0d\u5b58\u5728\u5206\u7c7b\u5e93\u4e2d\uff0c\u4e5f\u53ef\u80fd\u56db\u7ea7\u5206\u7c7b\u548c\u56db\u7ea7\u5206\u7c7bID\u4e0e\u5206\u7c7b\u4e2d\u7684\u4e0d\u4e00\u81f4
standard-keyword.upload.config.neutralStart=\u54a8\u8be2/\u9648\u8ff0

auth.ignore.urls=/es/overrideDWD/before|/es/overrideDM/before|/es/overrideDM/after|/unauthorized.html|.*.js|.*.jsp|.*.ttf|.*.png|.*.ico|/css/*|/fonts/*|/img/*|/js/*|/security/*
