<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.IndexSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.car.entity.IndexSystem">
        <id column="data_Id" property="dataId"/>
        <result column="job_name" property="jobName"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="status" property="status"/>
        <result column="first_index_id" property="firstIndexId"/>
        <result column="first_index_name" property="firstIndexName"/>
        <result column="second_index_id" property="secondIndexId"/>
        <result column="second_index_name" property="secondIndexName"/>
        <result column="third_index_id" property="thirdIndexId"/>
        <result column="third_index_name" property="thirdIndexName"/>
        <result column="four_index_id" property="fourIndexId"/>
        <result column="four_index_name" property="fourIndexName"/>
        <result column="index_type" property="indexType"/>
        <result column="index_id" property="indexId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_id
        , w_pdate_dt, w_insert_dt, job_name, batch_dt, first_index_id,
			first_index_name, second_index_id, second_index_name, 
			third_index_id, third_index_name, four_index_id, four_index_name, index_type, index_id, status
    </sql>
    <update id="updateIndexSystemStatus">
        update dim_voc3_index_system
        <set>
            status = #{status},
            job_name = #{jobName}
        </set>
        where index_id = #{indexId}
    </update>

    <update id="updateIndexSystem" parameterType="com.meicloud.voc.car.entity.IndexSystem">
        update dim_voc3_index_system
        <set>
            <if test="indexSystem.status != null and indexSystem.status != ''">status = #{indexSystem.status},</if>

            <if test="indexSystem.indexId != null and indexSystem.indexId != ''">index_id = #{indexSystem.indexId},</if>
            <if test="indexSystem.indexType != null and indexSystem.indexType != ''">index_type =
                #{indexSystem.indexType},
            </if>


            <if test="indexSystem.firstIndexId != null and indexSystem.firstIndexId != ''">first_index_id =
                #{indexSystem.firstIndexId},
            </if>
            <if test="indexSystem.firstIndexName != null and indexSystem.firstIndexName != ''">first_index_name =
                #{indexSystem.firstIndexName},
            </if>

            <if test="indexSystem.secondIndexId != null and indexSystem.secondIndexId != ''">second_index_id =
                #{indexSystem.secondIndexId},
            </if>
            <if test="indexSystem.secondIndexName != null and indexSystem.secondIndexName != ''">second_index_name =
                #{indexSystem.secondIndexName},
            </if>

            <if test="indexSystem.thirdIndexId != null and indexSystem.thirdIndexId != ''">third_index_id =
                #{indexSystem.thirdIndexId},
            </if>
            <if test="indexSystem.thirdIndexName != null and indexSystem.thirdIndexName != ''">third_index_name =
                #{indexSystem.thirdIndexName},
            </if>

            <if test="indexSystem.fourIndexId != null and indexSystem.fourIndexId != ''">four_index_id =
                #{indexSystem.fourIndexId},
            </if>
            <if test="indexSystem.fourIndexName != null and indexSystem.fourIndexName != ''">four_index_name =
                #{indexSystem.fourIndexName},
            </if>

            <if test="indexSystem.jobName != null and indexSystem.jobName != ''">job_name = #{indexSystem.jobName},</if>
            <if test="indexSystem.wInsertDt != null">w_insert_dt=#{indexSystem.wInsertDt}</if>
            w_pdate_dt = sysdate()
        </set>
        where index_type = #{indexSystem.indexType}
        and four_index_id = #{indexSystem.fourIndexId}
    </update>
    <update id="updateIndexSystemEnabled">
        update dim_voc3_index_system
        <set>
            `enabled` = #{enabled},
            job_name = #{jobName}
        </set>
        where
        index_id = #{indexId}
        and `status` = 1
        <![CDATA[and `enabled` <> #{enabled}]]>

    </update>
    <update id="updateIndexSystemInfo">
        update dim_voc3_index_system
        <set>
            `enabled` = #{enabled},
            `index_type` = #{indexTypeName},
            job_name = #{jobName}
        </set>
        where
        index_id = #{indexId}
        and `status` = 1
        and
        (
        <![CDATA[ `enabled` <> #{enabled}]]>
        or
        <![CDATA[ `index_type` <> #{indexTypeName}]]>
        )
    </update>

    <!-- getSearchIndex -->
    <select id="getSearchIndex" resultMap="BaseResultMap">
        <!-- SELECT data_id, first_index_id, first_index_name, second_index_id, second_index_name,
                third_index_id, third_index_name, four_index_id, four_index_name, index_type, index_id
        FROM dim_voc3_index_system t -->
        SELECT data_id, first_index_id, first_index_name, second_index_id, second_index_name,
        third_index_id, third_index_name, four_index_id, four_index_name, index_type, index_id
        FROM dim_voc3_index_system t
        where t.status = 1
        and t.enabled = '1'
        <if test="firstIndexIds != null and firstIndexIds.size() > 0">
            and t.first_index_id in
            <foreach collection="firstIndexIds" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="secondIndexIds != null and secondIndexIds.size() > 0">
            and t.second_index_id in
            <foreach collection="secondIndexIds" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="thirdIndexIds != null and thirdIndexIds.size() > 0">
            and t.third_index_id in
            <foreach collection="thirdIndexIds" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="fourIndexIds != null and fourIndexIds.size() > 0">
            and t.four_index_id in
            <foreach collection="fourIndexIds" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="indexType != null">
            and t.index_type = #{indexType}
        </if>
        order by
        first_index_id,second_index_id
    </select>
    <select id="findLastLevelModifyByRange" resultType="java.lang.String">
        select data_id
        from dim_voc3_index_system dvis,
        (select isi.data_id dataId,
        isi.index_id indexId,
        dvmis.index_type_name indexType,
        isi.item_id itemId,
        isi.item_name itemName,
        isi.level level,
        isi.parent_name_path parentNamePath,
        isi.parent_id_path parentIdPath,
        dvmis.enabled enabled,
        isi.status status,
        dvmis.status indexStatus
        from dim_voc3_m_index_system_item isi
        left join dim_voc3_m_index_system dvmis on isi.index_id = dvmis.data_id
        where
        isi.level = dvmis.metric_level
        <if test="startTime != null and endTime != null">
            and
            (
            (
            <if test="startTime != null">
                isi.w_pdate_dt >= #{startTime}
            </if>
            <if test="endTime != null">
                <![CDATA[and isi.w_pdate_dt <= #{endTime}]]>
            </if>

            )
            or (

            <if test="startTime != null">
                dvmis.w_pdate_dt >= #{startTime}
            </if>
            <if test="endTime != null">
                <![CDATA[and dvmis.w_pdate_dt <= #{endTime}]]>
            </if>
            )
            )
        </if>
        ) sis
        where dvis.status = '1'
          and dvis.index_id = sis.indexId
          <choose>
              <when test="indexLevel != null and indexLevel.trim() != '' and indexLevel == '1'.toString()">
                  and sis.parentIdPath = concat(
                  dvis.first_index_id, "@@")
              </when>
              <when test="indexLevel != null and indexLevel.trim() != '' and indexLevel == '2'.toString()">
                  and sis.parentIdPath = concat(
                  concat_ws("@@", dvis.first_index_id, dvis.second_index_id), "@@")
              </when>
              <when test="indexLevel != null and indexLevel.trim() != '' and indexLevel == '3'.toString()">
                  and sis.parentIdPath = concat(
                  concat_ws("@@", dvis.first_index_id, dvis.second_index_id, dvis.third_index_id), "@@")
              </when>
              <when test="indexLevel != null and indexLevel.trim() != '' and indexLevel == '4'.toString()">
                  and sis.parentIdPath = concat(
                  concat_ws("@@", dvis.first_index_id, dvis.second_index_id, dvis.third_index_id, dvis.four_index_id), "@@")
              </when>
              <otherwise>
                  and sis.parentIdPath = concat(
                  concat_ws("@@", dvis.first_index_id, dvis.second_index_id, dvis.third_index_id, dvis.four_index_id), "@@")
              </otherwise>
          </choose>
    </select>
    <select id="getIndexSystemWithoutStandardKeyword"
            resultType="com.meicloud.voc.selfAnalysis.dto.StKeywordDetailResult">
        select ist.index_id index_type, ist.index_type index_type_name,ist.first_index_id,ist.first_index_name,
               ist.second_index_id,ist.second_index_name,
               ist.third_index_id,ist.third_index_name,
               ist.four_index_id,ist.four_index_name,
        from dim_voc3_index_system ist
                 left join dim_voc3_index_system_standard_keyword st on st.four_index_id = ist.four_index_id and st.index_type = ist.index_type
        where isnull(st.data_id) and ist.status = '1'
    </select>


</mapper>
