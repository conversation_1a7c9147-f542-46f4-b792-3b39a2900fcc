<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.stKeywordManage.mapper.KeywordCorpusImportMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.stKeywordManage.entity.KeywordCorpusImport">
        <id column="data_id" property="dataId"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        <result column="last_modifier_id" property="lastModifierId"/>
        <result column="last_modifier_name" property="lastModifierName"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        
        <result column="file_name" property="fileName"/>
        <result column="file_cnt" property="fileCnt"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, job_name, batch_dt, w_pdate_dt, w_insert_dt, status, file_name, file_cnt, creator_id, 
		creator_name, last_modifier_id, last_modifier_name
	</sql>
    <!-- getWordCorpusByParams -->
    <select id="getFileListByParams" resultMap="BaseResultMap"
            parameterType="com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams">
        SELECT data_id, job_name, batch_dt, w_pdate_dt, w_insert_dt, status, file_name, file_cnt, creator_id, 
        	creator_name, last_modifier_id, last_modifier_name
		FROM dim_voc3_m_keyword_corpus_import t
        WHERE t.status != -1
        order by t.w_insert_dt desc
    </select>
    <!-- updDealStatus -->
    <delete id="deleteFileByIds" parameterType="String">
 		DELETE FROM dim_voc3_m_keyword_corpus_import
		WHERE data_id = #{dataId}        
 	</delete>
 	
 	<update id="updateByDataId" parameterType="com.meicloud.voc.manage.stKeywordManage.entity.KeywordCorpusImport">
 		update dim_voc3_m_keyword_corpus_import
 		<set>
 			<if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
 			<if test="fileCnt != null ">file_cnt = #{fileCnt},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="lastModifierId != null and lastModifierId != ''">last_modifier_id = #{lastModifierId},</if>
 			<if test="lastModifierName != null and lastModifierName != ''">last_modifier_name = #{lastModifierName},</if>
 			w_pdate_dt = sysdate()
 		</set>
 		where data_id = #{dataId}
	</update>
	
</mapper>
