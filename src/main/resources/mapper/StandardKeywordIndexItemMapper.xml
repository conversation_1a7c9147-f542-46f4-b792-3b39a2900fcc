<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.indexSystemManage.mapper.StandardKeywordIndexItemMapper">


    <update id="updateIndexItemId" >
        update dim_voc3_m_standard_keyword_index_item
        set item_data_id=#{newIndexItemDataId}
        where item_data_id =#{oldIndexItemDataId}
    </update>

    <select id="getStandardKeywordIndexInfo"
            resultType="com.meicloud.voc.manage.indexSystemManage.dto.StandardKeywordIndexInfo">
        SELECT ski.standard_keyword_id as standardKeywordId,
               ski.item_data_id as indexItemId,
               mis.data_id as indexId,
               mis.index_type_name as indexType,
               isi.parent_name_path as parentNamePath,
               isi.parent_id_path as parentIdPath
               FROM
                (SELECT * FROM dim_voc3_m_standard_keyword_index_item
                          where
                          1 = 1
                          <if test="standardKeywordIds != null and standardKeywordIds.size() > 0">
                            and standard_keyword_id in
                            <foreach collection="standardKeywordIds" item="standardKeywordId" open="(" separator="," close=")">
                              #{standardKeywordId}
                            </foreach>
                          </if>
                          ) ski
                    LEFT JOIN dim_voc3_m_index_system_item isi ON ski.item_data_id = isi.data_id
                    LEFT JOIN dim_voc3_m_index_system mis ON isi.index_id = mis.data_id
                WHERE
                    isi.status = '1'
                    AND
                    mis.status = '1'


    </select>
</mapper>
