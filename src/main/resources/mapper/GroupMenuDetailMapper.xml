<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.group.mapper.GroupMenuDetailMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.group.entity.GroupMenuDetail">
		<id column="group_id" property="groupId" />
        <result column="menu_id" property="menuId" />
        <result column="update_time" property="updateTime" />
	</resultMap>
	<!-- updateGroupById -->
	<resultMap id="VoResultMap"
		type="com.meicloud.voc.security.api.vo.RoleInfoVo">
		<id column="group_id" property="roleId" />
		<result column="group_name" property="roleName" />
		
		<result column="user_id" property="userId" />
		<result column="user_account" property="userAccount" />
		<result column="user_name" property="userName" />
	</resultMap>
	<!-- updateGroupById -->
	<resultMap id="UserResultMap"
		type="com.meicloud.voc.security.api.vo.UserInfoVo">
		<result column="user_id" property="userId" />
		<result column="user_account" property="userAccount" />
		<result column="user_name" property="userName" />
	</resultMap>
	<!-- updateGroupById -->
	<resultMap id="MenuResultMap"
		type="com.meicloud.voc.security.api.vo.MenuInfoVo">
		<result column="menu_id" property="modelId" />
		<result column="menu_name" property="modelName" />
		<result column="p_menu_id" property="parentModelId" />
	</resultMap>
	<!-- Base_Column_List -->
	<sql id="Base_Column_List">
		group_id, menu_id, update_time, group_name
	</sql>
	<!-- getRoleUserList -->
	<select id="getRoleUserList" resultMap="UserResultMap">
		SELECT t2.user_id,t2.user_account,t2.user_name 
		from t_sys_user_group t1 
		inner join t_user t2 on t2.user_id = t1.user_id
		where t1.group_id = #{roleId}
		limit ${startSize}, ${pageSize} 
	</select>
	<!-- getRoleUserTotal -->
	<select id="getRoleUserTotal" resultType="Integer">
		SELECT count(*)
		from t_sys_user_group t1 
		inner join t_user t2 on t2.user_id = t1.user_id
		where t1.group_id = #{roleId}
	</select>
	<!-- getRoleMenuList -->
	<select id="getRoleMenuList" resultMap="MenuResultMap">
		SELECT t2.menu_id,t2.menu_name,t2.p_menu_id 
		from t_sys_group_menu_detail t1 
		left join t_sys_menu t2 on t2.menu_id = t1.menu_id
		where t1.group_id = #{roleId}
	</select>
	<!-- deleteByRoleMenuIds -->
	<delete id="deleteByRoleMenuIds">
 		delete from t_sys_group_menu_detail where group_id = #{roleId} and menu_id = #{menuId}
 	</delete>
 	<!-- deleteByRoleMenuIds -->
	<delete id="deleteMenuByRoleId">
 		delete from t_sys_group_menu_detail where group_id = #{roleId}
 	</delete>
	
</mapper>
