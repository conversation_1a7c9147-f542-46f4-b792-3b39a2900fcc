<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CarMarketMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CarMarket">
		<id column="id" property="id" />
		<result column="value" property="value" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, value
	</sql>
	<!-- getSearchCarMarket -->
	<select id="getSearchCarMarket" resultMap="BaseResultMap">
		select
		serie.market_id as id,
		serie.market_name as value
		from 
		dim_voc3_car_series serie
		where serie.status = 1
		<if test="brandCodes != null and brandCodes.size() > 0">
			and serie.brand_code in
			<foreach collection="brandCodes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		group by id,value
	</select>


</mapper>
