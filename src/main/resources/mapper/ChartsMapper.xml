<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.ChartsMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.Charts">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="status" property="status" />
		
		<result column="charts_id" property="chartsId" />
		<result column="charts_name" property="chartsName" />
	</resultMap>
	<!-- VOResultMap -->
	<resultMap id="VOResultMap"
		type="com.meicloud.voc.car.vo.ChartsVO">
		<result column="charts_id" property="chartsId" />
		<result column="charts_name" property="chartsName" />
		<result column="index_type" property="indexType" />
		<result column="name" property="name" />
		<result column="value" property="value" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, status, charts_id, charts_name
	</sql>

	<!-- getChartsList -->
	<select id="getChartsList" resultMap="VOResultMap">
		select t1.charts_id,t1.charts_name,name,remark as value, t3.index_type
		from t_sys_portal_charts t1
		left join t_sys_portal_charts_index t2 on t2.charts_id = t1.data_id
		left join t_sys_index_remark t3 on t3.data_id = t2.remark_id
		where t1.status = 1
		<if test="chartsId != null and chartsId.size() > 0">
			and t1.charts_id in
			<foreach collection="chartsId" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		order by t3.index_type desc, t3.order_seq
	</select>

</mapper>
