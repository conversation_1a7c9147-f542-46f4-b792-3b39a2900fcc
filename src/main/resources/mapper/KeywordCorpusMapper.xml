<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.stKeywordManage.mapper.KeywordCorpusMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.stKeywordManage.vo.KeywordCorpusVo">
        <id column="data_id" property="dataId"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        <result column="last_modifier_id" property="lastModifierId"/>
        <result column="last_modifier_name" property="lastModifierName"/>
        
        <result column="keyword" property="keyword"/>
        <result column="deal_status" property="dealStatus"/>
        <result column="sentence" property="sentence"/>
        <result column="original_id" property="originalId"/>
        <result column="add_type" property="addType"/>
        <result column="source" property="source"/>
        <result column="word_cnt" property="wordCnt"/>
        
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        
        <result column="standard_keyword_id" property="standardKeywordId"/>
        <result column="standard_keyword" property="standardKeyword"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, brand_code, brand_name, brand_first_letter, 
		nature, w_pdate_dt, w_insert_dt, job_name, batch_dt, order_seq, brand_logo
	</sql>
	<!-- getWordkeywordByParams -->
    <select id="getWordCorpusByParams" resultMap="BaseResultMap"
            parameterType="com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams">
        SELECT t1.name as standard_keyword, t.data_id, t.keyword, t.sentence, t.original_id, t.add_type, t.source, t.word_cnt, 
        	t.standard_keyword_id, t.deal_status, t.job_name, t.w_insert_dt, t.w_pdate_dt, t.batch_dt, t.status, t.creator_id, 
        	t.creator_name, t.last_modifier_id, t.last_modifier_name
		FROM dim_voc3_m_keyword_corpus t
		left join dim_voc3_m_standard_keyword t1 on t1.data_id = t.standard_keyword_id
        WHERE t.status = 1
        and t.deal_status != 3
		<if test="corpus.keyword != null and corpus.keyword != ''">
			and t.keyword like CONCAT("%",#{corpus.keyword},"%")
		</if>
		<if test="corpus.dataSources != null and corpus.dataSources.size() > 0">
        	and t.source in
        	<foreach collection="corpus.dataSources" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
		<if test="corpus.addType != null and corpus.addType != ''">
			and t.add_type = #{corpus.addType}              
		</if>
		<if test="corpus.dealStatus != null and corpus.dealStatus != ''">
			and t.deal_status = #{corpus.dealStatus}              
		</if>
		<if test="corpus.dataIds != null and corpus.dataIds.size() > 0">
        	and t.data_id in
        	<foreach collection="corpus.dataIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
        <if test="corpus.fileId != null and corpus.fileId != ''">
			and t.file_id = #{corpus.fileId}              
		</if>
		<if test="corpus.startDate != null and corpus.startDate != ''">
			and t.w_pdate_dt >= #{corpus.startDate}              
		</if>
		<if test="corpus.endDate != null and corpus.endDate != ''">
			<![CDATA[ 
				and t.w_pdate_dt < #{corpus.endDate}    
			]]>
		</if>
		<if test="corpus.creatorName != null and corpus.creatorName != ''">
			and t.creator_name like CONCAT("%",#{corpus.creatorName},"%")
		</if>
		<if test="corpus.orders != null and corpus.orders.size() > 0">
			order by 
        	<foreach collection="corpus.orders" item="name" index="index" separator=",">
				${name.property} ${name.direction}
			</foreach>
        </if>
        <if test="corpus.startSize != null and corpus.size != null">
	        limit #{corpus.startSize},#{corpus.size}
        </if>
    </select>
	<!-- getWordCorpusByParamsTotal -->
    <select id="getWordCorpusByParamsTotal" resultType="Integer"
            parameterType="com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams">
        SELECT count(t.data_id)
		FROM dim_voc3_m_keyword_corpus t
        WHERE t.status = 1
        and t.deal_status != 3
		<if test="corpus.keyword != null and corpus.keyword != ''">
			and t.keyword like CONCAT("%",#{corpus.keyword},"%")
		</if>
		<if test="corpus.dataSources != null and corpus.dataSources.size() > 0">
        	and t.source in
        	<foreach collection="corpus.dataSources" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
		<if test="corpus.addType != null and corpus.addType != ''">
			and t.add_type = #{corpus.addType}              
		</if>
		<if test="corpus.dealStatus != null and corpus.dealStatus != ''">
			and t.deal_status = #{corpus.dealStatus}              
		</if>
		<if test="corpus.dataIds != null and corpus.dataIds.size() > 0">
        	and t.data_id in
        	<foreach collection="corpus.dataIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
        <if test="corpus.fileId != null and corpus.fileId != ''">
			and t.file_id = #{corpus.fileId}              
		</if>
		<if test="corpus.startDate != null and corpus.startDate != ''">
			and t.w_pdate_dt >= #{corpus.startDate}              
		</if>
		<if test="corpus.endDate != null and corpus.endDate != ''">
			<![CDATA[ 
				and t.w_pdate_dt < #{corpus.endDate}    
			]]>
		</if>
		<if test="corpus.creatorName != null and corpus.creatorName != ''">
			and t.creator_name like CONCAT("%",#{corpus.creatorName},"%")
		</if>
    </select>
    
	<!-- 获取新词语料 -->
    <select id="getKeywordCorpusList" resultType="String" >
        SELECT t.keyword
		FROM dim_voc3_m_keyword_corpus t
		where 1=1
		<if test="keywordList != null and keywordList.size() > 0">
        	and t.keyword in
        	<foreach collection="keywordList" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
		union all 
		SELECT t.keyword
		FROM dim_voc3_m_keyword t
		where 1=1
		and t.status = 1 
		<if test="keywordList != null and keywordList.size() > 0">
        	and t.keyword in
        	<foreach collection="keywordList" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
    </select>
	<!-- getStatusCnt -->
    <select id="getStatusCnt" resultType="Map">
		(select 'lastModifierTime' as status,convert(w_insert_dt,char) as total from dim_voc3_m_keyword_corpus where 1=1
			and status = 1
			order by w_insert_dt desc
			limit 1)				
		union all				
		(select deal_status as status,convert(count(data_id),char) as total from dim_voc3_m_keyword_corpus t
			where t.deal_status != 3
			<if test="corpus.keyword != null and corpus.keyword != ''">
				and t.keyword like CONCAT("%",#{corpus.keyword},"%")
			</if>
			<if test="corpus.dataSources != null and corpus.dataSources.size() > 0">
	        	and t.source in
	        	<foreach collection="corpus.dataSources" item="name" index="index" open="(" close=")" separator=",">
					#{name}
				</foreach>
	        </if>
			<if test="corpus.addType != null and corpus.addType != ''">
				and t.add_type = #{corpus.addType}              
			</if>
			<if test="corpus.dealStatus != null and corpus.dealStatus != ''">
				and t.deal_status = #{corpus.dealStatus}              
			</if>
			<if test="corpus.dataIds != null and corpus.dataIds.size() > 0">
	        	and t.data_id in
	        	<foreach collection="corpus.dataIds" item="name" index="index" open="(" close=")" separator=",">
					#{name}
				</foreach>
	        </if>
	        <if test="corpus.startDate != null and corpus.startDate != ''">
				and t.w_pdate_dt >= #{corpus.startDate}              
			</if>
			<if test="corpus.endDate != null and corpus.endDate != ''">
				<![CDATA[ 
					and t.w_pdate_dt < #{corpus.endDate}    
				]]>
			</if>
			group by deal_status)				
    </select>
	<!-- getKyewordCnt -->
    <select id="getKyewordCnt" resultType="Map">
        select standard_keyword_id as id, count(data_id) as total 
        	from dim_voc3_m_keyword_corpus t
        where t.status = 1
        <if test="keywordIds != null and keywordIds.size() > 0">
        	and t.standard_keyword_id in
        	<foreach collection="keywordIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
        </if>
		group by original_id
    </select>
	<!-- updDealStatus -->
    <update id="updDealStatus">
    	update dim_voc3_m_keyword_corpus set deal_status = #{status} where data_id = #{id} 
	</update>
	<!-- updCorpusBath -->
	<update id="updCorpusBath" parameterType="com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams">
 		update dim_voc3_m_keyword_corpus
 		<set>
 			<if test="dealStatus != null and dealStatus != ''">deal_status = #{dealStatus},</if>
 			<if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
 			<if test="standardKeywordId != null and standardKeywordId != ''">standard_keyword_id = #{standardKeywordId},</if>
 			
 			<if test="operatorId != null and operatorId != ''">last_modifier_id = #{operatorId},</if>
 			<if test="operatorName != null and operatorName != ''">last_modifier_name = #{operatorName},</if>
 			w_pdate_dt = sysdate()
 		</set>
 		where data_id in
		<foreach collection="dataIds" item="name" index="index" open="(" close=")" separator=",">
			#{name}
		</foreach>
	</update>
	<select id="getExistKeywordCorpusList" resultType="com.meicloud.voc.utils.Pair">
		SELECT t.keyword as `key`, t.data_id as `value`
		FROM dim_voc3_m_keyword_corpus t
		where 1=1
		<if test="keywordList != null and keywordList.size() > 0">
			and t.keyword in
			<foreach collection="keywordList" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
	</select>


	<!-- 获取没有结果的原文信息 -->
    <select id="getNoResultContentList" resultMap="BaseResultMap"
            parameterType="com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams">
        SELECT data_id as original_id, data_source as source, content, w_insert_dt, fs_status
			FROM dim_voc3_m_content_no_result t
        WHERE t.status = 1
        <if test="corpus.size != null and corpus.size != ''">
			limit #{corpus.size}              
		</if>
    </select>
    <!-- 更新原文已经进行新词分析 -->
    <update id="updateContentStatus">
 		update dim_voc3_m_content_no_result
 		<set>
 			<if test="status != null and status != ''">status = #{status},</if>
 			w_pdate_dt = sysdate()
 		</set>
 		where data_id in
		<foreach collection="dataIds" item="name" index="index" open="(" close=")" separator=",">
			#{name}
		</foreach>
	</update>
	
	<update id="updateWordCntByKeyword" parameterType="KeywordCorpus">
    	update dim_voc3_m_keyword_corpus set word_cnt = IFNULL(word_cnt,0) + #{wordCnt} where keyword = #{keyword} 
	</update>
      
</mapper>
