<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.SubscribeResultMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.SubscribeResult">
		<id column="data_id" property="dataId"/>
		<result column="status" property="status"/>
		<result column="job_name" property="jobName"/>
		<result column="batch_dt" property="batchDt"/>
		<result column="w_pdate_dt" property="wPdateDt"/>
		<result column="w_insert_dt" property="wInsertDt"/>
		
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="subscribe_id" property="subscribeId"/>
        <result column="mention" property="mention"/>
        <result column="mention_cycle" property="mentionCycle"/>
        <result column="experience" property="experience"/>
        <result column="experience_cycle" property="experienceCycle"/>
        <result column="top_list" property="topList"/>
        
        <result column="neg_mention_rate" property="negMentionRate"/>
        <result column="neg_mention_rate_cycle" property="negMentionRateCycle"/>
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		SELECT data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, status, start_date, end_date, 
			subscribe_id, mention, mention_cycle, experience, experience_cycle, top_list
		FROM dim_voc3_m_subscribe_result t
		where t.status = 1
	</sql>
	<!-- selectByDataId-->
	<select id="selectByDataId" parameterType="String"  resultMap="BaseResultMap">
		<include refid="Base_Column_List"/>
		and t.data_id = #{dataId}
	</select>
	<!-- selectSubscribeResult-->
	<select id="selectSubscribeResult" resultMap="BaseResultMap">
		<include refid="Base_Column_List"/>
		and t.subscribe_id = #{subscribeId}
		and t.start_date = #{startDate}
		and t.end_date = #{endDate}
	</select>
	<!-- updSubscribeResult-->
	<update id="updSubscribeResult" parameterType="com.meicloud.voc.car.entity.SubscribeResult">
 		update dim_voc3_m_subscribe_result
 		<set>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
 			<if test="batchDt != null ">batch_dt = #{batchDt},</if>
 			
 			<if test="startDate != null">start_date = #{startDate},</if>
 			<if test="endDate != null">end_date = #{endDate},</if>
 			<if test="subscribeId != null and subscribeId != ''">subscribe_id = #{subscribeId},</if>
 			<if test="mention != null and mention != ''">mention = #{mention},</if>
 			<if test="mentionCycle != null and mentionCycle != ''">mention_cycle = #{mentionCycle},</if>
 			<if test="experience != null and experience != ''">experience = #{experience},</if>
 			<if test="experienceCycle != null and experienceCycle != ''">experience_cycle = #{experienceCycle},</if>
 			<if test="topList != null and topList != ''">top_list = #{topList},</if>
 			<if test="negMentionRate != null">neg_mention_rate = #{negMentionRate},</if>
 			<if test="negMentionRateCycle != null">neg_mention_rate_cycle = #{negMentionRateCycle},</if>
 			w_pdate_dt = sysdate()
 		</set>
 		where data_id = #{dataId}
	</update>
	
	
</mapper>
