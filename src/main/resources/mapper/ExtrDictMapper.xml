<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.data.mapper.ExtrDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.data.entity.ExtrDict">
        <result column="id" property="id"/>
        <result column="domain" property="domain"/>
        <result column="feature" property="feature"/>
        <result column="feature_type" property="featureType"/>
        <result column="standard_keyword_id" property="standardKeywordId"/>
        <result column="standard_keyword" property="standardKeyword"/>
        <result column="dimension_id" property="dimensionId"/>
        <result column="dimension" property="dimension"/>
        <result column="attribute_id" property="attributeId"/>
        <result column="attribute" property="attribute"/>
        <result column="property_id" property="propertyId"/>
        <result column="property" property="property"/>
        <result column="sub_property_id" property="subPropertyId"/>
        <result column="sub_property" property="subProperty"/>
        <result column="source_phrase" property="sourcePhrase"/>
    </resultMap>

    <select id="count" resultType="java.lang.Integer">
        select count(*) from v_extr_dict
    </select>

    <select id="list" resultMap="BaseResultMap">
        SELECT *
        from v_extr_dict
        order by id asc limit ${offset}, ${size}
    </select>


</mapper>
