<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.AgeGroupMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.AgeGroup">
		<id column="data_id" property="dataId" />
		<result column="group_code" property="groupCode" />
		<result column="group_name" property="groupName" />
		<result column="start" property="start" />
		<result column="end" property="end" />
		
		<result column="order_seq" property="orderSeq" />
		<result column="create_by" property="createBy" />
		<result column="create_time" property="createTime" />
		<result column="update_by" property="updateBy" />
		<result column="update_time" property="updateTime" />
		<result column="status" property="status" />
	</resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, group_code, group_name, `start`, `end`, order_seq, create_by, 
			create_time, update_by, update_time, status
	</sql>
	<!-- getAgeGroups -->
	<select id="getAgeGroups" resultMap="BaseResultMap">
		SELECT data_id, group_code, group_name, `start`, `end`, status
			FROM dim_voc3_age_group age
			where age.status = 1
		<if test="groupIds != null and groupIds.size() > 0">
			and age.data_id in
			<foreach collection="groupIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="groupName != null">
			and age.group_name like CONCAT("%",#{groupName},"%")
		</if>
		order by order_seq
	</select>

</mapper>
