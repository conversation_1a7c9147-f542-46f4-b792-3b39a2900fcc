<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.category.mapper.GroupCategoryDetailMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.category.entity.GroupCategoryDetail">
		<id column="group_id" property="groupId" />
		<result column="id1" property="id1" />
		<result column="ec_id" property="ecId" />
		<result column="update_time" property="updateTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		ec_id, group_id, id1, update_time
	</sql>

</mapper>
