<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.user.mapper.UserMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.user.entity.User">
		<!-- userId -->
		<id column="user_id" property="userId" />
		<!-- companyId -->
		<result column="company_id" property="companyId" />
		<!-- userAccount -->
		<result column="user_account" property="userAccount" />
		<!-- userName -->
		<result column="user_name" property="userName" />
		<!-- interpriceUserId -->
		<result column="interprice_user_id" property="interpriceUserId" />
		<!-- deptCode -->
		<result column="dept_code" property="deptCode" />
		<!-- deptName -->
		<result column="dept_name" property="deptName" />
		<!-- positionName -->
		<result column="position_name" property="positionName" />
		<!-- isadmin -->
		<result column="isadmin" property="isadmin" />
		<!-- phone -->
		<result column="phone" property="phone" />
		<!-- email -->
		<result column="email" property="email" />
		<!-- isEnabled -->
		<result column="is_enabled" property="isEnabled" />
		<!-- isNewTab -->
		<result column="is_new_tab" property="isNewTab" />
		<!-- createDate -->
		<result column="create_date" property="createDate" />
		<!-- lastUpdateDate -->
		<result column="last_update_date" property="lastUpdateDate" />
		<!-- defaultBrand1 -->
		<result column="default_brand1" property="defaultBrand1" />
		<!-- defaultBrand2 -->
		<result column="default_brand2" property="defaultBrand2" />
		<!-- joinflow -->
		<result column="joinflow" property="joinflow" />
		<!-- flowJudged -->
		<result column="flow_judged" property="flowJudged" />
		<!-- startFlow -->
		<result column="start_flow" property="startFlow" />
		<!-- brands -->
		<result column="brands" property="brands" />
		<!-- editSeries -->
		<result column="edit_series" property="editSeries" />
		<!-- validEndDate -->
		<result column="valid_end_date" property="validEndDate" />
		<!-- userType -->
		<result column="user_type" property="userType" />
	</resultMap>

	<resultMap id="VoResultMap"
		type="com.meicloud.voc.security.api.vo.UserInfoVo">
		<id column="user_id" property="userId" />
		<result column="user_account" property="userAccount" />
		<result column="user_name" property="userName" />
	</resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		user_id, company_id, user_account, user_name,
		interprice_user_id, dept_code, dept_name, position_name,
		isadmin,
		phone, email, is_enabled,
		is_new_tab, create_date,
		last_update_date,
		default_brand1, default_brand2, joinflow,
		flow_judged, start_flow,
		edit_series, valid_end_date,
		user_type,
		is_download
	</sql>
	<!-- 查询映射结果1 -->
	<select id="getByAccountAndAuthUrl" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"></include>
		from
			t_user a
			left join t_sys_company b on a.company_id = b.id
		where
			a.user_account = #{userAccount}
		limit 1
	</select>

	<!-- 查询映射结果2 -->
	<select id="selectByCondition" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"></include>
		from
		t_user
		where (user_name like CONCAT("%",#{searchKey},"%") or
		user_account like
		CONCAT("%",#{searchKey},"%")) and
		company_id =
		#{companyId} and user_type = #{userType}
		order by create_date desc
	</select>

	<!-- 查询映射结果3 -->
	<select id="selectByGroup" resultMap="BaseResultMap">
		select
		b.*
		from t_sys_user_group a
		left join t_user b on a.user_account = b.user_account
		where a.group_id= #{groupId};
	</select>
	<!-- 查询映射结果4 -->
	<select id="selectByCompanyDept" resultMap="BaseResultMap">
		SELECT * FROM t_user t1
		where t1.is_enabled = 1
		<if test="companyId != null and companyId != '' ">
			and t1.company_id= #{companyId}
		</if>
		<if test="deptCode != null and deptCode != '' ">
			and t1.dept_code= #{deptCode}
		</if>
	</select>
	<!-- 查询映射结果5 -->
	<select id="getUserList" resultMap="VoResultMap">
		SELECT user_id,user_account,user_name 
		FROM t_user t1
		where t1.is_enabled = 1
		<if test="userAccount != null and userAccount != '' ">
			and t1.user_account= #{userAccount}
		</if>
		<if test="userName != null and userName != '' ">
			and t1.user_name like CONCAT("%",#{userName},"%")
		</if>
		limit ${startRow},${pageSize}
	</select>
	<!-- 查询映射结果6 -->
	<select id="getUserListTotal" resultType="Integer">
		SELECT count(*)
		FROM t_user t1
		where t1.is_enabled = 1
		<if test="userAccount != null and userAccount != '' ">
			and t1.user_account= #{userAccount}
		</if>
		<if test="userName != null and userName != '' ">
			and t1.user_name like CONCAT("%",#{userName},"%")
		</if>
	</select>
	
	<!-- 修改用户状态 -->
	<update id="updStatus">
		update t_user set is_enabled = #{status} where user_account = #{userAccount} 
	</update>

	<!-- 修改星谋云用户状态 -->
	<update id="updXmyStatus">
		update db_meicloud_xmy_base.t_user set is_enabled = #{status} where user_account = #{userAccount}
	</update>
</mapper>
