<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.keyword.mapper.IKeywordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.keyword.entity.Keyword">
        <id column="data_id" property="dataId"/>
        <result column="keyword" property="keyword"/>
        <result column="sentence" property="sentence"/>
        <result column="source" property="source"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="standard_keyword_id" property="standardKeywordId"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        <result column="last_modifier_id" property="lastModifierId"/>
        <result column="last_modifier_name" property="lastModifierName"/>
    </resultMap>

    <!-- findKeywordByParamsfindKeywordByParamsfindKeywordByParamsfindKeywordByParams -->
    <select id="findKeywordByParams"
            resultType="com.meicloud.voc.manage.keyword.entity.KeywordFullDetail"
            parameterType="com.meicloud.voc.manage.keyword.dto.KeywordSearchParamsDto">
        <!--
        SELECT
        kw.data_id AS data_id,
        kw.keyword AS keyword,
        kw.sentence AS sentence,
        kw.source AS `source`,
        kw.last_modifier_id AS last_modifier_id,
        sk.item_name as item_name,
        sk.parent_name_path as parent_name_path,
        sk.parent_id_path as parent_id_path
        FROM
        (
        SELECT
        data_id,
        keyword,
        sentence,
        `source`,
        last_modifier_id,
        last_modifier_name,
        standard_keyword_id,
        w_pdate_dt
        FROM
        dim_voc3_m_keyword kywd
        WHERE
        -->
        <!-- SELECT
        kw.data_id AS data_id,
        kw.keyword AS keyword,
        kw.sentence AS sentence,
        kw.source AS `source`,
        kw.last_modifier_id AS last_modifier_id,
        kw.last_modifier_name AS last_modifier_name,
        kw.standard_keyword_id AS standard_keyword_id,
        kw.w_pdate_dt AS w_pdate_dt,
        sk.name as standard_keyword_name,
        sk.emotion_attribute as emotion_attribute,
        sk.clarity as clarity
        FROM
        (
        SELECT
        data_id,
        keyword,
        sentence,
        `source`,
        last_modifier_id,
        last_modifier_name,
        standard_keyword_id,
        w_pdate_dt
        FROM
        dim_voc3_m_keyword kywd
        WHERE
        1 = 1
        AND kywd.status = '1'
        <if test="searchParamsDto.keyword != null and searchParamsDto.keyword != ''">
            AND kywd.keyword LIKE concat('%', #{searchParamsDto.keyword},'%')
        </if>
        <if test = "searchParamsDto.lastModifierId != null and searchParamsDto.lastModifierId != ''">
            AND kywd.last_modifier_id = #{searchParamsDto.lastModifierId}
        </if>
        <if test = "searchParamsDto.startDate != null and searchParamsDto.startDate != ''">
            AND w_pdate_dt >= #{searchParamsDto.startDate}
        </if>
        <if test = "searchParamsDto.endDate != null and searchParamsDto.endDate != ''">
            <![CDATA[AND w_pdate_dt <= #{searchParamsDto.endDate}]]>
        </if>
        ) AS kw
        INNER JOIN (
        select
        stkw.data_id as data_id,
        stkw.name as `name`,
        stkw.emotion_attribute as emotion_attribute,
        stkw.clarity as clarity
        from
        dim_voc3_m_standard_keyword_index_item kwin
        inner join (
            select
            data_id,
            `name`,
            emotion_attribute,
            clarity
            from
            dim_voc3_m_standard_keyword
            where
            1 = 1
            AND status = '1'
            <if test = "searchParamsDto.standardKeyword != null and searchParamsDto.standardKeyword != ''">
                AND `name` LIKE concat('%', #{searchParamsDto.standardKeyword}, '%')
            </if>
            <if test = "searchParamsDto.emotionAttribute != null and searchParamsDto.emotionAttribute != ''">
                AND emotion_attribute = #{searchParamsDto.emotionAttribute}
            </if>
            <if test = "searchParamsDto.clarity != null and searchParamsDto.clarity != ''">
                AND clarity = #{searchParamsDto.clarity}
            </if>
        ) stkw on stkw.data_id = kwin.standard_keyword_id
        inner join
        (
            select
            data_id
            from
            dim_voc3_m_index_system_item
            where
            1 = 1
            AND status = '1'
            <if test="searchParamsDto.indexType != null and searchParamsDto.indexType != ''">
                AND index_id = #{searchParamsDto.indexType}
            </if>
            <if test = "searchParamsDto.parentIdPath != null and searchParamsDto.parentIdPath != ''">
                AND parent_id_path LIKE concat(#{searchParamsDto.parentIdPath}, '%')
            </if>
            <if test = "searchParamsDto.fourIndexId != null and searchParamsDto.fourIndexId != ''">
                AND item_id = #{searchParamsDto.fourIndexId}
            </if>
        ) insi on kwin.item_data_id = insi.data_id
        group by stkw.data_id
        ) as sk on kw.standard_keyword_id = sk.data_id -->
        <!-- ORDER BY kw.w_pdate_dt DESC -->
        SELECT
        kywd.data_id AS data_id,
        kywd.keyword AS keyword,
        kywd.sentence AS sentence,
        kywd.source AS `source`,
        kywd.last_modifier_id AS last_modifier_id,
        kywd.last_modifier_name AS last_modifier_name,
        kywd.standard_keyword_id AS standard_keyword_id,
        kywd.w_pdate_dt AS w_pdate_dt,
        st.standard_keyword_name as standard_keyword_name,
        st.emotion_attribute as emotion_attribute,
        st.clarity as clarity
        FROM
        dim_voc3_m_keyword kywd
        INNER JOIN (select
        stkw.data_id as standard_keyword_id,
        stkw.name as standard_keyword_name,
        stkw.emotion_attribute as emotion_attribute,
        stkw.clarity as clarity from dim_voc3_m_standard_keyword_index_item kwin
        INNER JOIN dim_voc3_m_standard_keyword stkw ON kwin.standard_keyword_id = stkw.data_id
        INNER JOIN dim_voc3_m_index_system_item insi ON kwin.item_data_id = insi.data_id
        WHERE
        stkw.status = '1'
        AND insi.status = '1'
        <if test = "searchParamsDto.standardKeyword != null and searchParamsDto.standardKeyword != ''">
            AND stkw.name LIKE concat('%', #{searchParamsDto.standardKeyword}, '%')
        </if>
        <if test = "searchParamsDto.emotionAttribute != null and searchParamsDto.emotionAttribute != ''">
            AND stkw.emotion_attribute = #{searchParamsDto.emotionAttribute}
        </if>
        <if test = "searchParamsDto.clarity != null and searchParamsDto.clarity != ''">
            AND stkw.clarity = #{searchParamsDto.clarity}
        </if>
        <if test="searchParamsDto.indexType != null and searchParamsDto.indexType != ''">
            AND insi.index_id = #{searchParamsDto.indexType}
        </if>
        <if test = "searchParamsDto.parentIdPath != null and searchParamsDto.parentIdPath != ''">
            AND insi.parent_id_path LIKE concat(#{searchParamsDto.parentIdPath}, '%')
        </if>
        <if test = "searchParamsDto.fourIndexId != null and searchParamsDto.fourIndexId != ''">
            AND insi.item_id = #{searchParamsDto.fourIndexId}
        </if>
        GROUP BY stkw.data_id) st ON kywd.standard_keyword_id = st.standard_keyword_id
        WHERE
        kywd.status = '1'
        <if test="searchParamsDto.keyword != null and searchParamsDto.keyword != ''">
            AND kywd.keyword LIKE concat('%', #{searchParamsDto.keyword},'%')
        </if>
        <if test = "searchParamsDto.lastModifierId != null and searchParamsDto.lastModifierId != ''">
            AND kywd.last_modifier_id = #{searchParamsDto.lastModifierId}
        </if>
        <if test = "searchParamsDto.startDate != null and searchParamsDto.startDate != ''">
            AND kywd.w_pdate_dt >= #{searchParamsDto.startDate}
        </if>
        <if test = "searchParamsDto.endDate != null and searchParamsDto.endDate != ''">
            <![CDATA[AND kywd.w_pdate_dt <= #{searchParamsDto.endDate}]]>
        </if>
    </select>
    <!-- queryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpusqueryManageKwByCorpus -->
    <select id="queryManageKwByCorpus"  resultType="com.meicloud.voc.manage.keyword.dto.QueryStKwByCorpusResult">
        <!--
        select
            kw.data_id keywordId,
            kw.keyword keyword,
            stkw.data_id standardKeywordId,
            stkw.name standardKeywordName,
            stkw.emotion_attribute standardKeywordType
        from dim_voc3_m_keyword kw
        left join dim_voc3_m_standard_keyword stkw
        on kw.standard_keyword_id =stkw.data_id
        where kw.keyword =#{corpus} limit 1
        -->
        select
            kw.data_id keywordId,
            kw.keyword keyword,
            stkw.data_id standardKeywordId,
            stkw.name standardKeywordName,
            stkw.emotion_attribute standardKeywordType
        from dim_voc3_m_keyword kw
        left join dim_voc3_m_standard_keyword stkw
        on kw.standard_keyword_id =stkw.data_id
        where kw.keyword =#{corpus} limit 1
    </select>

    <select id="findKeywords" resultType="com.meicloud.voc.utils.Pair">
        select
            kw.data_id as `key`,
            kw.keyword as `value`
        from dim_voc3_m_keyword kw
        where
        kw.status = '1'
        and
        keyword in
        <foreach collection="keywords" item="name" index="index" open="(" close=")" separator=",">
            #{name}
        </foreach>
    </select>

    <update id="updateStKeywordId" >
        update dim_voc3_m_keyword
        set standard_keyword_id=#{newStKeywordId}
        where standard_keyword_id in
        <foreach collection="oldStKeywordIds" item="name" index="index" open="(" close=")" separator=",">
            #{name}
        </foreach>
    </update>
    
    <select id="findKeywordList" resultMap="BaseResultMap"
            parameterType="com.meicloud.voc.manage.keyword.dto.KeywordSearchParamsDto">
        SELECT data_id, keyword, sentence, source, original_id, creator_id, creator_name, 
        	standard_keyword_id, last_modifier_id, last_modifier_name, job_name, w_insert_dt, 
        	w_pdate_dt, batch_dt, status
		FROM dim_voc3_m_keyword
        where 1=1
        <if test = "searchParamsDto.startDate != null and searchParamsDto.startDate != ''">
            AND w_pdate_dt >= #{searchParamsDto.startDate}
        </if>
        <if test = "searchParamsDto.endDate != null and searchParamsDto.endDate != ''">
            <![CDATA[AND w_pdate_dt <= #{searchParamsDto.endDate}]]>
        </if>    
    </select>
    <select id="queryKeywordUser" resultType="com.meicloud.voc.common.dto.SelectItemResult">
        SELECT  distinct last_modifier_id as id, last_modifier_name as `value`
        FROM dim_voc3_m_keyword
        where 1=1
        AND status = '1'
        AND NOT ISNULL(last_modifier_id)
        AND NOT ISNULL(last_modifier_name)
        <if test = "keyWords != null and keyWords != ''">
            AND (
                last_modifier_id like concat('%', #{keyWords}, '%')
                OR
                last_modifier_name like concat('%', #{keyWords}, '%')
            )
        </if>
        ORDER BY last_modifier_name ASC
        LIMIT 20
    </select>
    <select id="findKeywordByParamsWithoutIndex"
            resultType="com.meicloud.voc.manage.keyword.entity.KeywordFullDetail">
        SELECT
        kywd.data_id AS data_id,
        kywd.keyword AS keyword,
        kywd.sentence AS sentence,
        kywd.source AS `source`,
        kywd.last_modifier_id AS last_modifier_id,
        kywd.last_modifier_name AS last_modifier_name,
        kywd.standard_keyword_id AS standard_keyword_id,
        kywd.w_pdate_dt AS w_pdate_dt,
        stkw.name as standard_keyword_name,
        stkw.emotion_attribute as emotion_attribute,
        stkw.clarity as clarity
        FROM
        dim_voc3_m_keyword kywd
        INNER JOIN
        dim_voc3_m_standard_keyword stkw
        ON kywd.standard_keyword_id = stkw.data_id
        WHERE
        kywd.status = '1'
        AND stkw.status = '1'
        <if test="searchParamsDto.keyword != null and searchParamsDto.keyword != ''">
            AND kywd.keyword LIKE concat('%', #{searchParamsDto.keyword},'%')
        </if>
        <if test = "searchParamsDto.lastModifierId != null and searchParamsDto.lastModifierId != ''">
            AND kywd.last_modifier_id = #{searchParamsDto.lastModifierId}
        </if>
        <if test = "searchParamsDto.startDate != null and searchParamsDto.startDate != ''">
            AND kywd.w_pdate_dt >= #{searchParamsDto.startDate}
        </if>
        <if test = "searchParamsDto.endDate != null and searchParamsDto.endDate != ''">
            <![CDATA[AND kywd.w_pdate_dt <= #{searchParamsDto.endDate}]]>
        </if>
        <if test = "searchParamsDto.standardKeyword != null and searchParamsDto.standardKeyword != ''">
            AND stkw.name LIKE concat('%', #{searchParamsDto.standardKeyword}, '%')
        </if>
        <if test = "searchParamsDto.emotionAttribute != null and searchParamsDto.emotionAttribute != ''">
            AND stkw.emotion_attribute = #{searchParamsDto.emotionAttribute}
        </if>
        <if test = "searchParamsDto.clarity != null and searchParamsDto.clarity != ''">
            AND stkw.clarity = #{searchParamsDto.clarity}
        </if>
    </select>

</mapper>
