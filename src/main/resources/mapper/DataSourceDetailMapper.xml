<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.datasourcedetail.mapper.IDataSourceDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.datasourcedetail.entity.DataSourceDetail">
        <id column="data_id" property="dataId"/>
        <result column="data_source_id" property="dataSourceId"/>
        <result column="text_total" property="textTotal"/>
        <result column="extract_total" property="extractTotal"/>
        <result column="view_rate" property="viewRate"/>
        <result column="day_total" property="dayTotal"/>
        <result column="mouth_total" property="mouthTotal"/>
        <!-- half_year_total -->
        <result column="half_year_total" property="halfYearTotal"/>
        <result column="year_total" property="yearTotal"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <!-- w_pdate_dt -->
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
    </resultMap>
    <!-- getFullDataSourceDetail -->
    <select id="getFullDataSourceDetail"
            resultType="com.meicloud.voc.manage.datasourcedetail.entity.FullDataSourceDetail">
        <!-- SELECT
            ds.data_id AS dataSourceId,
            ds.data_source as dataSource,
            ds.data_describe AS `describe`,
            ds.is_outer as isOuter,
            mdsd.text_total as textTotal,
            mdsd.extract_total as extractTotal,
            mdsd.view_rate as viewRate,
            mdsd.day_total as dayTotal,
            mdsd.mouth_total as mouthTotal,
            mdsd.half_year_total as halfYearTotal,
            mdsd.year_total as yearTotal
        FROM
            (
                SELECT
                    *
                FROM
                    dim_voc3_data_source
                WHERE-->
        SELECT
        ds.data_id AS dataSourceId,
        ds.data_source as dataSource,
        ds.data_describe AS `describe`,
        ds.source_type as sourceType,
        ds.is_outer as isOuter,
        mdsd.total_text_count as textTotal,
        mdsd.total_point_count as extractTotal,
        mdsd.total_text_with_point_count as totalTextWithPointCount,
        mdsd.total_text_with_point_count / mdsd.total_text_count as viewRate,
        mdsd.yesterday_data_count as dayTotal,
        mdsd.last_30_days_data_count as monthTotal,
        mdsd.last_6_months_data_count as halfYearTotal,
        mdsd.last_12_months_data_count as yearTotal,
        w_insert_dt
        FROM
        (
        SELECT
        data_id,
        data_source,
        data_describe,
        source_type,
        is_outer,
        sort_number
        FROM
        dim_voc3_data_source
        WHERE
        `status` = '1'
        ) ds
        LEFT JOIN (
        select
        dvdss.data_source_name as data_source_name,
        total_text_count,
        total_text_with_point_count,
        total_point_count,
        text_count as yesterday_data_count,
        last_30_days_data_count,
        last_6_months_data_count,
        last_12_months_data_count,
        dvdss.w_insert_dt as w_insert_dt
        from dm_voc3_data_source_summary dvdss
        where TO_DAYS(w_insert_dt)  = TO_DAYS(now()) - 1
        ) mdsd ON ds.data_source = mdsd.data_source_name
        order by sort_number asc
    </select>
</mapper>
