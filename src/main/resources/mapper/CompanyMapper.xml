<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.company.mapper.CompanyMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.company.entity.Company">
		<id column="id" property="id" />
		<result column="company_name" property="companyName" />
		<result column="validity_date" property="validityDate" />
		<result column="type" property="type" />
		<result column="auth_url" property="authUrl" />
		<result column="state" property="state" />
		<result column="logo" property="logo" />
		<result column="update_time" property="updateTime" />
		<result column="update_user" property="updateUser" />
		<result column="data_start_time" property="dataStartTime" />
		<result column="user_limit" property="userLimit" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, company_name, validity_date, type, auth_url, state,
		logo,
		update_time, update_user, data_start_time, user_limit
	</sql>
	<!-- selectByCondition -->
	<select id="selectByCondition" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"></include>
		from
		t_sys_company
		where
		1 = 1
		<if test="searchKey != '' and searchKey != null">
			and company_name like CONCAT("%",#{searchKey},"%")
		</if>
	</select>
</mapper>

