<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.user.mapper.UserGroupMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.user.entity.UserGroup">
		<id column="id" property="id" />
		<result column="company_id" property="companyId" />
		<result column="user_account" property="userAccount" />
		<result column="group_id" property="groupId" />
		<result column="update_time" property="updateTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, company_id, user_id, user_account, group_id, update_time
	</sql>
    <update id="updateGroupIdByUserAccount">
		update t_sys_user_group set group_id = #{groupId} where user_account = #{userAccount}
	</update>
    <!-- 查询映射结果1 -->
	<delete id="deleteByGroupUserId">
		delete from t_sys_user_group where user_id = #{userId} and group_id = #{roleId}
 	</delete>
	<!-- 通过用户账号和角色id删除角色 -->
	<delete id="deleteByGroupUserAccount">
		delete from t_sys_user_group where user_account = #{userAccount} and group_id = #{roleId}
 	</delete>
	<!-- 查询映射结果2 -->
 	<delete id="deleteByGroupRoleId">
		delete from t_sys_user_group where group_id = #{roleId}
 	</delete>
 	<!-- 删除账号的权限 -->
	<delete id="deleteByUserAccount">
		delete from t_sys_user_group where user_account = #{userAccount}
 	</delete>
	<select id="getUserRoleByUserAccount" resultType="com.meicloud.voc.user.entity.UserGroup">
		SELECT
			<include refid="Base_Column_List"></include>
		FROM t_sys_user_group
			WHERE user_account = #{userAccount}
	</select>


</mapper>
