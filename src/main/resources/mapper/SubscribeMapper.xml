<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.SubscribeMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.Subscribe">
		<id column="data_id" property="dataId"/>
		<result column="status" property="status"/>
		<result column="job_name" property="jobName"/>
		<result column="batch_dt" property="batchDt"/>
		<result column="w_pdate_dt" property="wPdateDt"/>
		<result column="w_insert_dt" property="wInsertDt"/>
		
        <result column="index_id" property="indexId"/>
        <result column="index_name" property="indexName"/>
        <result column="first_index_id" property="firstIndexId"/>
        <result column="second_index_id" property="secondIndexId"/>
        <result column="third_index_id" property="thirdIndexId"/>
        <result column="four_index_id" property="fourIndexId"/>
        <result column="standard_keyword" property="standardKeyword"/> 
        <result column="standard_keyword_id" property="standardKeywordId"/> 
        
        <result column="liable_dept_id" property="liableDeptId"/>
        <result column="liable_dept" property="liableDept"/>
        <result column="liable_user_id" property="liableUserId"/>
        <result column="liable_user" property="liableUser"/>
        
        <result column="emotion_attribute" property="emotionAttribute"/>
        <result column="clarity" property="clarity"/>
        <result column="keyword_source" property="keywordSource"/>
        <result column="domain" property="domain"/>
        <result column="is_webchat" property="isWebchat"/>
        <result column="date_type" property="dateType"/>
        <result column="date" property="date"/>
        <result column="title" property="title"/>
        <result column="push_time" property="pushTime"/>
        
        <result column="market_names" property="marketNames"/>
        <result column="brand_names" property="brandNames"/>
        <result column="series_names" property="seriesNames"/>
        <result column="model_names" property="modelNames"/>
        <result column="measure" property="measure"/>
        
        <result column="data_sources" property="dataSources"/>
        <result column="car_owner" property="carOwner"/>
        <result column="province" property="province"/>
        <result column="is_own" property="isOwn"/>
        <result column="subscribe_type" property="subscribeType"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        
	</resultMap>
	
	<!-- 通用查询映射结果 -->
	<resultMap id="VOResultMap"
		type="com.meicloud.voc.car.vo.SubscribeVO">
		<id column="data_id" property="dataId"/>
		<result column="status" property="status"/>
		<result column="job_name" property="jobName"/>
		<result column="batch_dt" property="batchDt"/>
		<result column="w_pdate_dt" property="wPdateDt"/>
		<result column="w_insert_dt" property="wInsertDt"/>
		
        <result column="index_id" property="indexType"/>
        <result column="index_name" property="indexTypeName"/>
        <result column="first_index_id" property="firstIndexId"/>
        <result column="second_index_id" property="secondIndexId"/>
        <result column="third_index_id" property="thirdIndexId"/>
        <result column="four_index_id" property="fourIndexId"/>
        <result column="standard_keyword" property="names"/> 
        <result column="standard_keyword_id" property="standardKeywordId"/> 
        
        <result column="liable_dept_id" property="liableDeptId"/>
        <result column="liable_dept" property="department"/>
        <result column="liable_user_id" property="chargeId"/>
        <result column="liable_user" property="charge"/>
        
        <result column="emotion_attribute" property="emotionAttribute"/>
        <result column="keyword_source" property="keywordSource"/>
        <result column="clarity" property="clarity"/>
        <result column="domain" property="field"/>
        <result column="is_webchat" property="isWebchat"/>
        <result column="date_type" property="dateType"/>
        <result column="date" property="date"/>
        <result column="title" property="title"/>
        <result column="push_time" property="pushTime"/>
        
        <result column="market_names" property="marketNames"/>
        <result column="brand_names" property="brandNames"/>
        <result column="series_names" property="seriesNames"/>
        <result column="model_names" property="modelNames"/>
        <result column="measure" property="measureIndex"/>
        
        <result column="data_sources" property="dataSources"/>
        <result column="car_owner" property="carOwner"/>
        <result column="province" property="province"/>
        <result column="is_own" property="isOwn"/>
        <result column="subscribe_type" property="subscribeType"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        
        <result column="result_id" property="resultId"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="mention" property="totalMentionValue"/>
        <result column="mention_cycle" property="momTotalMentionValueRate"/>
        <result column="experience" property="experienceValue"/>
        <result column="experience_cycle" property="momExperienceValueRate"/>

        <result column="neg_mention_rate" property="negativeMentionRate"/>
        <result column="neg_mention_rate_cycle" property="momNegativeMentionRate"/>
        
        <result column="top_list" property="topListStr"/>
        <result column="ktm" property="ktm"/>
        <result column="push_date" property="pushDate"/>
        
	</resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		SELECT liable_user, data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, status, index_id, index_name,
		first_index_id, second_index_id, third_index_id, four_index_id, standard_keyword, liable_dept_id, liable_dept, liable_user_id, 
		emotion_attribute, keyword_source, clarity, `domain`, is_webchat, date_type, `date`, title, market_names, brand_names, 
		series_names, model_names, measure, data_sources, car_owner, province, is_own, subscribe_type,push_time,
		create_id, create_name
		FROM dim_voc3_m_subscribe t
		where t.status = 1
	</sql>
	<!-- 查询映射结果1 -->
	<select id="selectByDataId" parameterType="String"  resultMap="BaseResultMap">
		<include refid="Base_Column_List"/>
		and t.data_id = #{dataId}
	</select>
	<!-- 查询映射结果2 -->
	<select id="getSubscribeList" resultMap="BaseResultMap" parameterType="com.meicloud.voc.car.dto.SubscribeParams">
		SELECT liable_user, data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, status, index_id, index_name,
			first_index_id, second_index_id, third_index_id, four_index_id, standard_keyword, standard_keyword_id, 
			liable_dept_id, liable_dept, liable_user_id, 
			emotion_attribute, keyword_source, clarity, `domain`, is_webchat, date_type, `date`, title, market_names, brand_names, 
			series_names, model_names, measure, data_sources, car_owner, province, is_own, subscribe_type,push_time,
			create_id, create_name
		FROM dim_voc3_m_subscribe t
		where t.status = 1
		<if test="dateType != null">
			and t.date_type = #{dateType}
		</if>
		<if test="day != null">
			and t.date = #{day}
		</if>
		<if test="pushTime != null">
			and t.push_time = #{pushTime}
		</if>
		<if test="createId != null">
			and t.create_id = #{createId}
		</if>
		order by t.w_insert_dt desc
	</select>
	<!-- 查询映射结果3 -->
	<select id="getSubscribePage" resultMap="BaseResultMap" parameterType="com.meicloud.voc.car.dto.SubscribeParams">
		SELECT liable_user, data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, status, index_id, index_name,
			first_index_id, second_index_id, third_index_id, four_index_id, standard_keyword, standard_keyword_id, 
			liable_dept_id, liable_dept, liable_user_id, 
			emotion_attribute, keyword_source, clarity, `domain`, is_webchat, date_type, `date`, title, market_names, brand_names, 
			series_names, model_names, measure, data_sources, car_owner, province, is_own, subscribe_type,push_time,
			create_id, create_name
		FROM dim_voc3_m_subscribe t
		where t.status = 1
		<if test="params.dateType != null">
			and t.date_type = #{params.dateType}
		</if>
		<if test="params.day != null">
			and t.date = #{params.day}
		</if>
		<if test="params.pushTime != null">
			and t.push_time = #{params.pushTime}
		</if>
		<if test="params.createId != null">
			and t.create_id = #{params.createId}
		</if>
		order by t.w_insert_dt desc
	</select>
	<!-- 查询映射结果4 -->
	<select id="getSubscribeResultList" resultMap="VOResultMap" parameterType="com.meicloud.voc.car.dto.SubscribeParams">
		SELECT liable_user, t.data_id, t.w_pdate_dt, t.w_insert_dt, t.job_name, t.batch_dt, t.status, index_id, index_name,
			first_index_id, second_index_id, third_index_id, four_index_id, standard_keyword, standard_keyword_id, 
			liable_dept_id, liable_dept, liable_user_id, 
			emotion_attribute, keyword_source, clarity, `domain`, is_webchat, date_type, `date`, title, market_names, brand_names, 
			series_names, model_names, measure, data_sources, car_owner, province, is_own, subscribe_type,push_time,
			create_id, create_name,
			result.data_id as result_id, result.start_date,result.end_date,result.mention,result.mention_cycle, 
			result.neg_mention_rate, result.neg_mention_rate_cycle, 
			date_format(result.w_insert_dt,'%Y-%m-%d') as push_date,
			result.experience,result.experience_cycle,result.top_list,
			(select count(*) from dim_voc3_m_task where subscribe_id = result.data_id and status = '1') as ktm
		FROM dim_voc3_m_subscribe t 
		left join dim_voc3_m_subscribe_result result on result.subscribe_id = t.data_id
		where t.status = 1
		<if test="subscribeType != null">
			and t.subscribe_type = #{subscribeType}
		</if>
		<if test="createId != null">
			and t.create_id = #{createId}              
		</if>
		<if test="createName != null">
			and t.create_name like CONCAT("%",#{createName},"%")
		</if>
		
		<if test="startDate != null">
			and result.w_insert_dt >= #{startDate}
		</if>
		<if test="endDate != null">
			<![CDATA[ 
				and result.w_insert_dt < #{endDate}
			]]>
		</if>
		<if test="title != null">
			and t.title like CONCAT("%",#{title},"%")
		</if>
		<if test="dateType != null">
			and t.date_type = #{dateType}
		</if>
		<if test="params.orders != null and params.orders.size() > 0">
			order by 
        	<foreach collection="params.orders" item="name" index="index" separator=",">
				${name.property} ${name.direction}
			</foreach>
        </if>
	</select>
	<select id="getSubscribeResultPage" resultMap="VOResultMap" parameterType="com.meicloud.voc.car.dto.SubscribeParams">
		SELECT liable_user, t.data_id, t.w_pdate_dt, t.w_insert_dt, t.job_name, t.batch_dt, t.status, index_id, index_name,
			first_index_id, second_index_id, third_index_id, four_index_id, standard_keyword, standard_keyword_id, 
			liable_dept_id, liable_dept, liable_user_id, 
			emotion_attribute, keyword_source, clarity, `domain`, is_webchat, date_type, `date`, title, market_names, brand_names, 
			series_names, model_names, measure, data_sources, car_owner, province, is_own, subscribe_type,push_time,
			create_id, create_name,
			result.data_id as result_id, result.start_date,result.end_date,result.mention,result.mention_cycle, 
			result.neg_mention_rate, result.neg_mention_rate_cycle, 
			date_format(result.w_insert_dt,'%Y-%m-%d') as push_date,
			result.experience,result.experience_cycle,result.top_list,
			(select count(*) from dim_voc3_m_task where subscribe_id = result.data_id) as ktm
		FROM dim_voc3_m_subscribe t 
		left join dim_voc3_m_subscribe_result result on result.subscribe_id = t.data_id
		where t.status = 1
		<if test="params.subscribeType != null">
			and t.subscribe_type = #{params.subscribeType}
		</if>
		<if test="params.createId != null">
			and t.create_id = #{params.createId}              
		</if>
		<if test="params.createName != null">
			and t.create_name like CONCAT("%",#{params.createName},"%")
		</if>
		
		<if test="params.startDate != null">
			and result.w_insert_dt >= #{params.startDate}
		</if>
		<if test="params.endDate != null">
			<![CDATA[ 
				and result.w_insert_dt < #{params.endDate}
			]]>
		</if>
		<if test="params.title != null">
			and t.title like CONCAT("%",#{params.title},"%")
		</if>
		<if test="params.dateType != null">
			and t.date_type = #{params.dateType}
		</if>
	</select>
	<!-- 查询映射结果15-->
	<update id="updSubscribe" parameterType="com.meicloud.voc.car.entity.Subscribe">
 		update dim_voc3_m_subscribe
 		<set>
 			<if test="indexId != null and indexId != ''">index_id = #{indexId},</if>
 			<if test="indexName != null">index_name = #{indexName},</if>
 			<if test="firstIndexId != null">first_index_id = #{firstIndexId},</if>
 			<if test="secondIndexId != null">second_index_id = #{secondIndexId},</if>
 			<if test="thirdIndexId != null">third_index_id = #{thirdIndexId},</if>
 			<if test="fourIndexId != null">four_index_id = #{fourIndexId},</if>
 			<if test="liableDeptId != null">liable_dept_id = #{liableDeptId},</if>
 			<if test="liableDept != null">liable_dept = #{liableDept},</if>
 			<if test="liableUserId != null">liable_user_id = #{liableUserId},</if>
 			<if test="liableUser != null">liable_user = #{liableUser},</if>
 			<if test="emotionAttribute != null">emotion_attribute = #{emotionAttribute},</if>
 			<if test="clarity != null">clarity = #{clarity},</if>
 			<if test="domain != null">domain = #{domain},</if>
 			<if test="isWebchat != null">is_webchat = #{isWebchat},</if>
 			<if test="dateType != null">date_type = #{dateType},</if>
 			<if test="date != null">date = #{date},</if>
 			<if test="title != null">title = #{title},</if>
 			<if test="marketNames != null">market_names = #{marketNames},</if>
 			<if test="brandNames != null">brand_names = #{brandNames},</if>
 			
 			<if test="seriesNames != null">series_names = #{seriesNames},</if>
 			<if test="modelNames != null">model_names = #{modelNames},</if>
 			<if test="measure != null">measure = #{measure},</if>
 			<if test="dataSources != null">data_sources = #{dataSources},</if>
 			<if test="carOwner != null">car_owner = #{carOwner},</if>
 			
 			<if test="standardKeyword != null">standard_keyword = #{standardKeyword},</if>
 			<if test="standardKeywordId != null">standard_keyword_id = #{standardKeywordId},</if>
 			
 			<if test="province != null">province = #{province},</if>
 			<if test="isOwn != null">is_own = #{isOwn},</if>
 			<if test="subscribeType != null">subscribe_type = #{subscribeType},</if>
 			<if test="createId != null">create_id = #{createId},</if>
 			<if test="createName != null">create_name = #{createName},</if>
 			w_pdate_dt = sysdate()
 		</set>
 		where data_id = #{dataId}
	</update>
	<!-- 查询映射结果56 -->
	<update id="updStatus">
		update dim_voc3_m_subscribe set status = #{status} where data_id = #{id} 
	</update>
	<!-- getCreatorList -->
	<select id="getCreatorList" resultType="com.meicloud.voc.common.dto.ListVo"
		parameterType="com.meicloud.voc.common.dto.ListVo">
		<!-- select create_id as id,create_name as value
		from  dim_voc3_m_subscribe t1
		where t1.status = 1
		<if test="id != null and id != ''">
			and t1.create_id = #{id}
		</if>
		<if test="value != null and value != ''">
			and t1.create_name like CONCAT("%",#{value},"%")
		</if>
		group by t1.create_id, t1.create_name -->
		select create_id as id,create_name as value
		from  dim_voc3_m_subscribe t1
		where t1.status = 1
		<if test="id != null and id != ''">
			and t1.create_id = #{id}
		</if>
		<if test="value != null and value != ''">
			and t1.create_name like CONCAT("%",#{value},"%")
		</if>
		group by t1.create_id, t1.create_name
	</select>
	<!-- 查询映射结果7 -->
	<select id="getLiableDeptList" resultType="com.meicloud.voc.common.dto.ListVo" 
		parameterType="com.meicloud.voc.common.dto.ListVo" >
		<!-- select liable_dept_id as id,liable_dept as value
		from dim_voc3_m_subscribe t1
		where t1.status = 1
		<if test="id != null and id != ''">
			and t1.liable_dept_id = #{id}
		</if>
		<if test="value != null and value != ''">
			and t1.liable_dept like CONCAT("%",#{value},"%")
		</if>
		group by t1.liable_dept_id, t1.liable_dept-->
		select liable_dept_id as id,liable_dept as value
		from dim_voc3_m_subscribe t1
		where t1.status = 1
		<if test="id != null and id != ''">
			and t1.liable_dept_id = #{id}
		</if>
		<if test="value != null and value != ''">
			and t1.liable_dept like CONCAT("%",#{value},"%")
		</if>
		group by t1.liable_dept_id, t1.liable_dept
	</select>
	
</mapper>
