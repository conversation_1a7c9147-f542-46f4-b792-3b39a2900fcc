<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.errorcheck.mapper.IErrorCheckMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.errorcheck.entity.ErrorCheck">
        <!--  <id column="data_id" property="dataId"/>
        <result column="keyword_id" property="keywordId"/>
        <result column="keyword" property="keyword"/>
        <result column="standard_keyword_id" property="standardKeywordId"/>
        <result column="source_id" property="sourceId"/>
        <result column="error_type" property="errorType"/>
        <result column="error_describe" property="errorDescribe"/>
        <result column="corrector_id" property="correctorId"/>
        <result column="corrector_name" property="correctorName"/>
        <result column="correct_status" property="correctStatus"/>
        <result column="handle_opinions" property="handleOpinions"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/> 主键-->
        <id column="data_id" property="dataId"/>
        <result column="keyword_id" property="keywordId"/>
        <result column="keyword" property="keyword"/>
        <result column="standard_keyword_id" property="standardKeywordId"/>
        <result column="source_id" property="sourceId"/>
        <result column="error_type" property="errorType"/>
        <result column="error_describe" property="errorDescribe"/>
        <result column="corrector_id" property="correctorId"/>
        <result column="corrector_name" property="correctorName"/>
        <result column="correct_status" property="correctStatus"/>
        <result column="handle_opinions" property="handleOpinions"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        <result column="standard_keyword_name" property="standardKeywordName"/>
        <result column="standard_keyword_type" property="standardKeywordType"/>
    </resultMap>
</mapper>
