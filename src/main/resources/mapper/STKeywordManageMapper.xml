<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.stKeywordManage.mapper.STKeywordManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.stKeywordManage.entity.StandardKeywordManage">
        <id column="data_id" property="dataId"/>
        <result column="name" property="name"/>
        <result column="emotion_attribute" property="emotionAttribute"/>
        <result column="clarity" property="clarity"/>
        <result column="field" property="field"/>
        <result column="department" property="department"/>
        <result column="charge" property="charge"/>
        <result column="charge_id" property="chargeId"/>
        <result column="notes" property="notes"/>
        <result column="creater" property="creater"/>
        <result column="last_modifier" property="lastModifier"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- selectStKeywordManageDetails-->
    <select id="selectStKeywordManageDetails" resultType="com.meicloud.voc.manage.stKeywordManage.dto.StKeywordResult">
        <!-- select count(t2.data_id) keywordCount, t1.*
        from (
            select
                stkw.*,
                group_concat(ins.index_type_name,':',insi.parent_name_path) index_names
            from dim_voc3_m_standard_keyword stkw
            left join dim_voc3_m_standard_keyword_index_item kwin on stkw.data_id =kwin.standard_keyword_id
            left join dim_voc3_m_index_system_item insi on kwin.item_data_id=insi.data_id
            left join dim_voc3_m_index_system ins on insi.index_id = ins.data_id
            where stkw.status ='1'-->
        select t2.keywordCount, t1.*
        from (
        select
        stkw.*,
        group_concat(ins.index_type_name,':',insi.parent_name_path) index_names,
        group_concat(ins.data_id,':', kwin.item_data_id,':',insi.parent_id_path) index_ids
        from dim_voc3_m_standard_keyword stkw
        left join dim_voc3_m_standard_keyword_index_item kwin on stkw.data_id =kwin.standard_keyword_id
        left join dim_voc3_m_index_system_item insi on kwin.item_data_id=insi.data_id
        left join dim_voc3_m_index_system ins on insi.index_id = ins.data_id
        where stkw.status ='1'
        and insi.status = '1'
        and ins.status = '1'
        <if test="params.names != null and params.names != '' ">
            and stkw.name regexp #{params.names}
        </if>
        <if test="params.department != null and params.department != ''">
            and stkw.department = #{params.department}
        </if>
        <if test="params.charge != null and params.charge != ''">
            and stkw.charge = #{params.charge}
        </if>
        <if test="params.emotionAttribute != null and params.emotionAttribute != ''">
            and stkw.emotion_attribute = #{params.emotionAttribute}
        </if>
        <if test="params.clarity != null and params.clarity != ''">
            and stkw.clarity = #{params.clarity}
        </if>
        <if test="params.field != null and params.field != ''">
            and stkw.field = #{params.field}
        </if>
        <choose>
            <when test="params.indexReverseFilter == true" >
                and stkw.data_id not in (
                    select
                        stkw.data_id
                    from dim_voc3_m_standard_keyword stkw
                    left join dim_voc3_m_standard_keyword_index_item kwin on stkw.data_id =kwin.standard_keyword_id
                    left join dim_voc3_m_index_system_item insi on kwin.item_data_id=insi.data_id
                    where stkw.status ='1'
                    <if test="params.indexType != null and params.indexType != ''">
                        and insi.index_id = #{params.indexType}
                    </if>
                    <if test="params.parentIdPath != null and params.parentIdPath != ''">
                        and insi.parent_id_path like concat (#{params.parentIdPath},'%')
                    </if>
                    group by stkw.data_id
                )
            </when>
            <otherwise>
                <if test="params.indexType != null and params.indexType != ''">
                    and insi.index_id = #{params.indexType}
                </if>
                <if test="params.parentIdPath != null and params.parentIdPath != ''">
                    and insi.parent_id_path like concat (#{params.parentIdPath},'%')
                </if>
            </otherwise>
        </choose>
        group by stkw.data_id
        ) t1
        left join
        (select kw.standard_keyword_id, count(kw.data_id) as keywordCount
         from dim_voc3_m_keyword kw where kw.status = '1'
         group by kw.standard_keyword_id) t2
        on t1.data_id = t2.standard_keyword_id
    </select>
    <!-- stKeywordSearch-->
    <select id="stKeywordManageSearch" resultType="com.meicloud.voc.manage.stKeywordManage.dto.StKwSearchResult">
        <!-- select
            stkw.*,
            ins.index_type_name,
            insi.parent_name_path index_names -->
        select
        stkw.*,
        ins.index_type_name,
        insi.parent_name_path index_names
        <!--			concat(ins.index_type_name,'-',insi.parent_name_path) index_names-->
        from dim_voc3_m_standard_keyword stkw
        left join dim_voc3_m_standard_keyword_index_item kwin on stkw.data_id =kwin.standard_keyword_id
        left join dim_voc3_m_index_system_item insi on kwin.item_data_id=insi.data_id
        left join dim_voc3_m_index_system ins on insi.index_id = ins.data_id
        where stkw.status ='1'
        <if test="params.names != null and params.names != '' ">
            and stkw.name regexp #{params.names}
        </if>
        <if test="params.department != null and params.department != ''">
            and stkw.department = #{params.department}
        </if>
        <if test="params.charge != null and params.charge != ''">
            and stkw.charge = #{params.charge}
        </if>
        <if test="params.emotionAttribute != null and params.emotionAttribute != ''">
            and stkw.emotion_attribute = #{params.emotionAttribute}
        </if>
        <if test="params.clarity != null and params.clarity != ''">
            and stkw.clarity = #{params.clarity}
        </if>
        <if test="params.field != null and params.field != ''">
            and stkw.field = #{params.field}
        </if>
        <if test="params.indexType != null and params.indexType != ''">
            and insi.index_id = #{params.indexType}
        </if>
        <if test="params.parentIdPath != null and params.parentIdPath != ''">
            and insi.parent_id_path like concat (#{params.parentIdPath},'%')
        </if>
        group by stkw.data_id

    </select>
    <select id="findUpdateMangeKeywords" resultType="com.meicloud.voc.selfAnalysis.entity.StandardKeyword">
        select sk.data_id as standardKeywordId,
        sk.name as standardKeyword,
        sk.department as department,
        sk.department_id as departmentId,
        sk.charge as charge,
        sk.charge_id as chargeId,
        sk.emotion_attribute as emotionAttribute,
        sk.field as `field`,
        sk.clarity as clarity,
        isi.item_id as fourIndexId,
        isi.item_name as fourIndexName,
        dvmis.index_type_name as indexType
        from dim_voc3_m_standard_keyword sk
        left join dim_voc3_m_standard_keyword_index_item skit
        on sk.data_id = skit.standard_keyword_id
        left join dim_voc3_m_index_system_item isi
        on isi.data_id = skit.item_data_id
        left join dim_voc3_m_index_system dvmis on isi.index_id = dvmis.data_id
        where (
        (

        <if test="startTime != null">
             sk.w_pdate_dt >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[and sk.w_pdate_dt <= #{endTime}]]>
        </if>
        )
        or (

        <if test="startTime != null">
             isi.w_pdate_dt >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[and isi.w_pdate_dt <= #{endTime}]]>
        </if>

        )
        or (

        <if test="startTime != null">
             dvmis.w_pdate_dt >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[and dvmis.w_pdate_dt <= #{endTime}]]>
        </if>
        )
        )
    </select>
    <select id="findInsertMangeKeywords" resultType="com.meicloud.voc.selfAnalysis.entity.StandardKeyword">
        select sk.data_id as standardKeywordId,
        sk.name as standardKeyword,
        sk.department as department,
        sk.department_id as departmentId,
        sk.charge as charge,
        sk.charge_id as chargeId,
        sk.emotion_attribute as emotionAttribute,
        sk.field as `field`,
        sk.clarity as clarity,
        isi.item_id as fourIndexId,
        isi.item_name as fourIndexName,
        dvmis.index_type_name as indexType
        from dim_voc3_m_standard_keyword sk
        left join dim_voc3_m_standard_keyword_index_item skit
        on sk.data_id = skit.standard_keyword_id
        left join dim_voc3_m_index_system_item isi
        on isi.data_id = skit.item_data_id
        left join dim_voc3_m_index_system dvmis on isi.index_id = dvmis.data_id
        where
        sk.status = '1'
        and
        isi.status = '1'
        and
        dvmis.status = '1'
        <if test="standardKeywordIds != null and standardKeywordIds.size() > 0">
            and sk.data_id in
            <foreach collection="standardKeywordIds" item="standardKeywordId" open="(" separator="," close=")">
                #{standardKeywordId}
            </foreach>
        </if>

    </select>
    <select id="stKeywordManageIndexSearch"
            resultType="com.meicloud.voc.manage.stKeywordManage.dto.StKwIndexSearchResult">
        select sk.data_id as standardKeywordId,
               sk.name as standardKeyword,
               isi.index_id as indexTypeId,
               isi.data_id as indexItemId,
               isi.parent_id_path as parentIdPath,
               isi.parent_name_path as indexNamePath,
               isi.item_id as indexItemId
        from dim_voc3_m_standard_keyword sk
        left join dim_voc3_m_standard_keyword_index_item skit on sk.data_id = skit.standard_keyword_id
        left join dim_voc3_m_index_system_item isi on isi.data_id = skit.item_data_id
        where sk.status = '1'
        <if test="standardKeywordIds != null and standardKeywordIds.size > 0 ">
            and sk.data_id in
            <foreach collection="standardKeywordIds" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
    </select>
    
    <!-- getStKeywordManageList-->
    <select id="getStKeywordManageMap" resultType="StandardKeywordManage">
        select sk.data_id, sk.name, sk.department,
        sk.department_id, sk.charge, sk.charge_id,
        sk.emotion_attribute, sk.field, sk.clarity
        from dim_voc3_m_standard_keyword sk
        where sk.status = '1'
        <if test="dataList != null and dataList.size() > 0">
            and sk.data_id in
            <foreach collection="dataList" item="name" index="index" open="(" close=")" separator=",">
            	#{name}
            </foreach>
        </if>
    </select>

</mapper>
