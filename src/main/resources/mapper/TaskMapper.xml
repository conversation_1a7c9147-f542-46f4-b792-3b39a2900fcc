<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.task.mapper.ITaskMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.task.entity.Task">
        <id column="data_id" property="dataId"/>
        <result column="task_id" property="taskId"/>
        <result column="domain_id" property="domainId"/>
        <result column="task_name" property="taskName"/>
        <result column="parent_task_id" property="parentTaskId"/>
        <result column="plan_begin_date" property="planBeginDate"/>
        <result column="plan_finish_date" property="planFinishDate"/>
        <result column="duty_login_id" property="dutyLoginId"/>
        <result column="support_login_ids" property="supportLoginIds"/>
        <result column="read_login_ids" property="readLoginIds"/>
        <result column="confirm_login_id" property="confirmLoginId"/>
        <result column="assistant_login_id" property="assistantLoginId"/>
        <result column="target" property="target"/>
        <result column="delivery" property="delivery"/>
        <result column="close_condition" property="closeCondition"/>
        <result column="task_source" property="taskSource"/>
        <result column="source_code" property="sourceCode"/>
        <result column="source_type" property="sourceType"/>
        <result column="source_ref_id" property="sourceRefId"/>
        <result column="report_type_value" property="reportTypeValue"/>
        <result column="report_choice_value" property="reportChoiceValue"/>
        <result column="task_path" property="taskPath"/>
        <result column="task_request_params" property="taskRequestParams"/>
        <result column="task_status" property="taskStatus"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
    </resultMap>
    <!-- 查询映射结果1 -->
    <select id="getInChargeList" resultType="com.meicloud.voc.utils.Pair">
        <!--  SELECT DISTINCT duty_login_id AS `key`, duty_login_name AS `value`
        FROM dim_voc3_m_task
        WHERE
        `status` = '1' -->
        SELECT DISTINCT duty_login_id AS `key`, duty_login_name AS `value`
        FROM dim_voc3_m_task
        WHERE
        `status` = '1'
        <if test="userId != null">
            AND (
            allocator_id = #{userId}
            OR
            duty_login_id = #{userId}
            OR
            support_login_ids LIKE CONCAT('%', #{userId}, '%')
            OR
            read_login_ids LIKE CONCAT('%', #{userId}, '%')
            OR
            confirm_login_id = #{userId}
            )
        </if>
    </select>
    <!-- 查询映射结果2 -->
    <select id="getDepartmentList" resultType="com.meicloud.voc.utils.Pair">
        <!-- select distinct department_id as `key`, department_name as `value`
        from dim_voc3_m_task where status = '1' -->
        select distinct department_id as `key`, department_name as `value`
        from dim_voc3_m_task where status = '1'
        <if test="userId != null">
            and (
            allocator_id = #{userId}
            or duty_login_id = #{userId}
            or support_login_ids like concat('%', #{userId}, '%')
            or read_login_ids like concat('%', #{userId}, '%')
            or confirm_login_id = #{userId}
            )
        </if>
    </select>


</mapper>
