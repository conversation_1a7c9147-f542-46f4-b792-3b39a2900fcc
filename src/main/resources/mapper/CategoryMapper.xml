<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.category.mapper.CategoryMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.category.entity.Category">
		<!-- ecId -->
		<id column="ec_id" property="ecId" />
		<!-- ecName -->
		<result column="ec_name" property="ecName" />
		<!-- id1 -->
		<result column="id1" property="id1" />
		<!-- id1Name -->
		<result column="id1_name" property="id1Name" />
		<!-- id2 -->
		<result column="id2" property="id2" />
		<!-- id2Name -->
		<result column="id2_name" property="id2Name" />
		<!-- id3 -->
		<result column="id3" property="id3" />
		<!-- id3Name -->
		<result column="id3_name" property="id3Name" />
		<!-- id4 -->
		<result column="id4" property="id4" />
		<!-- id4Name -->
		<result column="id4_name" property="id4Name" />
		<!-- itemCatId -->
		<result column="item_cat_id" property="itemCatId" />
		<!-- status -->
		<result column="status" property="status" />
		<!-- createTime -->
		<result column="create_time" property="createTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		ec_id, ec_name, id1, id1_name, id2, id2_name, id3,
		id3_name, id4, id4_name,
		item_cat_id, status, create_time
	</sql>
	<!-- selectByCompanyAndUser -->
	<select id="selectByCompanyAndUser" resultMap="BaseResultMap">
		<!-- select
		b.ec_id,
		b.ec_name,
		b.id1,
		b.id1_name,
		b.id2,
		b.id2_name,
		b.id3,
		b.id3_name,
		b.id4,
		b.id4_name,
		b.item_cat_id,
		b.status,
		b.create_time -->
		select
		b.ec_id,
		b.ec_name,
		b.id1,
		b.id1_name,
		b.id2,
		b.id2_name,
		b.id3,
		b.id3_name,
		b.id4,
		b.id4_name,
		b.item_cat_id,
		b.status,
		b.create_time
		from (
		select distinct a.id1,a.ec_id from t_xmy_group_category_detail a
		left join t_sys_user_group b on a.group_id = b.group_id
		where b.company_id = #{companyId} and b.user_account = #{userAccount}) a
		left join t_xmy_category b on a.ec_id = b.ec_id and a.id1 =
		b.item_cat_id
		where 1 = 1 and b.ec_id is not null
	</select>
	<!-- selectItemCatIdByCompanyAndUser -->
	<select id="selectItemCatIdByCompanyAndUser"
		resultMap="BaseResultMap">
		select
		b.ec_id,
		b.id1 as item_cat_id
		from t_sys_user_group a
		left join t_xmy_group_category_detail b on a.group_id = b.group_id
		where a.company_id = #{companyId} and a.user_account = #{userAccount}
	</select>
	<!-- selectByGroup -->
	<select id="selectByGroup" resultMap="BaseResultMap">
		select
		c.ec_id,
		c.ec_name,
		c.id1,
		c.id1_name,
		c.id2,
		c.id2_name,
		c.id3,
		c.id3_name,
		c.id4,
		c.id4_name,
		c.item_cat_id,
		c.status,
		c.create_time
		from t_xmy_group_category_detail b
		left join t_xmy_category c on b.id1 = c.item_cat_id
		where b.group_id = #{groupId} and c.ec_id is not null
	</select>
	<!-- selectByCompany -->
	<select id="selectByCompany" resultMap="BaseResultMap">
		select
		c.ec_id,
		c.ec_name,
		c.id1,
		c.id1_name,
		c.id2,
		c.id2_name,
		c.id3,
		c.id3_name,
		c.id4,
		c.id4_name,
		c.item_cat_id,
		c.status,
		c.create_time
		from t_xmy_company_category b
		left join t_xmy_category c on b.item_cat_id = c.item_cat_id
		where b.company_id = #{companyId} and c.item_cat_id is not null
	</select>
</mapper>
