<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CarBrandGroupMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CarBrandGroup">
		<!-- dataId -->
		<id column="data_Id" property="dataId" />
		<!-- jobName -->
		<result column="job_name" property="jobName" />
		<!-- batchDt -->
		<result column="batch_dt" property="batchDt" />
		<!-- wPdateDt -->
		<result column="w_pdate_dt" property="wPdateDt" />
		<!-- wInsertDt -->
		<result column="w_insert_dt" property="wInsertDt" />
		<!-- brandCode -->
		<result column="brand_code" property="brandCode" />
		<!-- brandName -->
		<result column="brand_name" property="brandName" />
		<!-- brandFirstLetter -->
		<result column="brand_first_letter" property="brandFirstLetter" />
		<!-- groupName -->
		<result column="group_name" property="groupName" />
		<!-- orderSeq -->
		<result column="order_seq" property="orderSeq" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		<!-- data_id, group_name, job_name, batch_dt, brand_code,
		brand_name, brand_first_letter, order_seq,
		w_pdate_dt, w_insert_dt, status -->
		data_id, group_name, job_name, batch_dt, brand_code, 
		brand_name, brand_first_letter, order_seq, 
		w_pdate_dt, w_insert_dt, status
	</sql>
	<!-- getSearchBrandGroups -->
	<select id="getSearchBrandGroups" resultMap="BaseResultMap">
		<!-- SELECT data_id, group_name, brand_code, brand_name, brand_first_letter, order_seq
			FROM dim_voc3_car_brand_group t-->
		SELECT data_id, group_name, brand_code, brand_name, brand_first_letter, order_seq
			FROM dim_voc3_car_brand_group t
			where t.status = 1
		<if test="brandCodes != null and brandCodes.size() > 0">
			and t.brand_code in
			<foreach collection="brandCodes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="groupName != null">
			and t.group_name like CONCAT("%",#{groupName},"%")
		</if>
		order by order_seq
	</select>
	<!-- getBrandGroupNames -->
	<select id="getBrandGroupNames" resultMap="BaseResultMap">
	<!-- ELECT group_name
			FROM dim_voc3_car_brand_group t
			where t.status = 1-->
		SELECT group_name
			FROM dim_voc3_car_brand_group t
			where t.status = 1
		<if test="brandCodes != null and brandCodes.size() > 0">
			and t.brand_code in 
			<foreach collection="brandCodes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="groupName != null">
			and t.group_name like CONCAT("%",#{groupName},"%")
		</if>
		group by group_name
		order by group_name
	</select>

</mapper>
