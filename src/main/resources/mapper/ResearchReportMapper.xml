<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.research.mapper.ResearchReportMapper">

    <select id="search" resultType="com.meicloud.voc.research.entity.ResearchReport">
        SELECT * FROM dim_voc3_research_report
        where 1=1
        <if test="searchParams.status != null">
            and status = #{searchParams.status}
        </if>
        <if test="searchParams.type != null">
            and type = #{searchParams.type}
        </if>
        <if test="searchParams.reportName != null">
            and report_name = #{searchParams.reportName}
        </if>
        <if test="searchParams.reportDate != null">
            and report_date = #{searchParams.reportDate}
        </if>
        order by report_date desc
    </select>
</mapper>
