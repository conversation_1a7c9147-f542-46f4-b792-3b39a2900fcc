<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.category.mapper.CompanyCategoryMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.category.entity.CompanyCategory">
		<id column="id" property="id" />
		<result column="company_id" property="companyId" />
		<result column="item_cat_id" property="itemCatId" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, company_id, item_cat_id, create_time
	</sql>

</mapper>
