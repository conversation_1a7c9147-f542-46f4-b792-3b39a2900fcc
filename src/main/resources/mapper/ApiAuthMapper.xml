<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.security.api.mapper.ApiAuthMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.security.api.entity.ApiAuth">
		<id column="data_Id" property="dataId" />
		<result column="status" property="status" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		
		<result column="ip" property="ip" />
		<result column="token" property="token" />
		<result column="api" property="api" />
		<result column="remark" property="remark" />
		<result column="create_by" property="createBy" />
		<result column="update_by" property="updateBy" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, ip, token, api, status, remark, w_insert_dt, create_by, w_pdate_dt, update_by, job_name, batch_dt
	</sql>
	<!-- getApiAuths -->
	<select id="getApiAuths" resultMap="BaseResultMap">
		SELECT data_id, ip, token, api, status, remark
		FROM t_api_auth t1
		where t1.status = 1
		and t1.ip = #{ip}
		and t1.token = #{token}
	</select>

</mapper>
