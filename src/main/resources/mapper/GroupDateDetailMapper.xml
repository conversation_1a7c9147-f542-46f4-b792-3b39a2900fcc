<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.group.mapper.GroupDateDetailMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.group.entity.GroupDateDetail">
		 <id column="group_id" property="groupId" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="update_time" property="updateTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		group_id, start_date, end_date, update_time
	</sql>
    <!-- getByCompanyAndUser -->
	<select id="getByCompanyAndUser" resultMap="BaseResultMap">
		select
        b.group_id,
        b.start_date,
        b.end_date,
        b.update_time
        from t_sys_user_group a
        left join t_sys_group_date_detail b on a.group_id = b.group_id
        where a.company_id = #{companyId} and a.user_account = #{userAccount}
        order by end_date desc
        limit  1
	</select>
</mapper>
