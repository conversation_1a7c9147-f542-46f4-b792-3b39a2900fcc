<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.gxt.mapper.TMenuApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meicloud.voc.menu.entity.MenuApi">
        <id column="menu_id" property="menuId" />
        <result column="api_url" property="apiUrl" />
        <result column="is_enable" property="isEnable" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        menu_id, api_url, is_enable, create_time
    </sql>

</mapper>
