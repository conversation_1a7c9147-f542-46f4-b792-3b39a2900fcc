<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CarSeriesMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CarSeries">
		<id column="data_id" property="dataId" />
		<result column="brand_name" property="brandName" />
		<result column="series_code" property="seriesCode" />
		<result column="series_name" property="seriesName" />
		<result column="brand_code" property="brandCode" />
		<result column="brand_name" property="brandName" />
		<result column="energy_type" property="energyType" />
		
		<result column="market_id" property="marketId" />
		<result column="market_name" property="marketName" />
		<result column="series_logo" property="seriesLogo" />
		<result column="hot" property="hot" />
		<result column="order_seq" property="orderSeq" />
		<result column="series_first_letter" property="seriesFirstLetter" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
	   data_id, series_code, series_name, brand_code, brand_name, energy_type, 
			market_id, market_name,hot,order_seq,series_first_letter
	</sql>
	<!-- getSearchCarSeries -->
	<select id="getSearchCarSeries" resultMap="BaseResultMap">
		<!-- SELECT data_id, series_code, series_name, brand_code, brand_name, energy_type,
			market_id, market_name, series_logo, hot, order_seq, series_first_letter
		from
			dim_voc3_car_series t
			where status = 1 -->
		SELECT data_id, series_code, series_name, brand_code, brand_name, energy_type, 
			market_id, market_name, series_logo, hot, order_seq, series_first_letter
		from
			dim_voc3_car_series t
			where status = 1
		<if test="energyTypes != null and energyTypes.size() > 0">
			and t.energy_type in
			<foreach collection="energyTypes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="brandCodes != null and brandCodes.size() > 0">
			and t.brand_code in
			<foreach collection="brandCodes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="brandNames != null and brandNames.size() > 0">
			and t.brand_name in
			<foreach collection="brandNames" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="marketIds != null and marketIds.size() > 0">
			and t.market_id in
			<foreach collection="marketIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="marketNames != null and marketNames.size() > 0">
			and t.market_name in
			<foreach collection="marketNames" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="hot != null">
			and t.hot = #{hot}
		</if>
		order by t.order_seq,t.series_first_letter ASC
	</select>

</mapper>
