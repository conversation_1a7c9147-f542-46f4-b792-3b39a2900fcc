<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.log.mapper.VisitLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.log.entity.VisitLog">
        <!-- id-->
        <id column="id" property="id"/>
        <!-- userId-->
        <result column="userId" property="userId"/>
        <!-- userAccount-->
        <result column="user_account" property="userAccount"/>
        <!-- userName-->
        <result column="user_name" property="userName"/>
        <!-- deptCode-->
        <result column="dept_code" property="deptCode"/>
        <!-- deptName-->
        <result column="dept_name" property="deptName"/>
        <!-- positionName-->
        <result column="position_name" property="positionName"/>
        <!-- visitUrl-->
        <result column="visit_url" property="visitUrl"/>
        <!-- menuId -->
        <result column="menu_id" property="menuId"/>
        <!-- createTime-->
        <result column="create_time" property="createTime"/>
    </resultMap>
</mapper>
