<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CustTagMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CustTag">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="status" property="status" />
		
		<result column="tag_id" property="tagId" />
		<result column="tag_name" property="tagName" />
	</resultMap>
	
	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, tag_id, tag_name, status
	</sql>
	<!-- getCustTagList -->
	<select id="getCustTagList" resultMap="BaseResultMap">
		SELECT data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, tag_id, tag_name, status
		FROM dim_voc3_cust_tag t1
		where t1.status = 1
		<if test="tagName != null">
			and t1.tag_name like CONCAT("%",#{tagName},"%")
		</if>
	</select>

</mapper>
