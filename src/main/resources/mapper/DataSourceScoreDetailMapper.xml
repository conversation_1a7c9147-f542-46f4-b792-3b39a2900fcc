<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.datasourcescoredetail.mapper.IDataSourceScoreDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.datasourcescoredetail.entity.DataSourceScoreDetail">
        <!-- dataId -->
        <id column="data_id" property="dataId"/>
        <!-- dataSourceId -->
        <result column="data_source_id" property="dataSourceId"/>
        <!-- score -->
        <result column="score" property="score"/>
        <!-- rater -->
        <result column="rater" property="rater"/>
        <!-- department -->
        <result column="department" property="department"/>
        <!-- jobName -->
        <result column="job_name" property="jobName"/>
        <!-- wInsertDt -->
        <result column="w_insert_dt" property="wInsertDt"/>
        <!-- wPdateDt -->
        <result column="w_pdate_dt" property="wPdateDt"/>
        <!-- batchDt -->
        <result column="batch_dt" property="batchDt"/>
        <!-- status -->
        <result column="status" property="status"/>
    </resultMap>
</mapper>
