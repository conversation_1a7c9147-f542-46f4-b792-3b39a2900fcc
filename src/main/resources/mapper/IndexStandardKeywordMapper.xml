<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.IndexStandardKeywordMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.IndexSystemStandardKeyword">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="status" property="status" />
		
		<result column="four_index_id" property="fourIndexId" />
		<result column="four_index_name" property="fourIndexName" />
		<result column="standard_keword" property="standardKeword" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
			data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, four_index_id, 
			four_index_name, standard_keword, status
	</sql>
	<!-- getIndexStandardKeyword -->
	<select id="getIndexStandardKeyword" resultMap="BaseResultMap">
		SELECT four_index_id, four_index_name, standard_keword
		FROM dim_voc3_index_system_standard_keyword word
		where word.status = 1
		<if test="fourIndexIds != null and fourIndexIds.size() > 0">
			and word.four_index_id in
			<foreach collection="fourIndexIds" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="standardKeword != null and standardKeword.size() > 0">
			and word.standard_keword like CONCAT("%",#{standardKeword},"%")
		</if>
		order by four_index_id
	</select>

</mapper>
