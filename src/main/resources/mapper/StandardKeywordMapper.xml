<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.selfAnalysis.mapper.StandardKeywordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.selfAnalysis.entity.StandardKeyword">
        <!-- data_id主键 -->
        <id column="data_id" property="dataId"/>
        <!-- name -->
        <result column="name" property="name"/>
        <!-- emotion_attribute -->
        <result column="emotion_attribute" property="emotionAttribute"/>
        <!-- clarity -->
        <result column="clarity" property="clarity"/>
        <result column="field" property="field"/>
        <result column="department" property="department"/>
        <result column="charge" property="charge"/>
        <result column="charge_id" property="chargeId"/>
        <result column="notes" property="notes"/>
        <result column="creater" property="creater"/>
        <result column="last_modifier" property="lastModifier"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        <result column="index_type" property="indexType"/>
    </resultMap>
    <!-- selectStKeywordAttentions -->
    <select id="selectStKeywordAttentions" resultType="com.meicloud.voc.selfAnalysis.dto.StKeywordDetailResult">
        <!--  select st.standard_keyword_id dataId,st.standard_keyword name ,
        st.emotion_attribute, st.clarity, st.field ,
        st.department ,st.department_id,
        st.charge ,st.charge_id,
        IF(t3.data_id, 1, 0) is_attention
        from dim_voc3_index_system_standard_keyword st
        left join (
        select stat.* from dim -->
        select st.standard_keyword_id dataId,st.standard_keyword name ,
        st.four_index_id,
        st.emotion_attribute, st.clarity, st.field ,
        st.department ,st.department_id,
        st.charge ,st.charge_id,
        IF(t3.data_id, 1, 0) is_attention
        from dim_voc3_index_system_standard_keyword st
        left join (
        select stat.* from dim_voc3_m_standard_keyword_attention stat
        where 1=1
        <if test="params.attentionType == 1">
            and stat.user_id = #{userId}
        </if>
        ) t3
        on st.standard_keyword_id = t3.standard_keyword_id
        where st.status =1
        <if test="params.ids != null and params.ids.size > 0 ">
            and st.standard_keyword_id in
            <foreach collection="params.ids" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="params.attentionType == 2">
            and t3.user_id = #{userId}
        </if>
        <if test="params.field != null and params.field != '' ">
            and st.field = #{params.field}
        </if>
        <if test="params.nameList != null and params.nameList.size > 0 ">
            and st.standard_keyword in
            <foreach collection="params.nameList" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="params.fourIdList != null and params.fourIdList.size > 0 ">
            and st.four_index_id in
            <foreach collection="params.fourIdList" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="params.department != null and params.department != ''">
            and st.department like concat ('%', #{params.department},'%')
        </if>
        <if test="params.charge != null and params.charge != ''">
            and st.charge like concat ('%', #{params.charge},'%')
        </if>
        <if test="params.emotionAttribute != null and params.emotionAttribute != ''">
            and st.emotion_attribute = #{params.emotionAttribute}
        </if>
        <if test="params.clarity != null and params.clarity != ''">
            and st.clarity = #{params.clarity}
        </if>
        <if test="params.indexTypeName != null and params.indexTypeName != ''">
            and st.index_type = #{params.indexTypeName}
        </if>
    </select>

    <!-- getKeywordList-->
    <select id="getKeywordList" resultType="com.meicloud.voc.selfAnalysis.dto.StKeywordDetailResult">
        <!--  select st.standard_keyword_id dataId,st.standard_keyword name ,
        st.emotion_attribute, st.clarity, st.field ,
        st.department ,st.department_id,
        st.charge ,st.charge_id,
        ist.index_id index_type, ist.index_type index_type_name,ist.first_index_id,ist.first_index_name,
        ist.second_index_id,ist.second_index_name,
        ist.third_index_id,ist.third_index_name, -->
        select st.standard_keyword_id dataId,st.standard_keyword name ,
        st.emotion_attribute, st.clarity, st.field ,
        st.department ,st.department_id,
        st.charge ,st.charge_id,
        ist.index_id index_type, ist.index_type index_type_name,ist.first_index_id,ist.first_index_name,
        ist.second_index_id,ist.second_index_name,
        ist.third_index_id,ist.third_index_name,
        ist.four_index_id,ist.four_index_name
        from dim_voc3_index_system_standard_keyword st
        left join dim_voc3_index_system ist on st.four_index_id = ist.four_index_id
        where st.status =1
        <if test="params.ids != null and params.ids.size > 0 ">
            and st.standard_keyword_id in
            <foreach collection="params.ids" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="params.nameList != null and params.nameList.size > 0">
            and st.standard_keyword in
            <foreach collection="params.nameList" item="name" index="index" open="(" close=")" separator=",">
                #{name}
            </foreach>
        </if>
        <if test="params.field != null and params.field != '' ">
            and st.field = #{params.field}
        </if>
        <if test="params.names != null and params.names != ''">
            and st.standard_keyword like CONCAT("%",#{params.names},"%")
        </if>
        <if test="params.department != null and params.department != ''">
            and st.department like CONCAT("%",#{params.department},"%")
        </if>
        <if test="params.charge != null and params.charge != ''">
            and st.charge like CONCAT("%",#{params.charge},"%")
        </if>
        <if test="params.emotionAttribute != null and params.emotionAttribute != ''">
            and st.emotion_attribute = #{params.emotionAttribute}
        </if>
        <if test="params.clarity != null and params.clarity != ''">
            and st.clarity = #{params.clarity}
        </if>
        <if test="params.indexType != null and params.indexType != ''">
            and ist.index_id = #{params.indexType}
        </if>
        <if test="params.firstIndexId != null and params.firstIndexId != ''">
            and ist.first_index_id = #{params.firstIndexId}
        </if>
        <if test="params.secondIndexId != null and params.secondIndexId != ''">
            and ist.second_index_id = #{params.secondIndexId}
        </if>
        <if test="params.thirdIndexId != null and params.thirdIndexId != ''">
            and ist.third_index_id = #{params.thirdIndexId}
        </if>
        <if test="params.fourIndexId != null and params.fourIndexId != ''">
            and ist.four_index_id = #{params.fourIndexId}
        </if>
    </select>
    <!-- queryKwByCorpus-->
    <select id="queryKwByCorpus" resultType="com.meicloud.voc.manage.keyword.dto.QueryStKwByCorpusResult">
        <!--    select kw.data_id             keywordId,
               kw.keyword             keyword,
               stkw.data_id           standardKeywordId,
               stkw.standard_keyword  standardKeywordName,
               stkw.emotion_attribute standardKeywordType -->
        select kw.data_id             keywordId,
               kw.keyword             keyword,
               stkw.data_id           standardKeywordId,
               stkw.standard_keyword  standardKeywordName,
               stkw.emotion_attribute standardKeywordType
        from dim_voc3_index_system_keyword kw
                 left join dim_voc3_index_system_standard_keyword stkw
                           on kw.standard_keyword_id = stkw.standard_keyword_id
        where kw.keyword = #{corpus} limit 1
    </select>
    <!-- stKeywordSearch-->
    <select id="stKeywordSearch" resultType="com.meicloud.voc.manage.stKeywordManage.dto.StKwSearchResult">
        <!--    select st.standard_keyword_id dataId , st.standard_keyword name,
        ist.index_type indexTypeName,
        concat(ist.first_index_name ,'/',ist.second_index_name,
        '/',ist.third_index_name , '/',ist.four_index_name) indexNames -->
        select st.standard_keyword_id dataId , st.standard_keyword name,
        istr.index_type indexTypeName,
        concat(istr.first_index_name ,'/',istr.second_index_name,
        '/',istr.third_index_name , '/',istr.four_index_name) indexNames
        from dim_voc3_index_system_standard_keyword st
        inner join (
            select * from
                dim_voc3_index_system ist
                where ist.status ='1'
                <if test="params.indexTypeName != null and params.indexTypeName != ''">
                    and ist.index_type = #{params.indexTypeName}
                </if>
                <if test="params.firstIndexId != null and params.firstIndexId != ''">
                    and ist.first_index_id = #{params.firstIndexId}
                </if>
                <if test="params.secondIndexId != null and params.secondIndexId != ''">
                    and ist.second_index_id = #{params.secondIndexId}
                </if>
                <if test="params.thirdIndexId != null and params.thirdIndexId != ''">
                    and ist.third_index_id = #{params.thirdIndexId}
                </if>
                <if test="params.fourIndexId != null and params.fourIndexId != ''">
                    and ist.four_index_id = #{params.fourIndexId}
                </if>
            ) istr
        on st.four_index_id = istr.four_index_id
        where st.status ='1'
        <if test="params.names != null and params.names != '' ">
            and st.standard_keyword regexp #{params.names}
        </if>
        <if test="params.indexTypeName != null and params.indexTypeName != ''">
            and st.index_type = #{params.indexTypeName}
        </if>

    </select>

</mapper>
