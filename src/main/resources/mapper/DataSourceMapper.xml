<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.DataSourceMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.DataSource">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="weight" property="weight" />
		<result column="status" property="status" />
		<result column="data_source" property="dataSource" />
		<result column="is_outer" property="isOuter" />
		<result column="source_type" property="sourceType" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, w_pdate_dt, w_insert_dt, job_name, 
		batch_dt, data_source, is_outer, status
	</sql>
	<!-- getSearchDataSource -->
	<select id="getSearchDataSource" resultMap="BaseResultMap">
		SELECT data_id, data_source, is_outer, weight, source_type
		FROM dim_voc3_data_source source
		where source.status = 1
		<if test="isOuter != null">
			and source.is_outer = #{isOuter}
		</if>
		<if test="dataSource != null">
			and source.data_source like CONCAT("%",#{dataSource},"%")
		</if>
		order by source.sort_number asc
	</select>

</mapper>
