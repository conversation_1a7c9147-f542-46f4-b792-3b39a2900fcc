<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CarBrandMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CarBrand">
		<!-- dataId -->
		<id column="data_Id" property="dataId" />
		<!-- brandCode -->
		<result column="brand_code" property="brandCode" />
		<!-- brandName -->
		<result column="brand_name" property="brandName" />
		<!-- brandFirstLetter -->
		<result column="brand_first_letter" property="brandFirstLetter" />
		<!-- orderSeq -->
		<result column="order_seq" property="orderSeq" />
		<!-- jobName -->
		<result column="job_name" property="jobName" />
		<!-- batchDt -->
		<result column="batch_dt" property="batchDt" />
		<!-- wPdateDt -->
		<result column="w_pdate_dt" property="wPdateDt" />
		<!-- wInsertDt -->
		<result column="w_insert_dt" property="wInsertDt" />
		<!-- nature -->
		<result column="nature" property="nature" />
		<!-- brandLogo -->
		<result column="brand_logo" property="brandLogo" />
		<!-- hot -->
		<result column="hot" property="hot" />
	</resultMap>


	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, brand_code, brand_name, brand_first_letter, 
		nature, w_pdate_dt, w_insert_dt, job_name, batch_dt, order_seq, brand_logo
	</sql>
	<!-- getSearchCarBrands -->
	<select id="getSearchCarBrands" resultMap="BaseResultMap">
		SELECT data_id, brand_code, brand_name, brand_first_letter, nature, 
			w_pdate_dt, w_insert_dt, job_name, batch_dt, order_seq, logo_url as brand_logo, hot
			FROM dim_voc3_car_brand brand 
			where brand.brand_name is not null
		and brand.brand_code is not null
		and brand.status = 1
		<if test="brandIds != null and brandIds.size() > 0">
			and brand.data_id in(#{brandIds})
		</if>
		<if test="nature != null">
			and brand.nature = #{nature}
		</if>
		<if test="brandName != null">
			and brand.brand_name like CONCAT("%",#{brandName},"%")
		</if>
		<if test="hot != null">
			and brand.hot = #{hot}
		</if>
		order by
		order_seq, brand_name
	</select>

</mapper>
