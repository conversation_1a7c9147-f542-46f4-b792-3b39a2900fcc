<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.menu.mapper.MenuMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.menu.entity.Menu">
        <!-- id -->
		 <id column="id" property="id"/>
        <!-- menuId -->
        <result column="menu_id" property="menuId"/>
        <!-- pMenuId -->
        <result column="p_menu_id" property="pMenuId"/>
        <!-- orderNum -->
        <result column="order_num" property="orderNum"/>
        <!-- companyId -->
        <result column="company_id" property="companyId"/>
        <!-- menuType -->
        <result column="menu_type" property="menuType"/>
        <!-- menuUrl -->
        <result column="menu_url" property="menuUrl"/>
        <!-- menuVersion -->
        <result column="menu_version" property="menuVersion"/>
        <!-- updateTime -->
        <result column="update_time" property="updateTime"/>
        <!-- menuName -->
        <result column="menu_name" property="menuName"/>
        <!-- menuIcon -->
        <result column="menu_icon" property="menuIcon"/>
        <!-- component -->
        <result column="component" property="component"/>
        <!-- hidden -->
        <result column="hidden" property="hidden"/>
        <!-- deleteStatus -->
        <result column="delete_status" property="deleteStatus"/>
        <!-- menuTag -->
        <result column="menu_tag" property="menuTag"/>
        <!-- menuTips -->
        <result column="menu_tips" property="menuTips"/>
        <!-- menuData -->
        <result column="menu_data" property="menuData"/>
        <!-- menuTips -->
        <result column="is_page" property="isPage"/>
        <!-- menuData -->
        <result column="iframe_url" property="iframeUrl"/>
	</resultMap>

	<!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        iframe_url,is_page,menu_data,menu_tips,menu_tag,id,menu_id, p_menu_id, order_num, company_id, menu_type, menu_url, menu_version, update_time, menu_name, menu_icon, hidden, component, delete_status
    </sql>
    <!-- deleteMenu -->
    <update id="deleteMenu">
        update t_sys_menu set delete_status = 1 where company_id = #{companyId} and menu_type = #{menuType};
    </update>
    <!-- selectByCompanyAndUser -->
    <select id="selectByCompanyAndUser" resultMap="BaseResultMap">
        <!--  select
          b.menu_id,
          b.p_menu_id,
          b.order_num,
          b.company_id,
          b.menu_type,
          b.menu_url,
          b.menu_data,
          b.menu_version,
          b.update_time,
          b.menu_name,
          b.menu_icon,
          b.hidden, -->
        select
          b.menu_id,
          b.p_menu_id,
          b.order_num,
          b.company_id,
          b.menu_type,
          b.menu_url,
          b.menu_data,
          b.menu_version,
          b.update_time,
          b.menu_name,
          b.menu_icon,
          b.hidden,
          b.component,
          b.menu_tag,
          b.menu_tips,
          b.is_page,
          b.iframe_url
        from
        (select distinct menu_id
        from t_sys_group_menu_detail a
        left join t_sys_user_group b on a.group_id = b.group_id
        where b.company_id = #{companyId} and b.user_account = #{userAccount}) a
        left join t_sys_menu b on a.menu_id = b.menu_id
        where b.delete_status = 0 and b.company_id = #{companyId}
    </select>
    <!-- selectByCompany -->
    <select id="selectByCompany" resultMap="BaseResultMap">
        select
          c.menu_id,
          c.p_menu_id,
          c.order_num,
          c.company_id,
          c.menu_type,
          c.menu_url,
          c.menu_data,
          c.menu_version,
          c.update_time,
          c.menu_name,
          c.menu_icon,
          c.hidden,
          c.component,
          c.menu_tag,
          c.menu_tips,
          c.is_page,
          c.iframe_url
        from t_sys_menu c
        where c.company_id = #{companyId} and c.delete_status = 0;
    </select>
    <!-- selectByGroup -->
    <select id="selectByGroup" resultMap="BaseResultMap">
        select
          c.menu_id,
          c.p_menu_id,
          c.order_num,
          c.company_id,
          c.menu_type,
          c.menu_url,
          c.menu_data,
          c.menu_version,
          c.update_time,
          c.menu_name,
          c.menu_icon,
          c.hidden,
          c.component,
          c.menu_tag,
          c.menu_tips
        from t_sys_group a
        left join t_sys_group_menu_detail b on a.group_id = b.group_id
        left join t_sys_menu c on b.menu_id = c.menu_id
        where a.group_id = #{groupId} and c.delete_status = 0;
    </select>
</mapper>

