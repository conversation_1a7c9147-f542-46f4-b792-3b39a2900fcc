<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.data.mapper.ESIndexMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.data.entity.ESIndex">
		<id column="id" property="id" />
		<result column="name" property="name" />
		<result column="write_alias" property="writeAlias" />
		<result column="read_alias" property="readAlias" />
		<result column="mapping" property="mapping" />
		<result column="settings" property="settings" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, name, write_alias, read_alias, mapping, settings
	</sql>

</mapper>
