<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.RegionMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.Region">
		<!-- dataId -->
		<id column="data_Id" property="dataId" />
		<!-- jobName -->
		<result column="job_name" property="jobName" />
		<!-- batchDt -->
		<result column="batch_dt" property="batchDt" />
		<!-- wPdateDt -->
		<result column="w_pdate_dt" property="wPdateDt" />
		<!-- wInsertDt -->
		<result column="w_insert_dt" property="wInsertDt" />
		<!-- status -->
		<result column="status" property="status" />
		<!-- province -->
		<result column="province" property="province" />
		<!-- city -->
		<result column="city" property="city" />
		<!-- area -->
		<result column="area" property="area" />
		<!-- cityLevel -->
		<result column="city_level" property="cityLevel" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, 
		province, city, area, status, city_level
	</sql>
	<!-- getSearchRegion -->
	<select id="getSearchRegion" resultMap="BaseResultMap">
		<!-- SELECT region.data_id, province, city, area, city.level_name as city_level
		FROM dim_voc3_region region
		left join dim_voc3_city_level city on city.city_name like CONCAT(region.city,"%") -->
		SELECT region.data_id, province, city, area, city.level_name as city_level
		FROM dim_voc3_region region
		left join dim_voc3_city_level city on city.city_name like CONCAT(region.city,"%")
		where region.status = 1
		and region.city is not null
		<if test="province != null">
			and region.province = #{province}
		</if>
		<if test="city != null">
			and region.city = #{city}
		</if>
		<if test="area != null">
			and region.area = #{area}
		</if>
		order by province,city
	</select>
	<!-- getProvinceList -->
	<select id="getProvinceList" resultMap="BaseResultMap">
		<!-- SELECT province
		FROM dim_voc3_region region
		where region.status = 1 -->
		SELECT province
		FROM dim_voc3_region region
		where region.status = 1
		<if test="province != null">
			and region.province like CONCAT("%",#{province},"%")
		</if>
		<if test="cityList != null and cityList.size() > 0">
			and region.city in
			<foreach collection="cityList" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="levelList != null and levelList.size() > 0">
			and region.city_level in 
			<foreach collection="levelList" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		group by province
		order by province
	</select>

</mapper>
