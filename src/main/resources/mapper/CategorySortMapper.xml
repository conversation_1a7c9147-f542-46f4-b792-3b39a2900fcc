<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.meicloud.voc.category.mapper.CategorySortMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.category.entity.CategorySort">
		<id column="id" property="id" />
		<result column="company_id" property="companyId" />
		<result column="sort_rule" property="sortRule" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, company_id, sort_rule, create_time
	</sql>

</mapper>
