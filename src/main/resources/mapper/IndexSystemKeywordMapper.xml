<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.IndexSystemKeywordMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.IndexSystemKeyword">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="status" property="status" />
		
		<result column="standard_keyword_id" property="standardKeywordId" />
		<result column="keyword" property="keyword" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
			data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, 
			standard_keyword_id, keyword, status
	</sql>
	<!-- getIndexSystemKeyword -->
	<select id="getIndexSystemKeyword" resultMap="BaseResultMap">
		SELECT data_id, standard_keyword_id, keyword, status
		FROM dim_voc3_index_system_keyword word
		where word.status = 1
		<if test="standardKeywordId != null and standardKeywordId != ''">
			and word.standard_keyword_id = #{standardKeywordId}
		</if>
		<if test="keyword != null and keyword.size() > 0">
			and word.keyword like CONCAT("%",#{keyword},"%")
		</if>
		order by standard_keyword_id
	</select>

</mapper>
