<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.indexSystemManage.mapper.IndexSystemItemManageMapper">
    <!-- 通用查询映射结果 -->

    <select id="findLastLevelModifyByRange"
            resultType="com.meicloud.voc.manage.indexSystemManage.dto.IndexSystemRangeResult">
        select
          isi.data_id dataId,
          isi.index_id indexId,
          dvmis.index_type_name indexType,
          isi.item_id itemId,
          isi.item_name itemName,
          isi.level level,
          isi.parent_name_path parentNamePath,
          isi.parent_id_path parentIdPath,
          dvmis.enabled enabled,
          isi.status status,
          dvmis.status indexStatus
        from
        dim_voc3_m_index_system_item isi
        left join dim_voc3_m_index_system dvmis on isi.index_id = dvmis.data_id
        where
        isi.level = dvmis.metric_level
        <if test="startTime != null and endTime != null">
        and
        (
        (
        <if test="startTime != null">
            isi.w_pdate_dt >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[and isi.w_pdate_dt <= #{endTime}]]>
        </if>

        )
        or (

        <if test="startTime != null">
            dvmis.w_pdate_dt >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[and dvmis.w_pdate_dt <= #{endTime}]]>
        </if>
        )
        )
        </if>
    </select>
    <select id="findIndexSystemItem"
            resultType="com.meicloud.voc.manage.indexSystemManage.entity.IndexSystemItemManage">
        select
            isi.data_id dataId,
            isi.index_id indexId,
            isi.item_id itemId,
            isi.item_name itemName,
            isi.level level,
            isi.parent_name_path parentNamePath,
            isi.parent_id_path parentIdPath,
            isi.standard_keyword_quantity standardKeywordQuantity,
            isi.keyword_quantity keywordQuantity,
            isi.job_name jobName,
            isi.w_insert_dt wInsertDt,
            isi.w_pdate_dt wPdateDt,
            isi.batch_dt batchDt,
            isi.creater_id createrId,
            isi.last_modifier_id lastModifierId,
            isi.status status
        from
            dim_voc3_m_index_system_item isi
                left join dim_voc3_m_index_system dvmis on isi.index_id = dvmis.data_id
        where
            isi.level = dvmis.metric_level
            and isi.status = '1'
            and dvmis.status = '1'
            <if test="itemId != null || itemId != ''">
                and isi.item_id = #{itemId}
            </if>
            <if test="itemName != null || itemName != ''">
                and isi.item_name = #{itemName}
            </if>
            <if test="indexId != null || indexId != ''">
                and isi.index_id = #{indexId}
            </if>
    </select>
</mapper>
