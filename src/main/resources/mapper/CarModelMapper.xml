<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.car.mapper.CarModelMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.car.entity.CarModel">
		<id column="data_Id" property="dataId" />
		<result column="job_name" property="jobName" />
		<result column="batch_dt" property="batchDt" />
		<result column="w_pdate_dt" property="wPdateDt" />
		<result column="w_insert_dt" property="wInsertDt" />
		<result column="status" property="status" />
		
		<result column="model_code" property="modelCode" />
		<result column="model_name" property="modelName" />
		<result column="series_code" property="seriesCode" />
		<result column="series_name" property="seriesName" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		   data_id, w_pdate_dt, w_insert_dt, job_name, 
		   batch_dt, model_code, model_name, series_code, series_name, status
	</sql>
	<!-- getSearchCarModel -->
	<select id="getSearchCarModel" resultMap="BaseResultMap">
		select model.series_code,model.model_code,model.model_name 
			from dim_voc3_car_model model
		where model.status = 1
		<if test="seriesCodes != null and seriesCodes.size() > 0">
			and model.series_code in
			<foreach collection="seriesCodes" item="name" index="index" open="(" close=")" separator=",">
				#{name}
			</foreach>
		</if>
		<if test="modelName != null">
			and model.model_name like CONCAT("%",#{modelName},"%")
		</if>
	</select>

</mapper>
