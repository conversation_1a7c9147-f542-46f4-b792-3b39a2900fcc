<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.group.mapper.GroupMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.meicloud.voc.group.entity.Group">
		<id column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="group_code" property="groupCode"/>
        <result column="group_desc" property="groupDesc"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
	</resultMap>
	<!-- VoResultMap -->
	<resultMap id="VoResultMap"
		type="com.meicloud.voc.security.api.vo.RoleInfoVo">
		<id column="group_id" property="roleId" />
		<id column="group_name" property="roleName" />
		<id column="group_code" property="roleCode" />
		<id column="group_desc" property="roleDesc" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		group_id, group_name, groupCode, group_desc, status, create_time, update_time
	</sql>
	<!-- getRoleList -->
	<select id="getRoleList" resultMap="VoResultMap">
		SELECT group_id, group_name, group_code, group_desc
		FROM t_sys_group t1 
		where t1.status = 1
		<if test="roleId != null">
			and t1.group_id = #{roleId}
		</if>
		<if test="roleName != null">
			and t1.group_name like CONCAT("%",#{roleName},"%")
		</if>
	</select>
	<!-- getRoleByName -->
	<select id="getRoleByName" resultMap="BaseResultMap">
		SELECT group_id, group_name, group_code, group_desc
		FROM t_sys_group t1 
		where t1.status = 1
		and t1.group_name = #{roleName}
	</select>
	<!-- updateGroupById -->
	<update id="updateGroupById" parameterType="com.meicloud.voc.group.entity.Group">
 		update t_sys_group
 		<set>
 			<if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
 			<if test="groupCode != null and groupCode != ''">group_name = #{groupCode},</if>
 			<if test="groupDesc != null and groupDesc != ''">group_desc = #{groupDesc},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			update_time = sysdate()
 		</set>
 		where group_id = #{groupId}
	</update>
	
</mapper>
