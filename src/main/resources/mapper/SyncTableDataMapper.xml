<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.stKeywordManage.mapper.SyncTableDataMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="SyncTable">
        <id column="data_id" property="dataId"/>
        <result column="job_name" property="jobName"/>
        <result column="w_insert_dt" property="wInsertDt"/>
        <result column="w_pdate_dt" property="wPdateDt"/>
        <result column="batch_dt" property="batchDt"/>
        <result column="status" property="status"/>
        
        <result column="sync_table_name" property="syncTableName"/>
        <result column="sync_date" property="syncDate"/>
        
    </resultMap>
    
    <!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, sync_table_name, sync_date, status
	</sql>
    <!-- getWordCorpusByParams -->
    <select id="getDataList" resultMap="BaseResultMap">
        SELECT data_id, w_pdate_dt, w_insert_dt, job_name, batch_dt, sync_table_name, sync_date, status
		FROM dim_voc3_m_sync_table t
        WHERE t.status = 1
        <if test="params.syncTableName != null and params.syncTableName != ''">
			and t.sync_table_name = #{params.syncTableName}
		</if>
        order by t.w_insert_dt desc
        <if test="total != null">
			limit #{total}
		</if>
    </select>
</mapper>
