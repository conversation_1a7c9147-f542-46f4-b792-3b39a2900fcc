<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.questionnaireimport.mapper.IQuestionnaireImportMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.questionnaireimport.entity.QuestionnaireImport">
        <!-- dataId -->
        <id column="data_id" property="dataId"/>
        <!-- wInsertDt -->
        <result column="w_insert_dt" property="wInsertDt"/>
        <!-- wPdateDt -->
        <result column="w_pdate_dt" property="wPdateDt"/>
        <!-- status -->
        <result column="status" property="status"/>
        <!-- importDate -->
        <result column="import_date" property="importDate"/>
        <!-- userId -->
        <result column="user_id" property="userId"/>
        <!-- userName -->
        <result column="user_name" property="userName"/>
        <!-- userAccount -->
        <result column="user_account" property="userAccount"/>
        <!-- successCount -->
        <result column="success_count" property="successCount"/>
        <!-- failCount -->
        <result column="fail_count" property="failCount"/>
        <!-- fileName -->
        <result column="file_name" property="fileName"/>
        <!-- fileLink -->
        <result column="file_link" property="fileLink"/>
    </resultMap>
</mapper>
