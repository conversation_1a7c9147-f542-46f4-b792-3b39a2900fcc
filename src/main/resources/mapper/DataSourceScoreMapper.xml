<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meicloud.voc.manage.datasourcescore.mapper.IDataSourceScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.meicloud.voc.manage.datasourcescore.entity.DataSourceScore">
        <!-- data_id -->
        <id column="data_id" property="dataId"/>
        <!-- dataSource -->
        <result column="data_source" property="dataSource"/>
        <!-- describe -->
        <result column="describe" property="describe"/>
        <!-- is_outer -->
        <result column="is_outer" property="isOuter"/>
        <!-- weight -->
        <result column="weight" property="weight"/>
        <!-- type -->
        <result column="type" property="type"/>
        <!-- job_name -->
        <result column="job_name" property="jobName"/>
        <!-- w_insert_dt -->
        <result column="w_insert_dt" property="wInsertDt"/>
        <!-- w_pdate_dt -->
        <result column="w_pdate_dt" property="wPdateDt"/>
        <!-- batch_dt -->
        <result column="batch_dt" property="batchDt"/>
        <!-- status -->
        <result column="status" property="status"/>
    </resultMap>
</mapper>
