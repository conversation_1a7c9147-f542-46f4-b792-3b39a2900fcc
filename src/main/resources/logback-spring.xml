<?xml version="1.0" encoding="UTF-8"?>
<!--
    本日志配置需要引入if标签依赖包：
    <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>3.0.6</version>
    </dependency>
-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 日志根目录 -->
    <property name="LOG_HOME" value="logs"/>
    <!--
        是否使用skywalking链路跟踪，如果为true，需要引入依赖包：
         <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.7.0</version>
        </dependency>
    -->
    <property name="HAS_SKYWALKING" value="false"/>
    <property name="MAX_FILE_SIZE" value="100MB"/>
    <property name="TOTAL_SIZE_CAP" value="20GB"/>
    <property name="MAX_HISTORY" value="90"/>
    <if condition='p("HAS_SKYWALKING").equals("true")'>
        <then>
            <property name="DEF_ENCODER_PATTERN" value="[%-5level][%date][%thread][%logger{50}][%F %L][%tid] - %msg%n"/>
        </then>
        <else>
            <property name="DEF_ENCODER_PATTERN" value="[%-5level][%date][%thread][%logger{20}][%F %L] - %msg%n"/>
        </else>
    </if>

    <!--控制台日志 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <if condition='p("HAS_SKYWALKING").equals("true")'>
            <then>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                        <pattern>${DEF_ENCODER_PATTERN}</pattern>
                    </layout>
                </encoder>
            </then>
            <else>
                <encoder>
                    <pattern>${DEF_ENCODER_PATTERN}</pattern>
                </encoder>
            </else>
        </if>
    </appender>

    <!-- debug日志 -->
    <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/debug/debug.log</file>
        <!-- 过滤器，记录「所有」级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <!-- 滚动策略: 时间 和 大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/debug/%d{yyyy-MM, aux}/debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>

        <if condition='p("HAS_SKYWALKING").equals("true")'>
            <then>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                        <pattern>${DEF_ENCODER_PATTERN}</pattern>
                    </layout>
                </encoder>
            </then>
            <else>
                <encoder>
                    <pattern>${DEF_ENCODER_PATTERN}</pattern>
                </encoder>
            </else>
        </if>
    </appender>

    <!-- info日志 -->
    <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/info/info.log</file>
        <!-- 过滤器，记录「所有」级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!-- 滚动策略: 时间 和 大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/info/%d{yyyy-MM, aux}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <if condition='p("HAS_SKYWALKING").equals("true")'>
            <then>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                        <pattern>${DEF_ENCODER_PATTERN}</pattern>
                    </layout>
                </encoder>
            </then>
            <else>
                <encoder>
                    <pattern>${DEF_ENCODER_PATTERN}</pattern>
                </encoder>
            </else>
        </if>
    </appender>

    <!-- warn日志 -->
    <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/warn/warn.log</file>
        <!-- 过滤器，记录「所有」级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <!-- 滚动策略: 时间 和 大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/warn/%d{yyyy-MM, aux}/warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>

        <if condition='p("HAS_SKYWALKING").equals("true")'>
            <then>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                        <pattern>${DEF_ENCODER_PATTERN}</pattern>
                    </layout>
                </encoder>
            </then>
            <else>
                <encoder>
                    <pattern>${DEF_ENCODER_PATTERN}</pattern>
                </encoder>
            </else>
        </if>
    </appender>

    <!-- error日志 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error/error.log</file>
        <!-- 过滤器，记录「所有」级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <!-- 滚动策略: 时间 和 大小 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error/%d{yyyy-MM,aux}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>

        <if condition='p("HAS_SKYWALKING").equals("true")'>
            <then>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                        <pattern>${DEF_ENCODER_PATTERN}</pattern>
                    </layout>
                </encoder>
            </then>
            <else>
                <encoder>
                    <pattern>${DEF_ENCODER_PATTERN}</pattern>
                </encoder>
            </else>
        </if>

    </appender>

    <logger name="org.springframework" level="DEBUG">
        <appender-ref ref="DEBUG"/>
    </logger>

    <logger name="com.alibaba.nacos" level="ERROR">
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="com.alibaba.druid" level="ERROR">
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="io.lettuce" level="ERROR">
        <appender-ref ref="ERROR"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="WARN"/>
        <appender-ref ref="ERROR"/>
    </root>
</configuration>
