package com.meicloud.voc.export.enums;


/**
 *
 */
public enum ExportType {
   /**
    * excel文件
    */
   EXPORT_TYPE_EXCEL("excel", 1),
   /**
    * csv文件
    */
   EXPORT_TYPE_CSV("csv", 2),

   /**
    * 原文明细导出
    */
   EXPORT_TYPE_ORIGINAL_DETAIL("originalDetail", 3);

   private String name;
   private String code;

   ExportType(String name, int code) {
      this.name = name;
      this.code = String.valueOf(code);
   }

   public String getName() {
      return this.name;
   }

   public String getCode() {
      return this.code;
   }

   public static ExportType getByCode(String code) {
      for (ExportType exportType : values()) {
         if (exportType.getCode().equals(code)) {
            return exportType;
         }
      }
      return null;
   }
}