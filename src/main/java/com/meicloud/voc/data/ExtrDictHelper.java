package com.meicloud.voc.data;

import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.RedisDao;
import com.meicloud.voc.common.utils.RedisKey;
import com.meicloud.voc.data.entity.ExtrDict;
import com.meicloud.voc.data.entity.ExtrDomain;
import com.meicloud.voc.data.service.IExtrDictService;
import com.meicloud.voc.data.service.IExtrDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 算法字典helper
 */
@Slf4j
@Component
public class ExtrDictHelper {
    @Autowired
    private IExtrDictService extrDictService;
    @Autowired
    private IExtrDomainService extrDomainService;
    @Autowired
    private RedisDao redisDao;

    /**
     * 定时数据同步
     */
    @Scheduled(cron = "0 30 23 * * ? ")
    public void scheduledSyncData() {
        if (log.isDebugEnabled()) {
            log.debug("scheduledSyncData");
        }
        String key = "vocu_sync_extr_dic";
        long time = 2 * 3600 * 1000l; // 2小时
        if (this.redisDao.lock(key, time)) {
            // this.syncData();
        }
    }

    /**
     * 数据同步
     */
    public void syncData() {
        if (log.isDebugEnabled()) {
            log.debug("syncData");
        }
        // 1、同步字典表
        this.extrDictService.syncDict();
        //2、同步domain信息
        this.extrDomainService.syncDomain();
    }
}
