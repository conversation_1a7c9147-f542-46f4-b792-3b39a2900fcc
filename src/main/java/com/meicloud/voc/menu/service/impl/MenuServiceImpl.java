package com.meicloud.voc.menu.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meicloud.voc.company.dto.CompanyMenuInfoBo;
import com.meicloud.voc.group.entity.GroupMenuDetail;
import com.meicloud.voc.group.mapper.GroupMenuDetailMapper;
import com.meicloud.voc.menu.entity.Menu;
import com.meicloud.voc.menu.mapper.MenuMapper;
import com.meicloud.voc.menu.service.IMenuService;
import com.meicloud.voc.security.api.vo.MenuInfoVo;
import com.meicloud.voc.user.dto.MenuInfoBo;
import com.meicloud.voc.user.entity.User;
import com.meicloud.voc.user.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务
 */
 @Slf4j
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {

    @Resource
    private GroupMenuDetailMapper groupMenuDetailMapper;

    @Resource
    private UserMapper userMapper;

    /**
     * 获取用户菜单
     *
     * @param companyId
     * @param userAccount
     * @return
     */
    @Override
    public List<MenuInfoBo> getUserMenu(int companyId, String userAccount) {
        List<Menu> xmyMenuDos = this.getBaseMapper().selectByCompanyAndUser(companyId, userAccount);
        Map<String, List<Menu>> menusGroup = xmyMenuDos.stream().collect(Collectors.groupingBy(Menu::getMenuType));
        List<Menu> select = null;
        for (String key : menusGroup.keySet()) {
            List<Menu> xmyMenuDosList = menusGroup.get(key);
            if (select == null) {
                select = xmyMenuDosList;
                continue;
            }
            if (xmyMenuDosList.size() > select.size()) {
                select = xmyMenuDosList;
            }
        }
        if (select == null) {
            return new ArrayList<MenuInfoBo>();
        }
        return createMenuTree(select);
    }

    /**
     * 查询菜单
     *
     * @param tXmyMenuDos
     * @param xmyMenuDo
     * @return
     */
    private MenuInfoBo findMenu(List<Menu> tXmyMenuDos, Menu xmyMenuDo) {
        MenuInfoBo menuInfoBo = new MenuInfoBo();
        menuInfoBo.setPath(xmyMenuDo.getMenuUrl());
        menuInfoBo.setName(xmyMenuDo.getMenuId());
        menuInfoBo.setMenuId(xmyMenuDo.getMenuId());
        menuInfoBo.setComponent(xmyMenuDo.getComponent());
        menuInfoBo.setMenuType(xmyMenuDo.getMenuType());
        menuInfoBo.setMenuData(xmyMenuDo.getMenuData());
        menuInfoBo.setHidden(xmyMenuDo.getHidden() == 0 ? false : true);
        menuInfoBo.setPage(xmyMenuDo.getIsPage() == 0 ? false : true);
        MenuInfoBo.Meta meta = new MenuInfoBo.Meta();
        meta.setTitle(xmyMenuDo.getMenuName());
        meta.setIcon(xmyMenuDo.getMenuIcon());
        meta.setMenuTag(xmyMenuDo.getMenuTag());
        meta.setMenuTips(xmyMenuDo.getMenuTips());
        meta.setIframeUrl(xmyMenuDo.getIframeUrl());
        menuInfoBo.setMeta(meta);
        List<MenuInfoBo> childMenus = new ArrayList<>();
        List<Menu> menus = tXmyMenuDos.stream()
                .filter(tXmyMenuDo -> xmyMenuDo.getMenuId().equals(tXmyMenuDo.getPMenuId()))
                .sorted(Comparator.comparingInt(Menu::getOrderNum)).collect(Collectors.toList());
        for (Menu menu : menus) {
            childMenus.add(findMenu(tXmyMenuDos, menu));
        }
        menuInfoBo.setChildren(childMenus);
        return menuInfoBo;
    }

    /**
     * 生成菜单树
     *
     * @param menuDos
     * @return
     */
    public List<MenuInfoBo> createMenuTree(List<Menu> menuDos) {
        List<Menu> firstMenus = menuDos.stream().filter(tXmyMenuDo -> "0".equals(tXmyMenuDo.getPMenuId()))
                .sorted(Comparator.comparingInt(Menu::getOrderNum)).collect(Collectors.toList());
        List<MenuInfoBo> menus = new ArrayList<>();
        for (Menu firstMenu : firstMenus) {
            menus.add(findMenu(menuDos, firstMenu));
        }
        return menus;
    }

    /**
     * 删除菜单
     *
     * @param companyId
     * @param menuType
     */
    @Override
    public void deleteMenu(int companyId, String menuType) {
        this.baseMapper.deleteMenu(companyId, menuType);
    }

    /**
     * 保存菜单
     *
     * @param companyId
     * @param menuInfoBos
     * @param parentMenuInfoBo
     */
    @Override
    public void saveMenu(int companyId, List<MenuInfoBo> menuInfoBos, MenuInfoBo parentMenuInfoBo) {
        Date version = new Date();
        int orderNum = 0;
        //循环设信息
        for (MenuInfoBo menuInfoBo : menuInfoBos) {
            Menu xmyMenuDo = new Menu();
            xmyMenuDo.setMenuId(menuInfoBo.getName());
            xmyMenuDo.setPMenuId(parentMenuInfoBo == null ? "0" : parentMenuInfoBo.getName());
            xmyMenuDo.setOrderNum(orderNum++);
            xmyMenuDo.setMenuType(menuInfoBo.getMenuType());
            xmyMenuDo.setCompanyId(companyId);
            xmyMenuDo.setMenuUrl(menuInfoBo.getPath());
            xmyMenuDo.setComponent(menuInfoBo.getComponent());
            xmyMenuDo.setMenuVersion(version);
            xmyMenuDo.setUpdateTime(version);
            xmyMenuDo.setMenuName(menuInfoBo.getMeta().getTitle());
            xmyMenuDo.setMenuIcon(menuInfoBo.getMeta().getIcon());
            xmyMenuDo.setMenuTag(menuInfoBo.getMeta().getMenuTag());
            xmyMenuDo.setDeleteStatus(0);
            xmyMenuDo.setHidden(menuInfoBo.isHidden() ? 1 : 0);
            this.save(xmyMenuDo);
            saveMenu(companyId, menuInfoBo.getChildren(), menuInfoBo);
        }
    }

    /**
     * 获取公司的菜单
     *
     * @param companyId
     * @return
     */
    @Override
    public List<String> getCompanyMenuIdList(int companyId) {
        List<Menu> xmyMenuDos = this.baseMapper.selectByCompany(companyId);
        List<String> menuIds = xmyMenuDos.stream().map(Menu::getMenuId).collect(Collectors.toList());
        return menuIds;
    }

    /**
     * 获取公司的菜单信息
     *
     * @param companyId
     * @return
     */
    @Override
    public CompanyMenuInfoBo getCompanyMenu(int companyId) {
        List<Menu> xmyMenuDos = this.baseMapper.selectByCompany(companyId);
        Map<String, List<Menu>> menusGroup = xmyMenuDos.stream().collect(Collectors.groupingBy(Menu::getMenuType));
        CompanyMenuInfoBo companyMenuInfoBo = new CompanyMenuInfoBo();
        List<String> menuTypes = new ArrayList<>();
        Map<String, List<MenuInfoBo>> menuTypeList = new HashMap<>();
        menusGroup.forEach((key, value) -> {
            menuTypes.add(key);
            menuTypeList.put(key, createMenuTree(value));
        });
        companyMenuInfoBo.setMenuTypeArr(menuTypes);
        companyMenuInfoBo.setMenuTypeList(menuTypeList);
        return companyMenuInfoBo;
    }

    /**
     * 查找菜单
     *
     * @param tXmyMenuDos
     * @param xmyMenuDo
     * @param groupMenuIds
     * @return
     */
    private MenuInfoBo findMenu(List<Menu> tXmyMenuDos, Menu xmyMenuDo, List<String> groupMenuIds) {
        MenuInfoBo menuInfoBo = new MenuInfoBo();
        menuInfoBo.setPath(xmyMenuDo.getMenuUrl());
        menuInfoBo.setName(xmyMenuDo.getMenuId());
        menuInfoBo.setComponent(xmyMenuDo.getComponent());
        menuInfoBo.setMenuType(xmyMenuDo.getMenuType());
        menuInfoBo.setHasAuth(groupMenuIds.contains(xmyMenuDo.getMenuId()) ? 1 : 0);
        menuInfoBo.setHidden(xmyMenuDo.getHidden() == 0 ? false : true);
        menuInfoBo.setPage(xmyMenuDo.getIsPage() == 0 ? false : true);
        MenuInfoBo.Meta meta = new MenuInfoBo.Meta();
        meta.setTitle(xmyMenuDo.getMenuName());
        meta.setIcon(xmyMenuDo.getMenuIcon());
        meta.setMenuTag(xmyMenuDo.getMenuTag());
        meta.setIframeUrl(xmyMenuDo.getIframeUrl());
        menuInfoBo.setMeta(meta);
        List<MenuInfoBo> childMenus = new ArrayList<>();
        List<Menu> menus = tXmyMenuDos.stream()
                .filter(tXmyMenuDo -> xmyMenuDo.getMenuId().equals(tXmyMenuDo.getPMenuId()))
                .sorted(Comparator.comparingInt(Menu::getOrderNum)).collect(Collectors.toList());
        for (Menu menu : menus) {
            childMenus.add(findMenu(tXmyMenuDos, menu, groupMenuIds));
        }
        menuInfoBo.setChildren(childMenus);
        return menuInfoBo;
    }

    /**
     * 创建菜单树
     *
     * @param menuDos
     * @param groupMenuIds
     * @return
     */
    @Override
    public List<MenuInfoBo> createMenuTree(List<Menu> menuDos, List<String> groupMenuIds) {
        List<Menu> firstMenus = menuDos.stream().filter(tXmyMenuDo -> "0".equals(tXmyMenuDo.getPMenuId()))
                .sorted(Comparator.comparingInt(Menu::getOrderNum)).collect(Collectors.toList());
        List<MenuInfoBo> menus = new ArrayList<>();
        for (Menu firstMenu : firstMenus) {
            menus.add(findMenu(menuDos, firstMenu, groupMenuIds));
        }
        return menus;
    }

    /**
     * 获取角色菜单
     *
     * @param companyId
     * @param groupId
     * @return
     */
    @Override
    public CompanyMenuInfoBo getGroupMenu(int companyId, int groupId) {
        List<Menu> xmyMenuDos = this.baseMapper.selectByCompany(companyId);
        List<GroupMenuDetail> groupMenuDetailDos = this.groupMenuDetailMapper
                .selectList(Wrappers.<GroupMenuDetail>query().eq("group_id", groupId));
        List<String> groupMenuIds = groupMenuDetailDos.stream().map(GroupMenuDetail::getMenuId)
                .collect(Collectors.toList());
        Map<String, List<Menu>> menusGroup = xmyMenuDos.stream().collect(Collectors.groupingBy(Menu::getMenuType));
        CompanyMenuInfoBo companyMenuInfoBo = new CompanyMenuInfoBo();
        List<String> menuTypes = new ArrayList<>();
        Map<String, List<MenuInfoBo>> menuTypeList = new HashMap<>();
        menusGroup.forEach((key, value) -> {
            menuTypes.add(key);
            menuTypeList.put(key, createMenuTree(value, groupMenuIds));
        });
        companyMenuInfoBo.setMenuTypeArr(menuTypes);
        companyMenuInfoBo.setMenuTypeList(menuTypeList);
        return companyMenuInfoBo;
    }

    /**
     * getMenuList
     *
     * @param companyId
     * @return
     */
    @Override
    public List<MenuInfoVo> getMenuList(int companyId) {
        List<MenuInfoVo> dataList = new ArrayList<MenuInfoVo>();
        List<Menu> menuList = this.getBaseMapper().selectByCompany(companyId);
        for (Menu menu : menuList) {
            MenuInfoVo infoVo = new MenuInfoVo();
            infoVo.setModelId(menu.getMenuId());
            infoVo.setModelName(menu.getMenuName());
            infoVo.setParentModelCode(menu.getPMenuId());
//			infoVo.setParentModelName();
            dataList.add(infoVo);
        }
        return dataList;
    }

    @Override
    public boolean hasApiPermission(int companyId, String userAccount, String url) {
        log.debug("hasApiPermission: companyId=[{}], userAccount=[{}], url=[{}]", companyId, userAccount, url);
        List<User> userList = userMapper.selectList(Wrappers.<User>query().eq("user_account", userAccount).eq("is_enabled", "1"));
        return userList != null && !userList.isEmpty();
    }

}
