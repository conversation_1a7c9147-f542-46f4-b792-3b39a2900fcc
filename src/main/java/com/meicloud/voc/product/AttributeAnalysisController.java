package com.meicloud.voc.product;

import com.meicloud.voc.car.entity.DwdEvtWorkOrderDtlEntity;
import com.meicloud.voc.car.service.IDmKeywordMentionRegionService;
import com.meicloud.voc.car.service.IDwdEvtDtlService;
import com.meicloud.voc.car.service.impl.BaseServiceImpl;
import com.meicloud.voc.common.dto.PageList;
import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.dto.Result;
import com.meicloud.voc.common.utils.DataUtil;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.ListUtil;
import com.meicloud.voc.common.utils.ResultUtil;
import com.meicloud.voc.product.response.AttributeAnalysisExperienceRes;
import com.meicloud.voc.product.response.FeatureAnalysisRes;
import com.meicloud.voc.product.response.IndexAnalysisRes;
import com.meicloud.voc.product.service.IAttributeAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 本品分析-归因分析
 */
@Api(tags = "本品分析-归因分析")
@RestController
@RequestMapping(value = "/attribute/analysis")
public class AttributeAnalysisController {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private IDwdEvtDtlService dwdEvtDtlService;

    @Autowired
    private IAttributeAnalysisService attributeAnalysisService;

    @Autowired
    private IDmKeywordMentionRegionService dmKeywordMentionRegionService;

    @Autowired
    private BaseServiceImpl baseServiceImpl;


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = DwdEvtWorkOrderDtlEntity.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = ", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    /**
     * voc-体验值-趋势
     */
    @ApiOperation("voc-体验值-趋势")
    @RequestMapping(value = "/vocExperienceTrend", method = {RequestMethod.POST})
    public Result<List<DwdEvtWorkOrderDtlEntity>> vocExperienceTrend(@RequestBody RequestParams requestParams) {
        requestParams.roundDateByDateType();
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.experienceTrend(requestParams);
        return ResultUtil.getSuccess(dwdEvtWorkOrderDtlEntities);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = AttributeAnalysisExperienceRes.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    /**
     * voc-体验值
     */
    @ApiOperation("voc-体验值")
    @RequestMapping(value = "/vocExperience", method = {RequestMethod.POST})
    public Result<AttributeAnalysisExperienceRes> vocExperience(@RequestBody RequestParams requestParams) {
        return ResultUtil.getSuccess(attributeAnalysisService.vocExperience(requestParams));
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = DwdEvtWorkOrderDtlEntity.class),
    })
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    @ApiOperation("TOP问题（标准关键词）")
    @RequestMapping(value = "/topKeywordList", method = {RequestMethod.POST})
    public Result<List<DwdEvtWorkOrderDtlEntity>> topKeywordList(@RequestBody RequestParams requestParams) throws ParseException {
        int returnTopN = requestParams.getTopN();
        //setDynamicTopN(requestParams);
        //计算前10000个
        requestParams.setTopN(10000);
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.keywordList(requestParams);

        //计算环比
        List<String> standardKeywords = dwdEvtWorkOrderDtlEntities.stream().map(DwdEvtWorkOrderDtlEntity::getStandardKeyword).collect(Collectors.toList());
        String momStartTime = DateUtil.getPerStartDate(requestParams.getStartDate(), requestParams.getEndDate(), requestParams.getDateType());
        String momEndTime = DateUtil.getPerEndDate(requestParams.getStartDate());
        requestParams.setStartDate(momStartTime);
        requestParams.setEndDate(momEndTime);
        requestParams.setStandardKeywords(standardKeywords);

        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntitiesMom = dmKeywordMentionRegionService.keywordList(requestParams);
        for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntity : dwdEvtWorkOrderDtlEntities) {
            boolean flag = false;
            for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntityMom : dwdEvtWorkOrderDtlEntitiesMom) {
                if (dwdEvtWorkOrderDtlEntity.getStandardKeyword().equals(dwdEvtWorkOrderDtlEntityMom.getStandardKeyword())) {
                    dwdEvtWorkOrderDtlEntity.setMomTotalMentionValue(dwdEvtWorkOrderDtlEntity.getTotalMentionValue() - dwdEvtWorkOrderDtlEntityMom.getTotalMentionValue());
                    dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getTotalMentionValue(), dwdEvtWorkOrderDtlEntity.getTotalMentionValue()));
                    dwdEvtWorkOrderDtlEntity.setMomNegativeMentionRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getNegativeMentionValue(), dwdEvtWorkOrderDtlEntity.getNegativeMentionValue()));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                dwdEvtWorkOrderDtlEntity.setMomTotalMentionValue(dwdEvtWorkOrderDtlEntity.getStandardKeywordMentionValue());
                dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(0); // 环比变化，如果环比为0 就是0
            }
        }
        ListUtil.sortDwdList(dwdEvtWorkOrderDtlEntities, requestParams.getSortName());
        if (returnTopN > 0) {
            dwdEvtWorkOrderDtlEntities = dwdEvtWorkOrderDtlEntities.stream().limit(returnTopN).collect(Collectors.toList());
        }
        return ResultUtil.getSuccess(dwdEvtWorkOrderDtlEntities);
    }

    /**
     * 根据已有的参数，动态设置查询时的topN
     */
    private void setDynamicTopN(RequestParams requestParams) {
        int topN = requestParams.getTopN();
        // 系数
        double coefficient = 1;
        // 1. 如果有车系，系数*2
        if(CollectionUtils.isNotEmpty(requestParams.getSeriesNames())) {
            coefficient = coefficient * 2;
        }
        // 2. 如果有车型，系数*1.5
        if(CollectionUtils.isNotEmpty(requestParams.getModelNames())) {
            coefficient = coefficient * 1.5;
        }

        // 3. 如果有二级指标，系数*1.5
        if(StringUtils.isNotEmpty(requestParams.getSecondIndexId())) {
            coefficient = coefficient * 1.5;
        }

        // 4. 如果有三级指标，系数*1.5
        if(StringUtils.isNotEmpty(requestParams.getThirdIndexId())) {
            coefficient = coefficient * 1.5;
        }

        // 5. 如果有日期小于180日， 系数*1.5 小于90日，系数*2， 小于30天，系数*3, 小于7天，系数*5
        try {
            long day = DateUtil.betweenDays(requestParams.getStartDate(), requestParams.getEndDate());
            if(day < 7) {
                coefficient = coefficient * 5;
            } else if(day < 30) {
                coefficient = coefficient * 3;
            } else if(day < 90) {
                coefficient = coefficient * 2;
            } else if(day < 180) {
                coefficient = coefficient * 1.5;
            }
        } catch (ParseException e) {

        }

        topN = (int) (topN * coefficient);
        requestParams.setTopN(topN);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = DwdEvtWorkOrderDtlEntity.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    @ApiOperation("数据源分析-渠道构成-数据源分组")
    @RequestMapping(value = "/dataSourceConsitute", method = {RequestMethod.POST})
    public Result<List<DwdEvtWorkOrderDtlEntity>> dataSourceConsitute(@RequestBody RequestParams requestParams) {

        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.dataSourceAgg(requestParams);
        return ResultUtil.getSuccess(dwdEvtWorkOrderDtlEntities);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = DwdEvtWorkOrderDtlEntity.class),
    })
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
    })*/
    @ApiOperation("数据源分析-渠道构成-提及量趋势")
    @RequestMapping(value = "/mentionValueTrend", method = {RequestMethod.POST})
    public Result<List<DwdEvtWorkOrderDtlEntity>> mentionValueTrend(@RequestBody RequestParams requestParams) {
        requestParams.roundDate();
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.experienceTrend(requestParams);
        return ResultUtil.getSuccess(dwdEvtWorkOrderDtlEntities);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = Map.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
    })*/
    @ApiOperation("数据源分析-渠道构成-词云")
    @RequestMapping(value = "/wordCloud", method = {RequestMethod.POST})
    public Result<Map<String, Object>> wordCloud(@RequestBody RequestParams requestParams) {
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.keywordList(requestParams);
        Map<String, Object> wordCloudMap = new HashMap<>();
        for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntity : dwdEvtWorkOrderDtlEntities) {
            int wordCount = (int) wordCloudMap.getOrDefault(dwdEvtWorkOrderDtlEntity.getStandardKeyword(), 0);
            wordCount += dwdEvtWorkOrderDtlEntity.getPositiveMentionValue() + dwdEvtWorkOrderDtlEntity.getNegativeMentionValue() + dwdEvtWorkOrderDtlEntity.getNeutralMentionValue();
            wordCloudMap.put(dwdEvtWorkOrderDtlEntity.getStandardKeyword(), wordCount);
        }
        return ResultUtil.getSuccess(wordCloudMap);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = DwdEvtWorkOrderDtlEntity.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    @ApiOperation("地域分析")
    @RequestMapping(value = "/areaCompare", method = {RequestMethod.POST})
    public Result<List<DwdEvtWorkOrderDtlEntity>> areaCompare(@RequestBody RequestParams requestParams) throws ParseException {
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.provinceAgg(requestParams);
        //计算环比
        String momStartTime = DateUtil.getPerStartDate(requestParams.getStartDate(), requestParams.getEndDate(), requestParams.getDateType());
        String momEndTime = DateUtil.getPerEndDate(requestParams.getStartDate());
        requestParams.setStartDate(momStartTime);
        requestParams.setEndDate(momEndTime);
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntitiesMom = dmKeywordMentionRegionService.provinceAgg(requestParams);
        for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntity : dwdEvtWorkOrderDtlEntities) {
            boolean flag = false;
            for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntityMom : dwdEvtWorkOrderDtlEntitiesMom) {
                if (dwdEvtWorkOrderDtlEntity.getProvince().equals(dwdEvtWorkOrderDtlEntityMom.getProvince())) {
                    dwdEvtWorkOrderDtlEntity.setMomExperienceValueRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getExperienceValue(), dwdEvtWorkOrderDtlEntity.getExperienceValue()));
                    dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getTotalMentionValue(), dwdEvtWorkOrderDtlEntity.getTotalMentionValue()));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                dwdEvtWorkOrderDtlEntity.setMomExperienceValueRate(0);
                dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(0);
                dwdEvtWorkOrderDtlEntity.setMomNegativeMentionRate(0);
            }
        }
        return ResultUtil.getSuccess(dwdEvtWorkOrderDtlEntities);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = IndexAnalysisRes.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    @ApiOperation("指标分析")
    @RequestMapping(value = "/indexAnalysis", method = {RequestMethod.POST})
    public Result<IndexAnalysisRes> indexAnalysis(@RequestBody RequestParams requestParams) throws ParseException {
        // 获取树结构值
        List<DwdEvtWorkOrderDtlEntity> treeData = attributeAnalysisService.getIndexAnalysisTreeData(requestParams);
        //查询表格数据
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntities = dmKeywordMentionRegionService.experienceAggIndex(requestParams);
        //计算环比
        String momStartTime = DateUtil.getPerStartDate(requestParams.getStartDate(), requestParams.getEndDate(), requestParams.getDateType());
        String momEndTime = DateUtil.getPerEndDate(requestParams.getStartDate());
        requestParams.setStartDate(momStartTime);
        requestParams.setEndDate(momEndTime);
        List<DwdEvtWorkOrderDtlEntity> dwdEvtWorkOrderDtlEntitiesMom = dmKeywordMentionRegionService.experienceAggIndex(requestParams);
        for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntity : dwdEvtWorkOrderDtlEntities) {
            boolean flag = false;
            for (DwdEvtWorkOrderDtlEntity dwdEvtWorkOrderDtlEntityMom : dwdEvtWorkOrderDtlEntitiesMom) {
                if (dwdEvtWorkOrderDtlEntity.getIndexName().equals(dwdEvtWorkOrderDtlEntityMom.getIndexName())) {
                    dwdEvtWorkOrderDtlEntity.setMomExperienceValueRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getExperienceValue(), dwdEvtWorkOrderDtlEntity.getExperienceValue()));
                    dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getTotalMentionValue(), dwdEvtWorkOrderDtlEntity.getTotalMentionValue()));
                    dwdEvtWorkOrderDtlEntity.setMomNegativeMentionRate(DataUtil.getCycleRate(dwdEvtWorkOrderDtlEntityMom.getNegativeMentionRate(), dwdEvtWorkOrderDtlEntity.getNegativeMentionRate()));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                dwdEvtWorkOrderDtlEntity.setMomExperienceValueRate(0);
                dwdEvtWorkOrderDtlEntity.setMomTotalMentionValueRate(0);
                dwdEvtWorkOrderDtlEntity.setMomNegativeMentionRate(0);
            }
        }

        IndexAnalysisRes indexAnalysisRes = new IndexAnalysisRes();
        indexAnalysisRes.setDwdEvtWorkOrderDtls(dwdEvtWorkOrderDtlEntities);
        indexAnalysisRes.setDwdEvtWorkOrderDtlsMom(dwdEvtWorkOrderDtlEntitiesMom);
        indexAnalysisRes.setTreeData(treeData);
        return ResultUtil.getSuccess(indexAnalysisRes);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "ok", response = FeatureAnalysisRes.class),
    })
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexIds", value = "指标id，逗号隔开", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "indexLevel", value = "指标等级", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "dataSource", value = "数据源", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "marketName", value = "车系级别、细分市场", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "seriesName", value = "车系名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "carOwner", value = "是否车主(0: 不限  1：车主  2：非车主)", required = false, paramType = "query", dataType = "int", defaultValue = ""),
            @ApiImplicitParam(name = "modelCode", value = "车型版本代码", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "地域", required = false, paramType = "query", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "brandName", value = "品牌名称", required = false, paramType = "query", dataType = "String", defaultValue = ""),

    })*/
    @ApiOperation("人群特征分析")
    @RequestMapping(value = "/featureAnalysis", method = {RequestMethod.POST})
    public Result<Map<String, Object>> featureAnalysis(@RequestBody RequestParams requestParams) {
        //FeatureAnalysisRes featureAnalysisRes = dwdEvtWorkOrderDtlService.featureAnalysis(requestParams);
        try {
            Map<String, Object> resultMap = baseServiceImpl.getCrowdFeatures(requestParams, null);
            return ResultUtil.getSuccess(resultMap);
        } catch (Exception e) {
            logger.error("人群特征分析失败", e);
            return ResultUtil.getError("人群特征分析失败");
        }
    }

    @ApiOperation("原文明细")
    @RequestMapping(value = "/originalDetails", method = {RequestMethod.POST})
    public Result<PageList<Map<String, Object>>> originalDetails(@RequestBody RequestParams requestParams) {
        PageList<Map<String, Object>> dwdEvtDlEntityList = dwdEvtDtlService.detailsForAttribute(requestParams);
        return ResultUtil.getSuccess(dwdEvtDlEntityList);
    }
}

