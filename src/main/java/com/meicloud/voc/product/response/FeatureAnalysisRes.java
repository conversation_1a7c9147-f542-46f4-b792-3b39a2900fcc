package com.meicloud.voc.product.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@ApiModel
public class FeatureAnalysisRes {

    /**
     * 人群总量
     */
    @ApiModelProperty("人群总量")
    private long totalCustomer;

    /**
     * 客户类型占比
     */
    @ApiModelProperty("客户类型占比")
    private Map<String, Object> customerType = new HashMap<>();

    @ApiModelProperty("省份人群占比")
    private Map<String, Object> provinceMap = new HashMap<>();

    @ApiModelProperty("性别占比")
    private Map<String, Object> sexMap = new HashMap<>();

    @ApiModelProperty("年龄占比")
    private Map<String, Object> ageMap = new HashMap<>();

    @ApiModelProperty("车系占比")
    private Map<String, Object> seriesNameMap = new HashMap<>();

    @ApiModelProperty("城市等级占比(key为城市等级，value为占比；城市等级为0为未知等级城市)")
    private Map<String, Double> cityLevelMap = new HashMap<>();
}
