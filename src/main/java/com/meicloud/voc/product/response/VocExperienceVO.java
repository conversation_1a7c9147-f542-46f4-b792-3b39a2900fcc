package com.meicloud.voc.product.response;

import com.meicloud.voc.car.entity.DwsKeywordMentionDay;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 声量VO
 */
@Data
@ApiModel
public class VocExperienceVO {
    /**
     * 品牌voc体验值列表
     */
    @ApiModelProperty("品牌voc体验值列表")
    private List<DwsKeywordMentionDay> experienceList;
    /**
     * 内部平均体验值列表
     */
    @ApiModelProperty("内部平均体验值列表")
    private List<DwsKeywordMentionDay> intervalAvgList;

}
