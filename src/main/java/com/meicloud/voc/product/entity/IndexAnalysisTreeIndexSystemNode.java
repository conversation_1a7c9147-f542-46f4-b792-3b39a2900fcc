package com.meicloud.voc.product.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;


/**
 * 归因分析-指标体系树节点
 */
@Data
public class IndexAnalysisTreeIndexSystemNode {
    private String indexId; // 节点id
    private String indexName; // 节点名
    private Integer level; // 节点等级
    private Map<String, IndexAnalysisTreeIndexSystemNode> children; // 子节点

    public IndexAnalysisTreeIndexSystemNode() {
        children = new HashMap<>();
    }
}
