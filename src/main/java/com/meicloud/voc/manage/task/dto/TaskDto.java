package com.meicloud.voc.manage.task.dto;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.meicloud.voc.utils.Pair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * task请求实体类
 *
 * <AUTHOR>
 * @since 2022-04-21 20:27:19
 */
@Data
@ApiModel(value = "task请求实体类")
public class TaskDto implements Serializable {
    private static final long serialVersionUID = -52506336219866719L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long dataId;
    /**
     * 任务名称id
     */
    @ApiModelProperty(value = "任务名称id")
    private String taskId;
    /**
     * 任务域id
     */
    @ApiModelProperty(value = "任务域id")
    private String domainId;
    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    private String taskName;
    /**
     * 父任务id
     */
    @ApiModelProperty(value = "父任务id")
    private String parentTaskId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Long planBeginDate;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Long planFinishDate;
    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    private Pair<String,String> dutyLogin;
    /**
     * 支持人工号数组
     */
    @ApiModelProperty(value = "支持人工号数组")
    private List<Pair<String, String>> supportLoginIdList;
    /**
     * 阅读人工号数组
     */
    @ApiModelProperty(value = "阅读人工号数组")
    private List<Pair<String, String>> readLoginIdList;
    /**
     * 确认人工号
     */
    @ApiModelProperty(value = "确认人工号")
    private Pair<String,String> confirmLogin;
    /**
     * 助理人工号
     */
    @ApiModelProperty(value = "助理人工号")
    private Pair<String,String> assistantLogin;
    /**
     * 任务目标
     */
    @ApiModelProperty(value = "任务目标")
    private String target;
    /**
     * 交付物
     */
    @ApiModelProperty(value = "交付物")
    private String delivery;
    /**
     * 关闭要素
     */
    @ApiModelProperty(value = "关闭要素")
    private String closeCondition;
    /**
     * 任务来源
     */
    @ApiModelProperty(value = "任务来源")
    private String taskSource;
    /**
     * 任务来源编码（公文的发文编号）
     */
    @ApiModelProperty(value = "任务来源编码（公文的发文编号）")
    private String sourceCode;
    /**
     * 任务来源类型: 0公司发文、1部门发文
     */
    @ApiModelProperty(value = "任务来源类型: 0公司发文、1部门发文")
    private Integer sourceType;
    /**
     * 任务来源关联id
     */
    @ApiModelProperty(value = "任务来源关联id")
    private String sourceRefId;
    /**
     * 报告类型值
     */
    @ApiModelProperty(value = "报告类型值")
    private Integer reportTypeValue;
    /**
     * 报告选项值
     */
    @ApiModelProperty(value = "报告选项值")
    private Integer reportChoiceValue;
    /**
     * 任务的路径链接
     */
    @ApiModelProperty(value = "任务的路径链接")
    private String taskPath;
    /**
     * 任务请求参数
     */
    @ApiModelProperty(value = "任务请求参数")
    private String taskRequestParams;
    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String jobName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date wInsertDt;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date wPdateDt;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date batchDt;
    /**
     * 状态，有效: 1, 无效: -1
     */
    @ApiModelProperty(value = "状态，有效: 1, 无效: -1")
    private String status;

    /**
     * 分配人id
     */
    @ApiModelProperty(value = "分配人")
    private Pair<String, String> allocator;

    /**
     * 牵头部门id
     */
    @ApiModelProperty(value = "牵头部门")
    private Pair<String, String> department;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private String taskType;

    /**
     * 订阅Id
     */
    @ApiModelProperty(value = "订阅id")
    private Long subscribeId;

}

