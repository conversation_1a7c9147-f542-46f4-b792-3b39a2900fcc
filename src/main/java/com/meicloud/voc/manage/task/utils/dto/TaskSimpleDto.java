package com.meicloud.voc.manage.task.utils.dto;

import lombok.Data;

import java.util.List;

@Data
public class TaskSimpleDto {
    /**
     * 任务名称id
     */
    private String taskId;
    /**
     * 任务域id
     */
    private String domainId;
    /**
     * 任务描述
     */
    private String taskName;
    /**
     * 父任务id
     */
    private String parentTaskId;
    /**
     * 计划开始时间
     */
    private Long planBeginDate;
    /**
     * 计划结束时间
     */
    private Long planFinishDate;
    /**
     * 责任人工号
     */
    private String dutyLoginId;
    /**
     * 支持人工号数组
     */
    private List<String> supportLoginIds;
    /**
     * 阅读人工号数组
     */
    private List<String> readLoginIds;
    /**
     * 确认人工号
     */
    private String confirmLoginId;
    /**
     * 助理人工号
     */
    private String assistantLoginId;
    /**
     * 任务目标
     */
    private String target;
    /**
     * 交付物
     */
    private String delivery;
    /**
     * 关闭要素
     */
    private String closeCondition;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 任务来源编码（公文的发文编号）
     */
    private String sourceCode;
    /**
     * 任务来源类型: 0公司发文、1部门发文
     */
    private Integer sourceType;
    /**
     * 任务来源关联id
     */
    private String sourceRefId;
    /**
     * 报告类型值
     */
    private Integer reportTypeValue;
    /**
     * 报告选项值
     */
    private Integer reportChoiceValue;
}
