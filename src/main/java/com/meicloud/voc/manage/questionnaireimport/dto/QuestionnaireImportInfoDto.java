package com.meicloud.voc.manage.questionnaireimport.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QuestionnaireImportInfoDto implements Serializable {
    private static final long serialVersionUID = 156185778007562180L;
    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 13)
    private String mobile;
    /**
     * 车架号
     */
    @ExcelProperty(value = "vin")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 13)
    private String vin;
    /**
     * 问题
     */
    @ExcelProperty(value = "问题(必填)")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 48)
    private String question;
    /**
     * 答案
     */
    @ExcelProperty(value = "回答(必填)")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 48)
    private String answer;
    /**
     * 答题时间
     */
    @ExcelProperty(value = "答题时间(必填)")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 48)
    private String answerTime;
    /**
     * 答题人
     */
    @ExcelProperty(value = "答题人")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 13)
    private String userName;
    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 13)
    private String brandName;
    /**
     * 车系名称
     */
    @ExcelProperty(value = "车系")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 13)
    private String seriesName;

    /**
     * 标准关键词
     */
    @ExcelProperty(value = "标准关键词(必填)")
    @HeadFontStyle(fontHeightInPoints = 11, bold =  BooleanEnum.TRUE)
    @HeadStyle(fillForegroundColor = 48)
    private String standardKeyword;

}
