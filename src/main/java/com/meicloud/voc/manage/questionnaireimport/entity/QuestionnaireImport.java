package com.meicloud.voc.manage.questionnaireimport.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 线下问卷导入批次表(QuestionnaireImport)数据库类
 *
 * <AUTHOR>
 * @since 2022-04-28 10:53:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dim_voc3_questionnaire_import")
public class QuestionnaireImport implements Serializable {
    private static final long serialVersionUID = 562428054397596479L;
    /**
     * id
     */
    @TableId(value = "data_id", type = IdType.AUTO)
    private Long dataId;
    /**
     * 创建时间
     */
    private Date wInsertDt;
    /**
     * 最后更新时间
     */
    private Date wPdateDt;
    /**
     * 状态，有效: 1, 无效: -1
     */
    private String status;
    /**
     * 导入日期
     */
    private String importDate;
    /**
     * 操作人ID
     */
    private String userId;
    /**
     * 操作人名称
     */
    private String userName;
    /**
     * 操作人账号
     */
    private String userAccount;
    /**
     * 导入成功数
     */
    private Long successCount;
    /**
     * 导入失败数
     */
    private Long failCount;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件下载地址
     */
    private String fileLink;
    /**
     * 失败数据下载地址
     */
    private String failFileLink;

    /**
     * 任务状态，上传文件成功: 1, 执行中: 2, 执行完成: 3, 执行失败: -1
     */
    private String jobStatus;

    /**
     * 失败信息
     */
    private String errorMsg;
}

