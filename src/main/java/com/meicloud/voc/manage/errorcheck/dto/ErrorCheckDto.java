package com.meicloud.voc.manage.errorcheck.dto;

import java.util.Date;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 纠错清单(ErrorCheck)实体类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:40:47
 */
@Data
@ApiModel(value = "纠错清单")
public class ErrorCheckDto implements Serializable {
    private static final long serialVersionUID = 715473887003565131L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long dataId;
    /**
     * 关键词id
     */
    @ApiModelProperty(value = "关键词id")
    private Long keywordId;
    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词")
    private String keyword;
    /**
     * 标准关键词id
     */
    @ApiModelProperty(value = "标准关键词id")
    private Long standardKeywordId;
    /**
     * 原文id
     */
    @ApiModelProperty(value = "原文id")
    private String sourceId;

    @ApiModelProperty(value = "原文数据源")
    private String source;
    /**
     * 纠错类型
     */
    @ApiModelProperty(value = "纠错类型")
    private String errorType;
    /**
     * 纠错内容
     */
    @ApiModelProperty(value = "纠错内容")
    private String errorDescribe;
    /**
     * 纠错人id
     */
    @ApiModelProperty(value = "纠错人id")
    private String correctorId;
    /**
     * 纠错人名称
     */
    @ApiModelProperty(value = "纠错人名称")
    private String correctorName;
    /**
     * 纠错状态
     */
    @ApiModelProperty(value = "纠错状态")
    private String correctStatus;
    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见")
    private String handleOpinions;

    @ApiModelProperty(value = "处理类型")
    private String handleType;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String jobName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date wInsertDt;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date wPdateDt;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date batchDt;
    /**
     * 状态，有效: 1, 无效: -1
     */
    @ApiModelProperty(value = "状态，有效: 1, 无效: -1")
    private String status;
    /**
     * 标准关键词
     */
    @ApiModelProperty(value = "标准关键词")
    private String standardKeywordName;
    /**
     * 情感
     */
    @ApiModelProperty(value = "情感")
    private String standardKeywordType;

    /**
     * 最后修改人Id
     */
    @ApiModelProperty(value = "最后修改人Id")
    private String lastModifierId;
}

