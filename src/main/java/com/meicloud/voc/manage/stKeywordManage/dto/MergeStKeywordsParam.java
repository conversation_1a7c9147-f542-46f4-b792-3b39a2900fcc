package com.meicloud.voc.manage.stKeywordManage.dto;

import com.meicloud.voc.manage.indexSystemManage.dto.IndexSystemItemResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 指标体系管理
 * </p>
 *
 * <AUTHOR> ouyang
 * @since 2022-05-06
 */
@Data
@ApiModel("标准关键词合并")
public class MergeStKeywordsParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合并选中的那个节点")
    private StKeywordResult mergeNode;

    @ApiModelProperty("需要合并的分类列表（不包含选中的那个节点）")
    private List<StKeywordResult> otherNodes;

}
