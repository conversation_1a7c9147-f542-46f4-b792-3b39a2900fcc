package com.meicloud.voc.manage.stKeywordManage.dto;

import com.meicloud.voc.common.dto.Page;
import com.meicloud.voc.manage.common.BaseParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 标准关键词明细查询条件
 * @Date 2022-5-24 
 * @Version 1.0.0
 */
@Data
@ApiModel("标准关键词明细查询条件")
public class StKeywordParams extends BaseParams implements Serializable {

	//标准关键词,多个用“|”分割
    @ApiModelProperty("标准关键词,多个用“|”分割")
    private String names;

    //责任部门
    @ApiModelProperty("责任部门")
    private String department;

    //责任人
    @ApiModelProperty("责任人")
    private String charge;

    //导出文件名称（导出时使用）
    @ApiModelProperty(value = "导出文件名称（导出时使用）", required = false)
    private String exportFileName;

    private String parentIdPath;

    @ApiModelProperty(value = "指标体系反选", required = false)
    private boolean indexReverseFilter;

}
