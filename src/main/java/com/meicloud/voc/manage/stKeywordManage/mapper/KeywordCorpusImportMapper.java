package com.meicloud.voc.manage.stKeywordManage.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meicloud.voc.manage.stKeywordManage.dto.KeywordCorpusParams;
import com.meicloud.voc.manage.stKeywordManage.entity.KeywordCorpusImport;
import com.meicloud.voc.manage.stKeywordManage.vo.KeywordCorpusVo;

/**
 *	新词训练批量导入记录表
 * @Date 2022-5-9 
 * @Version 1.0.0
 */
public interface KeywordCorpusImportMapper extends BaseMapper<KeywordCorpusImport> {

	IPage<KeywordCorpusImport> getFileListByParams(IPage<KeywordCorpusImport> page, @Param("corpus") KeywordCorpusParams corpusParams);
	
	int deleteFileByIds(@Param("dataId") String dataId);
	
	int updateByDataId(KeywordCorpusImport corpusImport);
}

