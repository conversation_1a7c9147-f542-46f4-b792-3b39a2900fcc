package com.meicloud.voc.manage.stKeywordManage.enums;

public enum FieldTypeEnum {
    PRODUCT_DESIGN("产品设计"),
    PRODUCT_QUALITY("产品质量"),
    MARKETING_SERVICES("营销服务"),
    ;
    private String value;

    FieldTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static FieldTypeEnum getEnum(String value) {
        for (FieldTypeEnum fieldTypeEnum : FieldTypeEnum.values()) {
            if (fieldTypeEnum.getValue().equals(value)) {
                return fieldTypeEnum;
            }
        }
        return null;
    }
}
