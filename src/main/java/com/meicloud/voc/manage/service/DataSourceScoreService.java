package com.meicloud.voc.manage.service;

import com.meicloud.voc.manage.datasourcescore.dto.DataSourceScoreDto;
import com.meicloud.voc.manage.datasourcescore.dto.DataSourceScoreSearchParamsDto;
import com.meicloud.voc.common.dto.PageList;
import com.meicloud.voc.common.dto.QueryPageDto;
import com.meicloud.voc.manage.datasourcescore.entity.DataSourceScore;

import java.util.List;

/**
 * 数据源权重表(DataSourceScore)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-17 20:56:10
 */

public interface DataSourceScoreService {

    /**
     * 分页查询
     *
     * @param queryDto 筛选条件
     * @return 查询结果
     */
    PageList<DataSourceScoreDto> page(QueryPageDto<DataSourceScoreSearchParamsDto> queryDto);

    /**
     * 通过主键查询单条数据
     *
     * @param dataId 主键
     * @return 单条数据
     */
    DataSourceScoreDto findById(Long dataId);

    /**
     * 创建数据
     *
     * @param dataSourceScoreDto 实体
     * @return 新增结果
     */
    DataSourceScoreDto create(DataSourceScoreDto dataSourceScoreDto);

    /**
     * 更新数据
     *
     * @param dataSourceScoreDto 实体
     * @return 更新数据
     */
    DataSourceScoreDto update(DataSourceScoreDto dataSourceScoreDto);

    /**
     * 删除数据
     *
     * @param dataId 主键
     * @return 删除是否成功
     */
    boolean deleteById(Long dataId);

    /**
     * 通过主键设置数据状态为无效
     *
     * @param dataId 主键
     * @return 根据status更新状态
     */
    boolean removeLogicalById(Long dataId);

    /**
     * 获取打分数据源
     *
     */
    List<DataSourceScoreDto> getAllDataSourceScore();

    /**
     * 根据batchId获取打分表记录
     */
    List<DataSourceScoreDto> getDataSourceScoreByBatchId(Long batchId);


}

