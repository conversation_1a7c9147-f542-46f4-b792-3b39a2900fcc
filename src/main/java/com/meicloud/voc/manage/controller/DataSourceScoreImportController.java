package com.meicloud.voc.manage.controller;

import com.alibaba.excel.util.StringUtils;
import com.meicloud.voc.car.enums.DateFormatEnum;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.SessionUtil;
import com.meicloud.voc.manage.datasourcescore.dto.DataSourceScoreDto;
import com.meicloud.voc.manage.datasourcescoredetail.dto.DataSourceScoreDetailDto;
import com.meicloud.voc.manage.datasourcescoreimport.dto.DataSourceScoreImportDto;
import com.meicloud.voc.manage.datasourcescoreimport.dto.DataSourceScoreImportSearchParamsDto;
import com.meicloud.voc.manage.datasourcescoreimport.dto.DataSourceScoreMatrixItemDto;
import com.meicloud.voc.manage.enums.JobStatusEnum;
import com.meicloud.voc.manage.service.DataSourceScoreImportService;
import com.meicloud.voc.common.dto.PageList;
import com.meicloud.voc.common.dto.QueryPageDto;
import com.meicloud.voc.common.dto.Result;
import com.meicloud.voc.common.utils.ResultUtil;
import com.meicloud.voc.utils.Pair;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 数据源评分导入记录(DataSourceScoreImport)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-28 14:28:01
 */
@Api(tags = "数据源评分导入记录")
@RestController
@RequestMapping("/api/m/data-source-score-import")
public class DataSourceScoreImportController {
    private static final Logger logger = LoggerFactory.getLogger(DataSourceScoreImportController.class);

    /**
     * 服务对象
     */
    @Autowired
    private DataSourceScoreImportService dataSourceScoreImportService;

    @Value("${file.upload.path}")
    private String uploadPath;

    /**
     * 分页查询
     *
     * @param queryDto 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "根据分页条件获取实体列表", httpMethod = "POST", notes = "根据分页条件获取实体列表")
    public Result<PageList<DataSourceScoreImportDto>> queryByPage(@RequestBody QueryPageDto<DataSourceScoreImportSearchParamsDto> queryDto) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.page(queryDto));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param dataId 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查看实体", httpMethod = "GET", notes = "根据ID查看实体")
    public Result<DataSourceScoreImportDto> findById(@PathVariable("id") Long dataId) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.findById(dataId));
    }

    /**
     * 创建数据
     *
     * @param dataSourceScoreImportDto 实体
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation(value = "根据ID创建实体", httpMethod = "POST", notes = "根据ID创建实体")
    public Result<DataSourceScoreImportDto> create(@RequestBody DataSourceScoreImportDto dataSourceScoreImportDto) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.create(dataSourceScoreImportDto));
    }

    /**
     * 更新数据
     *
     * @param dataSourceScoreImportDto 实体
     * @return 更新数据
     */
    @PutMapping
    @ApiOperation(value = "根据ID更新实体", httpMethod = "PUT", notes = "根据ID更新实体")
    public Result<DataSourceScoreImportDto> update(@RequestBody DataSourceScoreImportDto dataSourceScoreImportDto) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.update(dataSourceScoreImportDto));
    }

    /**
     * 删除数据
     *
     * @param dataId 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据ID删除实体", httpMethod = "DELETE", notes = "根据ID删除实体")
    public Result<Boolean> deleteById(@PathVariable("id") Long dataId) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.deleteById(dataId));
    }

    /**
     * 删除数据
     *
     * @param dataId 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/removeLogicalById/{id}")
    @ApiOperation(value = "根据ID删除实体", httpMethod = "DELETE", notes = "根据ID删除实体")
    public Result<Boolean> removeLogicalById(@PathVariable("id") Long dataId) {
        return ResultUtil.getSuccess(this.dataSourceScoreImportService.removeLogicalById(dataId));
    }

    /**
     * 上传评分文件
     */
    @ApiOperation("上传评分文件")
    @RequestMapping(value = "/uploadFile", method = {RequestMethod.POST})
    public Result<DataSourceScoreImportDto> uploadFile(@RequestParam("uploadFile") MultipartFile uploadFile, HttpServletRequest request) {
        String fileName = uploadFile.getOriginalFilename();
        String filePath = uploadPath + File.separator + System.currentTimeMillis()+ fileName;
        File file = new File(filePath);
        try {
            uploadFile.transferTo(file);
        } catch (IOException e) {
            logger.error("上传文件失败：", e);
            return ResultUtil.getError("上传失败");
        }

        DataSourceScoreImportDto dataSourceScoreImportDto = new DataSourceScoreImportDto();
        dataSourceScoreImportDto.setFileName(fileName);
        dataSourceScoreImportDto.setUserId(SessionUtil.getUserName(request));
        dataSourceScoreImportDto.setUserName(SessionUtil.getUserChineseName(request));
        dataSourceScoreImportDto.setJobStatus(JobStatusEnum.UPLOAD_SUCCESS.getValue());
        String importDate = DateFormatEnum.YYYY_MM_DD.getSimpleDateFormat().format(new Date());
        dataSourceScoreImportDto.setImportDate(importDate);

        dataSourceScoreImportDto = dataSourceScoreImportService.create(dataSourceScoreImportDto);
        if(dataSourceScoreImportDto.getDataId() != null) {
            dataSourceScoreImportService.handleUploadFile(dataSourceScoreImportDto, file);
        }

        return ResultUtil.getSuccess(dataSourceScoreImportDto);
    }

    @ApiOperation("获取打分表详情-矩阵")
    @RequestMapping(value = "/getImportDetails/{dataId}", method = {RequestMethod.GET})
    public Result<List<DataSourceScoreMatrixItemDto>> getDataSourceSourceScoreImportDetail(@PathVariable("dataId") Long dataId) {
        return ResultUtil.getSuccess(dataSourceScoreImportService.getDataSourceSourceScoreImportDetail(dataId));
    }

    @ApiOperation("打分表详情的应用")
    @RequestMapping(value = "/apply/{dataId}", method = {RequestMethod.PUT})
    public Result<Boolean> apply(@PathVariable("dataId") Long dataId, @RequestParam("type") String type) {
        return ResultUtil.getSuccess(dataSourceScoreImportService.apply(dataId, type));
    }


    @ApiOperation("计算打分详情表")
    @RequestMapping(value = "/calSourceScore/{dataId}", method = {RequestMethod.GET})
    public Result<String> calSourceScore(@PathVariable("dataId") Long dataId) {
        String result = dataSourceScoreImportService.calculateDataSourceScore(dataId);
        if(StringUtils.isNotBlank(result)) {
            return ResultUtil.getSuccess(result);
        }
        return ResultUtil.getError("计算打分失败");
    }

    @ApiOperation("获取最新的暂存数据")
    @RequestMapping(value = "/getDataSourceScoreTmp", method = {RequestMethod.GET})
    public Result<List<DataSourceScoreDto>> getDataSourceScoreTmp() {
        return ResultUtil.getSuccess(dataSourceScoreImportService.getDataSourceScoreTmp());
    }

    /**
     * 获取数据源下载模板
     * @param response
     */
    @ApiOperation("下载模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        dataSourceScoreImportService.genDownloadTemplate(response);
    }
}

