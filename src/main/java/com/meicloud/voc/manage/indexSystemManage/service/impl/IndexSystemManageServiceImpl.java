package com.meicloud.voc.manage.indexSystemManage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meicloud.voc.car.enums.DateFormatEnum;
import com.meicloud.voc.car.service.IIndexSystemService;
import com.meicloud.voc.common.constance.Status;
import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.ESUtil;
import com.meicloud.voc.log.enums.OperationEnum;
import com.meicloud.voc.log.enums.OperationPageEnum;
import com.meicloud.voc.manage.indexSystemManage.dto.*;
import com.meicloud.voc.manage.indexSystemManage.entity.IndexSystemItemManage;
import com.meicloud.voc.manage.indexSystemManage.entity.IndexSystemManage;
import com.meicloud.voc.manage.indexSystemManage.mapper.IndexSystemManageMapper;
import com.meicloud.voc.manage.indexSystemManage.service.IIndexSystemManageService;
import com.meicloud.voc.user.dto.UserInfoBo;
import com.meicloud.voc.user.service.IUserService;
import com.meicloud.voc.utils.OperationLogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 指标体系管理 服务实现类
 * </p>
 *
 * <AUTHOR> ouyang
 * @since 2022-05-06
 */
@Service
public class IndexSystemManageServiceImpl extends ServiceImpl<IndexSystemManageMapper, IndexSystemManage> implements IIndexSystemManageService {
    /**
     * 用户服务
     */
    @Autowired
    private IUserService userService;
    /**
     * 指标体系管理服务
     */
    @Autowired
    private IndexSystemItemManageServiceImpl indexSystemItemManageService;

    /**
     * 指标体系服务
     */
    @Autowired
    private IIndexSystemService indexSystemService;

    @Value("${file.tmp.path:./temp/}")
    private String tempPath;

    private static final String MSG_TEMP = "%s:%s";

    /**
     * 获取指标体系列表
     *
     * @return
     */
    @Override
    public List<IndexSystemManage> getIndexSystemList() {
        LambdaQueryWrapper<IndexSystemManage> queryWrapper = new QueryWrapper<IndexSystemManage>().lambda();
        queryWrapper.orderByAsc(IndexSystemManage::getDataId);
        queryWrapper.eq(IndexSystemManage::getStatus, "1");
        return list(queryWrapper);
    }

    /**
     * 根据参数返回指标体系列表
     *
     * @param indexSystemManage
     * @return
     */
    @Override
    public List<IndexSystemManage> getIndexSystemListByParam(IndexSystemManage indexSystemManage) {
        LambdaQueryWrapper<IndexSystemManage> queryWrapper = new QueryWrapper<IndexSystemManage>().lambda();
        queryWrapper.orderByAsc(IndexSystemManage::getDataId);
        if (indexSystemManage != null) {
            if (indexSystemManage.getDataId() != null && indexSystemManage.getDataId() >= 0) {
                queryWrapper.eq(IndexSystemManage::getDataId, indexSystemManage.getDataId());
            }

            if (StringUtils.isNotBlank(indexSystemManage.getIndexTypeName())) {
                queryWrapper.eq(IndexSystemManage::getIndexTypeName, indexSystemManage.getIndexTypeName());
            }
        }
        queryWrapper.eq(IndexSystemManage::getStatus, "1");
        return list(queryWrapper);
    }


    /**
     * 新增指标体系
     *
     * @param indexSystemManage
     * @return
     */
    @Override
    public Boolean addIndexSystem(IndexSystemManage indexSystemManage) {
        indexSystemManage.setBatchDt(LocalDateTime.now());
        indexSystemManage.setWPdateDt(LocalDateTime.now());
        indexSystemManage.setWInsertDt(LocalDateTime.now());
        indexSystemManage.setStatus("1");
        indexSystemManage.setEnabled("-1");
        OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.ADD,
                String.format(MSG_TEMP, "新增指标体系", indexSystemManage.getIndexTypeName()));
        return save(indexSystemManage);
    }

    /**
     * 更新指标体系
     *
     * @param indexSystemManage
     * @return
     */
    @Override
    public Boolean updateIndexSystem(IndexSystemManage indexSystemManage) {
        indexSystemManage.setWPdateDt(LocalDateTime.now());
//        indexSystemManage.setEnabled("-1");
        IndexSystemManage oldManage = this.getById(indexSystemManage.getDataId());

        if(updateById(indexSystemManage)) {
            if(!oldManage.getIndexTypeName().equals(indexSystemManage.getIndexTypeName())) {
                OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.UPDATE,
                        "修改指标体系"+oldManage.getIndexTypeName()+"为"+indexSystemManage.getIndexTypeName());
            }

            if(!oldManage.getDepartment().equals(indexSystemManage.getDepartment())) {
                OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.UPDATE,
                        "修改指标体系"+indexSystemManage.getIndexTypeName()+"管理部门为"+indexSystemManage.getDepartment());
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 更新状态
     *
     * @param id
     * @param type
     * @return
     */
    @Override
    public Boolean updateStatus(Long id, String type) {
        IndexSystemManage indexSystemManage = new IndexSystemManage();
        indexSystemManage.setDataId(id);
        indexSystemManage.setEnabled(type);
        return updateById(indexSystemManage);
    }

    /**
     * 删除指标体系
     *
     * @param indexSystemManage
     * @return
     */
    @Override
    public Boolean deleteIndexSystem(IndexSystemManage indexSystemManage) {
        //删除所有分类
        LambdaQueryWrapper<IndexSystemItemManage> queryWrapper = new QueryWrapper<IndexSystemItemManage>().lambda();
        queryWrapper.eq(IndexSystemItemManage::getIndexId, indexSystemManage.getDataId());
        List<IndexSystemItemManage> list = indexSystemItemManageService.list(queryWrapper);
        List<IndexSystemItemManage> updateList = new LinkedList<>();
        for(IndexSystemItemManage indexSystemItemManage: list){
            indexSystemItemManage.setStatus("-1");
            updateList.add(indexSystemItemManage);
        }
        if(updateList.size()>0){
            indexSystemItemManageService.updateBatchById(updateList);
        }
        indexSystemManage.setStatus("-1");
        OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.DELETE,
                String.format(MSG_TEMP, "删除指标体系", indexSystemManage.getIndexTypeName()));

        return updateById(indexSystemManage);
    }

    /**
     * 根据indexId获取指标体系
     * @param indexId
     * @return
     */
    private List<IndexSystemManage> getIndexSystemByIndexId(String indexId) {
        //获取所有的指标体系
        LambdaQueryWrapper<IndexSystemManage> queryWrapper = new QueryWrapper<IndexSystemManage>().lambda();
        if(StringUtils.isNotEmpty(indexId)){
            queryWrapper.eq(IndexSystemManage::getDataId, indexId);
        }
        queryWrapper.eq(IndexSystemManage::getStatus, "1");
        List<IndexSystemManage> indexSystemList = list(queryWrapper);
        return indexSystemList;
    }

    /**
     * list 转换为 map
     * @param list
     * @param indexSystemMap
     * @return
     */
    private Map<Long, IndexSystemNode> convertIndexSystemToMap(List<IndexSystemItemManage> list,
                                                               Map<String, IndexSystemNode> indexSystemMap) {
        Map<Long, IndexSystemNode> indexSystemItemMap = new HashMap<>();
        for (IndexSystemItemManage indexSystemItem : list) {
            IndexSystemNode node = new IndexSystemNode();
            Long pId = indexSystemItem.getParentId();
            node.setId(indexSystemItem.getItemId());
            node.setPId(pId);
            node.setDataId(indexSystemItem.getDataId());
            node.setIndexName(indexSystemItem.getItemName());
            node.setLevel(indexSystemItem.getLevel());
            node.setChildren(new LinkedList<>());
            if (pId == null || pId <= 0) {//一级分类
                IndexSystemNode indexSystemNode = indexSystemMap.get(indexSystemItem.getIndexId());
                if (indexSystemNode != null) {
                    indexSystemNode.getChildren().add(node);
                }
            }
            indexSystemItemMap.put(node.getDataId(), node);
        }
        return indexSystemItemMap;
    }

    /**
     * 获取指标体系树
     *
     * @return
     */
    @Override
    public List<IndexSystemNode> getIndexSystemTree() {
        List<IndexSystemNode> result = new LinkedList<>();
        //获取所有的指标体系
        List<IndexSystemManage> indexSystemList = getIndexSystemByIndexId(null);
        if (indexSystemList.size() <= 0) {
            return result;
        }
        Map<String, IndexSystemNode> indexSystemMap = new HashMap<>();
        for (IndexSystemManage indexSystem : indexSystemList) {
            IndexSystemNode node = new IndexSystemNode();
            node.setDataId(indexSystem.getDataId());
            node.setPId(0L);
            node.setIndexName(indexSystem.getIndexTypeName());
            node.setId(indexSystem.getDataId().toString());
            node.setLevel("0");
            node.setChildren(new LinkedList<>());
            result.add(node);
            indexSystemMap.put(indexSystem.getDataId().toString(), node);
        }
        //获取所有的分类
        LambdaQueryWrapper<IndexSystemItemManage> query = new QueryWrapper<IndexSystemItemManage>().lambda();
        query.eq(IndexSystemItemManage::getStatus, "1");
        List<IndexSystemItemManage> list = indexSystemItemManageService.list(query);
        Map<Long, IndexSystemNode> indexSystemItemMap = convertIndexSystemToMap(list, indexSystemMap);

        for (IndexSystemNode node : indexSystemItemMap.values()) {
            Long pId = node.getPId();
            if (pId != null && pId > 0) {
                IndexSystemNode indexSystemNode = indexSystemItemMap.get(pId);
                if (indexSystemNode != null) {
                    indexSystemNode.getChildren().add(node);
                }
            }
        }

        return result;
    }

    /**
     * 新增指标体系项目
     *
     * @param indexSystemItemManage
     * @return
     */
    @Override
    public Boolean addIndexSystemItem(IndexSystemItemManage indexSystemItemManage) {
        indexSystemItemManage.setWPdateDt(LocalDateTime.now());
        indexSystemItemManage.setWInsertDt(LocalDateTime.now());
        UserInfoBo loginUser = userService.getLoginUser();
        indexSystemItemManage.setCreater(loginUser.getUserName());
        indexSystemItemManage.setCreaterId(loginUser.getUserAccount());
        indexSystemItemManage.setLastModifier(loginUser.getUserName());
        indexSystemItemManage.setLastModifierId(loginUser.getUserAccount());
        Long parentId = indexSystemItemManage.getParentId();
        indexSystemItemManageService.save(indexSystemItemManage);
        int pLevel=0;
        String parentIdPath = "";
        String parentNamePath = "";
        if (parentId != null && parentId > 0) {
            IndexSystemItemManage pNode = indexSystemItemManageService.getById(parentId);
            pLevel = Integer.parseInt(pNode.getLevel());
            parentIdPath = pNode.getParentIdPath() == null ? "" : pNode.getParentIdPath();
            parentNamePath = pNode.getParentNamePath() == null ? "" : pNode.getParentNamePath() ;
        }
        //生成一个itemid,L层级_自增序号(自增序号使用当前分类主键id)
        String level = String.valueOf(pLevel + 1);
        indexSystemItemManage.setLevel(level);
        indexSystemItemManage.setItemId("L"+level+"_"+indexSystemItemManage.getDataId());
        String parenPathId = parentIdPath + indexSystemItemManage.getItemId()+ Constants.SPLIT_CHART;
        String parenPathName = parentNamePath + indexSystemItemManage.getItemName()+ Constants.SPLIT_CHART;
        indexSystemItemManage.setParentIdPath(parenPathId);
        indexSystemItemManage.setParentNamePath(parenPathName);
        indexSystemItemManageService.updateById(indexSystemItemManage);
        OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.ADD,
                String.format(MSG_TEMP, "新增指标分类", indexSystemItemManage.getItemName()));
        return true;
    }

    /**
     * 通过 id 和indexSystemItemManage 更新数据
     * @param id
     * @param indexSystemItemManage
     */
    private void updateByIndexSystemItemManage(Long id, IndexSystemItemManage indexSystemItemManage) {
        //查询旧数据
        IndexSystemItemManage oldNode = indexSystemItemManageService.getById(id);
        if (!indexSystemItemManage.getItemName().equals(oldNode.getItemName())) {
            //名称变更
            LambdaQueryWrapper<IndexSystemItemManage> query = new QueryWrapper<IndexSystemItemManage>().lambda();
            String oldNodeParentNamePath = oldNode.getParentNamePath();
            String newNodeParentNamePath = indexSystemItemManage.getParentNamePath();
//            if (StringUtils.isNotEmpty(oldNode.getParentNamePath())) {
//                oldNodeParentNamePath = oldNode.getParentNamePath() + Constants.SPLIT_CHART + oldNode.getItemName();
//                newNodeParentNamePath = oldNode.getParentNamePath() + Constants.SPLIT_CHART + indexSystemItemManage.getItemName();
//            }
            query.eq(IndexSystemItemManage::getIndexId, oldNode.getIndexId());
            query.eq(IndexSystemItemManage::getStatus, "1");
            query.likeRight(IndexSystemItemManage::getParentNamePath, oldNodeParentNamePath);
            query.likeRight(IndexSystemItemManage::getParentIdPath, oldNode.getParentIdPath());
            List<IndexSystemItemManage> upList = indexSystemItemManageService.list(query);
            List<IndexSystemItemManage> updateList = new LinkedList<>();
            LocalDateTime wPdateDt = LocalDateTime.now();
            for (IndexSystemItemManage upNode : upList) {
                String parentNamePath = upNode.getParentNamePath();
                parentNamePath = parentNamePath.replace(oldNodeParentNamePath, newNodeParentNamePath);
                upNode.setParentNamePath(parentNamePath);
                upNode.setWPdateDt(wPdateDt);
                updateList.add(upNode);
            }
            if (updateList.size() > 0) {
                indexSystemItemManageService.updateBatchById(updateList);
            }
        }
    }

    /**
     * 更新指标体系项目
     *
     * @param indexSystemItemManage
     * @return
     */
    @Override
    @Transactional
    public Boolean updateIndexSystemItem(IndexSystemItemManage indexSystemItemManage) {
        indexSystemItemManage.setWPdateDt(LocalDateTime.now());
        indexSystemItemManage.setWInsertDt(LocalDateTime.now());
        UserInfoBo loginUser = userService.getLoginUser();
        indexSystemItemManage.setCreater(loginUser.getUserName());
        indexSystemItemManage.setLastModifier(loginUser.getUserName());
        Long parentId = indexSystemItemManage.getParentId();
        Long id = indexSystemItemManage.getDataId();

        if (parentId != null && parentId > 0) {
            IndexSystemItemManage pNode = indexSystemItemManageService.getById(parentId);
            int pLevel = Integer.parseInt(pNode.getLevel());
            String parentIdPath = pNode.getParentIdPath() == null ? "" : pNode.getParentIdPath();
            String parentNamePath = pNode.getParentNamePath() == null ? "" : pNode.getParentNamePath();
            String parenPathId = parentIdPath + indexSystemItemManage.getItemId()+ Constants.SPLIT_CHART;
            String parenPathName = parentNamePath + indexSystemItemManage.getItemName()+ Constants.SPLIT_CHART;
            indexSystemItemManage.setParentIdPath(parenPathId);
            indexSystemItemManage.setParentNamePath(parenPathName);
            indexSystemItemManage.setLevel(String.valueOf(pLevel + 1));
        }

        if (StringUtils.isNotEmpty(indexSystemItemManage.getItemName())) {
            updateByIndexSystemItemManage(id, indexSystemItemManage);
        }

        IndexSystemItemManage oldNode = indexSystemItemManageService.getById(id);
        if(indexSystemItemManageService.updateById(indexSystemItemManage)) {
            OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.UPDATE,
                    "修改指标分类"+oldNode.getItemName()+"为"+indexSystemItemManage.getItemName());
            return true;
        } else {
            return false;
        }

    }

    /**
     * 删除指标体系项目
     *
     * @param indexSystemManages
     * @return
     */
    @Override
    public Boolean deleteIndexSystemItem(List<IndexSystemItemManage> indexSystemManages) {
        for (IndexSystemItemManage indexSystemManage : indexSystemManages) {
            Long dataId = indexSystemManage.getDataId();
            LambdaQueryWrapper<IndexSystemItemManage> query = new QueryWrapper<IndexSystemItemManage>().lambda();
            query.eq(IndexSystemItemManage::getDataId, dataId);
            query.eq(IndexSystemItemManage::getStatus, "1");
            IndexSystemItemManage queryNode = indexSystemItemManageService.getOne(query);
            if (queryNode == null) {
                return false;
            }
            indexSystemManage.setItemName(queryNode.getItemName());

            String pathId = queryNode.getParentIdPath();
            String indexId = queryNode.getIndexId();
            LambdaQueryWrapper<IndexSystemItemManage> upQuery = new QueryWrapper<IndexSystemItemManage>().lambda();
            upQuery.eq(IndexSystemItemManage::getStatus, "1");
            upQuery.eq(IndexSystemItemManage::getIndexId, indexId);
            upQuery.likeRight(IndexSystemItemManage::getParentIdPath, pathId);
            List<IndexSystemItemManage> upList = indexSystemItemManageService.list(upQuery);
            List<IndexSystemItemManage> updateList = new LinkedList<>();
            for (IndexSystemItemManage item : upList) {
                item.setStatus("-1");
                item.setWPdateDt(LocalDateTime.now());
                updateList.add(item);
            }
            indexSystemManage.setStatus("-1");
            indexSystemManage.setWPdateDt(LocalDateTime.now());
            updateList.add(indexSystemManage);
            indexSystemItemManageService.updateBatchById(updateList);

        }
        OperationLogUtil.addLog(OperationPageEnum.INDEX_MANAGEMENT, OperationEnum.DELETE,
                String.format(MSG_TEMP, "刪除指标分类", indexSystemManages.stream().map(IndexSystemItemManage::getItemName).collect(Collectors.toList())));
        return true;
    }

    @Override
    public List<IndexSystemItemNode> getIndexSystemItemTree(String indexId) {
        List<IndexSystemItemNode> result = new LinkedList<>();

        //获取所有的指标体系
        List<IndexSystemManage> indexSystemList = getIndexSystemByIndexId(indexId);
        if (indexSystemList.size() <= 0) {
            return result;
        }
        Map<String, IndexSystemItemNode> indexSystemMap = new HashMap<>();
        for (IndexSystemManage indexSystem : indexSystemList) {
            IndexSystemItemNode node = new IndexSystemItemNode();
            node.setDataId(indexSystem.getDataId());
            node.setParentId(0L);
            node.setItemName(indexSystem.getIndexTypeName());
            node.setItemId(indexSystem.getDataId().toString());
            node.setLevel("0");
            node.setChildren(new LinkedList<>());
            result.add(node);
            indexSystemMap.put(indexSystem.getDataId().toString(), node);
        }

        //获取所有的分类
        LambdaQueryWrapper<IndexSystemItemManage> query = new QueryWrapper<IndexSystemItemManage>().lambda();
        query.eq(IndexSystemItemManage::getStatus, "1");
        if(StringUtils.isNotEmpty(indexId)){
            query.eq(IndexSystemItemManage::getIndexId, indexId);
        }
        List<IndexSystemItemManage> list = indexSystemItemManageService.list(query);
        if(list.size()<=0){
            return result;
        }
        Map<Long, IndexSystemItemNode> indexSystemItemMap = new HashMap<>();
        for(IndexSystemItemManage indexSystemItemManage: list){
            IndexSystemItemNode node = new IndexSystemItemNode();
            node.setChildren(new LinkedList<>());
            BeanUtils.copyProperties(indexSystemItemManage, node);
            indexSystemItemMap.put(node.getDataId(), node);
        }

        for (IndexSystemItemNode node : indexSystemItemMap.values()) {
            Long pId = node.getParentId();
            if(pId==null || pId == 0){
                //找到指标体系
                IndexSystemItemNode indexSystemItemNode = indexSystemMap.get(node.getIndexId());
                if(indexSystemItemNode!=null){
                    indexSystemItemNode.getChildren().add(node);
                }
            }
            IndexSystemItemNode indexSystemNode = indexSystemItemMap.get(pId);
            if (indexSystemNode != null) {
                indexSystemNode.getChildren().add(node);
            }
        }
        return result;
    }

    @Override
    public Boolean saveIndexSystemItems(IndexSystemItemsParams params) {
        String indexId = params.getIndexId();
        List<ImportIndexItemResult> indexItems = params.getIndexItems();
        UserInfoBo loginUser = userService.getLoginUser();
        Map<String, IndexSystemItemManage> firstMap = new HashMap<>();
        Map<String, IndexSystemItemManage> secondMap = new HashMap<>();
        Map<String, IndexSystemItemManage> thirdMap = new HashMap<>();
        Map<String, IndexSystemItemManage> fourMap = new HashMap<>();

        for(ImportIndexItemResult item: indexItems){
            String firstIndexName = item.getFirstIndexName();
            if(StringUtils.isEmpty(firstIndexName)){
                continue;
            }
            IndexSystemItemManage firstIndexSystemItemManage =
                    selectOrSaveItem(firstIndexName, indexId, "1", item.getNotes(), loginUser, null, firstMap);
            String secondIndexName = item.getSecondIndexName();
            if(StringUtils.isEmpty(secondIndexName)){
                continue;
            }
            IndexSystemItemManage secondIndexSystemItemManage =
                    selectOrSaveItem(secondIndexName, indexId, "2", item.getNotes(),loginUser, firstIndexSystemItemManage, secondMap);
            String thirdIndexName = item.getThirdIndexName();
            if(StringUtils.isEmpty(thirdIndexName)){
                continue;
            }
            IndexSystemItemManage thirdIndexSystemItemManage =
                    selectOrSaveItem(thirdIndexName, indexId, "3", item.getNotes(),loginUser, secondIndexSystemItemManage, thirdMap);
            String fourIndexName = item.getFourIndexName();
            if(StringUtils.isEmpty(fourIndexName)){
                continue;
            }
            IndexSystemItemManage fourIndexSystemItemManage =
                    selectOrSaveItem(fourIndexName, indexId, "4", item.getNotes(),loginUser, thirdIndexSystemItemManage, fourMap);
        }
        return true;
    }

    /**
     * 查询或保存指标分类
     */
    private IndexSystemItemManage selectOrSaveItem(String name, String indexId, String level, String notes, UserInfoBo loginUser,
                                                   IndexSystemItemManage pNode, Map<String, IndexSystemItemManage> itemMap){
        IndexSystemItemManage indexSystemItemManage = itemMap.get(name);
        if(StringUtils.isNotEmpty(name) && indexSystemItemManage == null){
            indexSystemItemManage =  queryIndexSystemItemManage(name, indexId, level);
            if(indexSystemItemManage==null){
                indexSystemItemManage = new IndexSystemItemManage();
                indexSystemItemManage.setIndexId(indexId);
                indexSystemItemManage.setItemName(name);
                indexSystemItemManage.setNotes(notes);
                indexSystemItemManage = saveItem(indexSystemItemManage, pNode , loginUser);
            }
            itemMap.put(name, indexSystemItemManage);
        }
        return indexSystemItemManage;
    }


    /**
     * 保存指标分类
     */
    private IndexSystemItemManage saveItem(IndexSystemItemManage indexSystemItemManage, IndexSystemItemManage pNode,  UserInfoBo loginUser){
        indexSystemItemManage.setWPdateDt(LocalDateTime.now());
        indexSystemItemManage.setWInsertDt(LocalDateTime.now());
        indexSystemItemManage.setCreater(loginUser.getUserName());
        indexSystemItemManage.setCreaterId(loginUser.getUserAccount());
        indexSystemItemManage.setLastModifier(loginUser.getUserName());
        indexSystemItemManage.setLastModifierId(loginUser.getUserAccount());
        indexSystemItemManageService.save(indexSystemItemManage);
        int pLevel=0;
        Long parentId = 0L;
        String parentIdPath = "";
        String parentNamePath = "";
        if (pNode!=null) {
            parentId = pNode.getDataId();
            pLevel = Integer.parseInt(pNode.getLevel());
            parentIdPath = pNode.getParentIdPath() == null ? "" : pNode.getParentIdPath();
            parentNamePath = pNode.getParentNamePath() == null ? "" : pNode.getParentNamePath();
        }
        indexSystemItemManage.setParentId(parentId);
        //生成一个itemid,L层级_自增序号(自增序号使用当前分类主键id)
        String level = String.valueOf(pLevel + 1);
        indexSystemItemManage.setLevel(level);
        indexSystemItemManage.setItemId("L"+level+"_"+indexSystemItemManage.getDataId());
        String parenPathId = parentIdPath + indexSystemItemManage.getItemId() + Constants.SPLIT_CHART;
        String parenPathName = parentNamePath + indexSystemItemManage.getItemName() + Constants.SPLIT_CHART;
        indexSystemItemManage.setParentIdPath(parenPathId);
        indexSystemItemManage.setParentNamePath(parenPathName);
        indexSystemItemManageService.updateById(indexSystemItemManage);
        return indexSystemItemManage;
    }

    /**
     * 查询指标分类
     */
    private IndexSystemItemManage queryIndexSystemItemManage(String itemName, String indexId, String level){
        LambdaQueryWrapper<IndexSystemItemManage> query = new QueryWrapper<IndexSystemItemManage>().lambda();
        query.eq(IndexSystemItemManage::getStatus, "1");
        query.eq(IndexSystemItemManage::getItemName, itemName);
        query.eq(IndexSystemItemManage::getIndexId, indexId);
        query.eq(IndexSystemItemManage::getLevel, level);
        query.last("limit 1");
        return indexSystemItemManageService.getOne(query);
    }

    /**
     * 清空数据，硬删除
     */
    @Override
    public boolean clearIndexSystem() {
        return this.remove(new QueryWrapper<>());
    }

    /**
     *
     * @return
     */
    private List<IndexSystemManage> getAllIndexSystemList() {
        LambdaQueryWrapper<IndexSystemManage> queryWrapper = new QueryWrapper<IndexSystemManage>().lambda();
        queryWrapper.orderByAsc(IndexSystemManage::getDataId);
        return list(queryWrapper);
    }

    @Deprecated
    public boolean applyModificationOld(Date startTime, Date endTime) {
        List<IndexSystemManage> indexSystemManageList = this.getAllIndexSystemList();
        String jobName = "modification-"+DateFormatEnum.YYYY_MM_DD_HHMMSS.getSimpleDateFormat().format(endTime);
        for(IndexSystemManage indexSystemManage : indexSystemManageList) {
            if(Status.DELETE.equals(indexSystemManage.getStatus())) {
                // 如果管理部分指标体系状态为删除， 那么实际使用表直接所有所选的状态标记为删除
                indexSystemService.updateIndexSystemStatus(indexSystemManage.getDataId().toString(), jobName, Status.DELETE);
            } else if (Status.NORMAL.equals(indexSystemManage.getStatus())) {
                // 更新对应指标体系的应用状态
                indexSystemService.updateIndexSystemInfo(indexSystemManage.getDataId().toString(), jobName,
                        indexSystemManage.getIndexTypeName(), indexSystemManage.getEnabled());

                // 先通过startTime, endTime以及对应的指标体系id，找出最近修改过的指标并应用
                List<IndexSystemItemManage> indexSystemItemManages = indexSystemItemManageService
                        .findLastLevelModifyByRange(
                                indexSystemManage.getDataId(),
                                indexSystemManage.getMetricLevel(),
                                startTime, endTime);
                if(CollectionUtils.isNotEmpty(indexSystemItemManages)) {
                    indexSystemService.updateByIndexSystemMange(jobName, indexSystemItemManages, indexSystemManage);
                }
            }
        }

        return true;
    }

    /**
     * 应用指标体系
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional
    @Override
    public boolean applyModification(Date startTime, Date endTime) {
        String jobName = "modification-"+DateFormatEnum.YYYY_MM_DD_HHMMSS.getSimpleDateFormat().format(endTime);

        // 在已有的指标体系中找出最近被更新过的指标体系，并标记为-1
        List<String> dataIds = this.indexSystemService.findLastLevelModifyByRange(startTime, endTime);
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(log.isDebugEnabled()){
                for(int i = 0; i < dataIds.size(); i++) {
                    log.debug("删除了 dataIds["+i+"]="+dataIds.get(i));
                }
            }
            this.indexSystemService.removeIndexSystemByIds(dataIds, jobName);
        }

        // 更新指标体系， 插入indexStatus为1或者status为1的指标体系
        List<IndexSystemRangeResult> indexSystemRangeResults = this.indexSystemItemManageService.findLastLevelModifyByRange(startTime, endTime);
        if(CollectionUtils.isNotEmpty(indexSystemRangeResults)) {
            this.indexSystemService.updateByIndexSystemRangeResult(jobName, indexSystemRangeResults);
        }

        return true;
    }

    /**
     * 根据参数获取agg的指标体系
     *
     * @param params
     * @return
     */
    @Override
    public String getAggIndexByParams(RequestParams params) {
        Integer indexLevel = getIndexLevel(params);
        return getAggIndexByIndexLevel(indexLevel);
    }


    /**
     * 根据参数获取指标体系的级别
     * @param params
     * @return
     */
    @Override
    public Integer getIndexLevel(RequestParams params) {
        IndexSystemManage indexSystemManageParam = new IndexSystemManage();
        indexSystemManageParam.setIndexTypeName(params.getIndexTypeName());
        indexSystemManageParam.setDataId(Long.valueOf(params.getIndexType()));
        List<IndexSystemManage> indexSystemManageServices = this.getIndexSystemListByParam(indexSystemManageParam);
        if (CollectionUtils.isNotEmpty(indexSystemManageServices)) {
            if(StringUtils.isNotBlank(indexSystemManageServices.get(0).getMetricLevel())) {
                return Integer.valueOf(indexSystemManageServices.get(0).getMetricLevel());
            }
        }
        // 如果没有找到对用的指标体系信息，默认返回4
        return 4;
    }

    /**
     * 根据指标体系层级判断需要获取的agg的字段
     * @param indexLevel
     * @return
     */
    private String getAggIndexByIndexLevel(Integer indexLevel) {
        if(indexLevel == 1) {
            return ESUtil.AGG_FIRST_INDEX_ID;
        }

        if(indexLevel == 2) {
            return ESUtil.AGG_SECOND_INDEX_ID;
        }

        if(indexLevel == 3) {
            return ESUtil.AGG_THIRD_INDEX_ID;
        }

        return ESUtil.AGG_FOUR_INDEX_ID;
    }

}
