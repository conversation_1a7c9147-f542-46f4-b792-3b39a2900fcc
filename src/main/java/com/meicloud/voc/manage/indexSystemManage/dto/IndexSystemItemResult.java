package com.meicloud.voc.manage.indexSystemManage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 指标体系管理
 * </p>
 *
 * <AUTHOR> ouyang
 * @since 2022-05-06
 */
@Data
@ApiModel("指标体系分类")
public class IndexSystemItemResult implements Serializable {

    @ApiModelProperty(value = "指标类型", required = true)
    private String indexType;

    @ApiModelProperty(value = "指标类型名称", required = true)
    private String indexTypeName;

    @ApiModelProperty(value = "指标层级", required = true)
    private String metricLevel;

    @ApiModelProperty("一级指标id")
    private String firstIndexId;

    @ApiModelProperty("一级指标")
    @ExcelProperty(value = "一级分类", index = 0)
    private String firstIndexName;

    @ApiModelProperty("二级指标id")
    private String secondIndexId;

    @ApiModelProperty("二级指标")
    @ExcelProperty(value = "二级分类", index = 1)
    private String secondIndexName;

    @ApiModelProperty("三级指标id")
    private String thirdIndexId;

    @ApiModelProperty("三级指标")
    @ExcelProperty(value = "三级分类", index = 2)
    private String thirdIndexName;

    @ApiModelProperty("四级指标id")
    private String fourIndexId;

    @ApiModelProperty("四级指标")
    @ExcelProperty(value = "四级分类", index = 3)
    private String fourIndexName;

    @ApiModelProperty("id")
    private Long dataId;

    @ApiModelProperty("父节点id")
    private Long parentId;

    @ApiModelProperty("id路径")
    private String idPath;

    @ApiModelProperty("名称路径")
    private String namePath;

    @ApiModelProperty("关联标准关键词数量")
    @ExcelProperty(value = "关联标准关键词数量", index = 4)
    private int stKeywordCount;

    @ApiModelProperty("关联语料数量")
    @ExcelProperty(value = "关联语料数量", index = 5)
    private int keywordCount;

    @ApiModelProperty("最后修改人（最近更新人）")
    @ExcelProperty(value = "最近更新人", index = 6)
    private String lastModifier;

    @ApiModelProperty("最后修改人id(最近更新人id)")
    @ExcelProperty(value = "最近更新人id", index = 7)
    private String lastModifierId;

    @ApiModelProperty("最后更新时间（最近更新时间）")
//    @ExcelProperty(value = "最近更新时间", index = 8)
    private LocalDateTime wPdateDt;

}
