package com.meicloud.voc.manage.indexSystemManage.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meicloud.voc.common.enums.StatusEnum;
import com.meicloud.voc.common.exception.ServiceException;
import com.meicloud.voc.manage.indexSystemManage.dto.*;
import com.meicloud.voc.manage.indexSystemManage.entity.IndexSystemItemManage;
import com.meicloud.voc.manage.indexSystemManage.entity.StandardKeywordIndexItem;
import com.meicloud.voc.manage.indexSystemManage.enums.IndexSystemItemManageColumnEnum;
import com.meicloud.voc.manage.indexSystemManage.mapper.IndexSystemItemManageMapper;
import com.meicloud.voc.manage.indexSystemManage.service.IIndexSystemItemManageService;
import com.meicloud.voc.manage.indexSystemManage.service.IStandardKeywordIndexItemService;
import com.meicloud.voc.manage.keyword.entity.Keyword;
import com.meicloud.voc.manage.keyword.service.impl.KeywordServiceImpl;
import com.meicloud.voc.manage.stKeywordManage.service.ISTKeywordManageService;
import com.meicloud.voc.utils.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;

/**
 * <p>
 * 指标体系项 服务实现类
 * </p>
 *
 * <AUTHOR> ouyang
 * @since 2022-05-06
 */
@Service
public class IndexSystemItemManageServiceImpl extends ServiceImpl<IndexSystemItemManageMapper, IndexSystemItemManage> implements IIndexSystemItemManageService {

    @Value("${file.tmp.path:./temp/}")
    private String tempPath;

    /**
     * 标准关键词
     */
    @Autowired
    private ISTKeywordManageService stKeywordManageService;

    /**
     * 语料管理
     */
    @Autowired
    private KeywordServiceImpl keywordService;

    /**
     * 标准关键词与指标分类关联关系
     */
    @Autowired
    private IStandardKeywordIndexItemService standardKeywordIndexItemService;

    /**
     * 线程池
     */
    @Resource(name = "serviceExecutor")
    private Executor executor;

    /**
     * 下载指标体系模板
     *
     * @param httpResponse
     */
    @Override
    public void downloadIndexSystemItemTemplate(HttpServletResponse httpResponse) {
        FileUtil.downloadLocalFile(httpResponse, "exportTemplate/indexSystemItemTemplate.xlsx", "指标体系导入模板.xlsx");
    }

    /**
     * 上传指标体系文件
     *
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public List<ImportIndexItemResult> uploadIndexItemFile(MultipartFile file) throws IOException {
        List<ImportIndexItemResult> importList = EasyExcel.read(file.getInputStream())
                .head(ImportIndexItemResult.class)
                .sheet()
                .doReadSync();
        return importList;
    }


    /**
     * 获取item中的指标体系
     * @param indexIds
     * @param metriclLevel
     * @param detailResult
     */
    private void getItemIndexIds(List<String> indexIds, Integer metriclLevel, IndexSystemItemDetailResult detailResult) {
        if(metriclLevel > 1) {
            indexIds.add(detailResult.getFirstIndexId());
        }

        if(metriclLevel > 2) {
            indexIds.add(detailResult.getSecondIndexId());
        }

        if(metriclLevel > 3) {
            indexIds.add(detailResult.getThirdIndexId());
        }
    }

    /**
     * 获取指标 notes映射关系
     * @param detailResultList
     * @param metricLevel
     * @param indexId
     * @return
     */
    private Map<String, String> getNotesMapInfo(List<IndexSystemItemDetailResult> detailResultList,
                                               String metricLevel, String indexId){
        List<String> indexIds = new ArrayList<>();
        for(IndexSystemItemDetailResult detailResult : detailResultList) {
            getItemIndexIds(indexIds, Integer.parseInt(metricLevel), detailResult);
        }

        if(indexIds.size() <= 0) new HashMap<>();

        Set<String> indexIdSet = new HashSet<>(indexIds);
        LambdaQueryWrapper<IndexSystemItemManage> queryWrapper = new QueryWrapper<IndexSystemItemManage>().lambda();

        queryWrapper.eq(IndexSystemItemManage::getStatus, "1");
        queryWrapper.eq(IndexSystemItemManage::getIndexId, indexId);
        queryWrapper.in(IndexSystemItemManage::getItemId, indexIdSet);
        List<IndexSystemItemManage> indexSystemItemManageList = baseMapper.selectList(queryWrapper);
        Map<String, String> notesMap = new HashMap<>();
        for(IndexSystemItemManage indexSystemItemManage : indexSystemItemManageList) {
            notesMap.put(indexSystemItemManage.getItemId(), indexSystemItemManage.getNotes());
        }
        return notesMap;
    }

    /**
     * 设置指标体系的notes
     * @param detailResultList
     * @param metricLevel
     * @param indexId
     * @return
     */
    private List<IndexSystemItemDetailResult> setIndexSystemItemListNotes(List<IndexSystemItemDetailResult> detailResultList,
                                                                          String metricLevel, String indexId) {

        Map<String, String> notesMap = getNotesMapInfo(detailResultList, metricLevel, indexId);
        if(notesMap.size() == 0) return detailResultList;
        int level = Integer.parseInt(metricLevel);
        for(IndexSystemItemDetailResult detailResult : detailResultList) {
            if(level > 1) {
                detailResult.setFirstIndexNotes(notesMap.getOrDefault(detailResult.getFirstIndexId(), null));
            }

            if(level > 2) {
                detailResult.setSecondIndexNotes(notesMap.getOrDefault(detailResult.getSecondIndexId(), null));
            }

            if(level > 3) {
                detailResult.setThirdIndexNotes(notesMap.getOrDefault(detailResult.getThirdIndexId(), null));
            }
        }
        return detailResultList;
    }

    /**
     * 指标体系-分类列表
     *
     * @param params
     * @return
     */
    @Override
    public IPage<IndexSystemItemDetailResult> getIndexSystemItemList(IndexSystemItemParams params) {
        IPage<IndexSystemItemDetailResult> page = new Page<>(params.getCurrent(), params.getSize());
        List<IndexSystemItemDetailResult> records = new LinkedList<>();
        LambdaQueryWrapper<IndexSystemItemManage> queryWrapper = new QueryWrapper<IndexSystemItemManage>().lambda();
        String indexIdPath = handlerIndexIdPath(params);
        queryWrapper.eq(IndexSystemItemManage::getStatus, "1");
        queryWrapper.eq(IndexSystemItemManage::getLevel, params.getMetricLevel());
        queryWrapper.eq(IndexSystemItemManage::getIndexId, params.getIndexType());
        if(StringUtils.isNotEmpty(indexIdPath)){
            queryWrapper.likeRight(IndexSystemItemManage::getParentIdPath, indexIdPath);
        }
        queryWrapper.orderByAsc(IndexSystemItemManage::getParentIdPath);
        IPage<IndexSystemItemManage> queryPage = new Page<>(params.getCurrent(), params.getSize());
        queryPage = page(queryPage, queryWrapper);
        if(queryPage.getRecords().size()>0){
            for(IndexSystemItemManage indexSystemItemManage: queryPage.getRecords()){
                IndexSystemItemDetailResult result =indexSystemItemManage2IndexSystemItemResult(indexSystemItemManage);
                result.setLastModifier(indexSystemItemManage.getLastModifier());
                result.setLastModifierId(indexSystemItemManage.getLastModifierId());
                result.setIndexType(indexSystemItemManage.getIndexId());
                result.setIndexTypeName(params.getIndexTypeName());
                result.setMetricLevel(params.getMetricLevel());
                result.setIdPath(indexSystemItemManage.getParentIdPath());
                result.setNamePath(indexSystemItemManage.getParentNamePath());
                result.setParentId(indexSystemItemManage.getParentId());
                result.setDataId(indexSystemItemManage.getDataId());
                //查询关键词数量和语料数量
                getKeywordsCount(result);
                result.setWPdateDt(indexSystemItemManage.getWPdateDt());
                records.add(result);
            }

            setIndexSystemItemListNotes(records, params.getMetricLevel(), params.getIndexType());
        }
        page.setTotal(queryPage.getTotal());
        page.setRecords(records);
        return page;
    }

    /**
     * 补全关键词和语料的数据
     */
    private void getKeywordsCount(IndexSystemItemResult indexSystemItem){

        //查询关键词数量
        LambdaQueryWrapper<StandardKeywordIndexItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StandardKeywordIndexItem::getItemDataId, indexSystemItem.getDataId());
        int count = (int) standardKeywordIndexItemService.count(queryWrapper);
        indexSystemItem.setStKeywordCount(count);
        indexSystemItem.setKeywordCount(0);
        //语料数量
        //获取所有的标准关键词id
        List<StandardKeywordIndexItem> list = standardKeywordIndexItemService.list(queryWrapper);
        Set<Long> stKeywordIds = new HashSet<>();
        for(StandardKeywordIndexItem indexItem: list){
            stKeywordIds.add(indexItem.getStandardKeywordId());
        }
        if(stKeywordIds.size()>0){
            LambdaQueryWrapper<Keyword> query = new LambdaQueryWrapper<>();
            query.in(Keyword::getStandardKeywordId, stKeywordIds);
            int keywordCount = (int) keywordService.count(query);
            indexSystemItem.setKeywordCount(keywordCount);
        }
    }

    private IndexSystemItemDetailResult indexSystemItemManage2IndexSystemItemResult(IndexSystemItemManage indexSystemItemManage){
        IndexSystemItemDetailResult result = new IndexSystemItemDetailResult();
        String parentIdPath = indexSystemItemManage.getParentIdPath();
        String parentNamePath = indexSystemItemManage.getParentNamePath();
        if(parentIdPath.endsWith(Constants.SPLIT_CHART)){
            parentIdPath = parentIdPath.substring(0, parentIdPath.length()-2);
        }
        if(parentNamePath.endsWith(Constants.SPLIT_CHART)){
            parentNamePath = parentNamePath.substring(0, parentNamePath.length()-2);
        }
        String[] idPathArr = parentIdPath.split(Constants.SPLIT_CHART);
        String[] namePathArr = parentNamePath.split(Constants.SPLIT_CHART);
        if(idPathArr.length==4){
            result.setFirstIndexId(idPathArr[0]);
            result.setFirstIndexName(namePathArr[0]);
            result.setSecondIndexId(idPathArr[1]);
            result.setSecondIndexName(namePathArr[1]);
            result.setThirdIndexId(idPathArr[2]);
            result.setThirdIndexName(namePathArr[2]);
            result.setFourIndexId(idPathArr[3]);
            result.setFourIndexName(namePathArr[3]);
            result.setFourIndexNotes(indexSystemItemManage.getNotes());
        }else if(idPathArr.length==3){
            result.setFirstIndexId(idPathArr[0]);
            result.setFirstIndexName(namePathArr[0]);
            result.setSecondIndexId(idPathArr[1]);
            result.setSecondIndexName(namePathArr[1]);
            result.setThirdIndexId(idPathArr[2]);
            result.setThirdIndexName(namePathArr[2]);
            result.setThirdIndexNotes(indexSystemItemManage.getNotes());
        }else if(idPathArr.length==2){
            result.setFirstIndexId(idPathArr[0]);
            result.setFirstIndexName(namePathArr[0]);
            result.setSecondIndexId(idPathArr[1]);
            result.setSecondIndexName(namePathArr[1]);
            result.setSecondIndexNotes(indexSystemItemManage.getNotes());
        }else if(idPathArr.length==1){
            result.setFirstIndexId(idPathArr[0]);
            result.setFirstIndexName(namePathArr[0]);
            result.setFirstIndexNotes(indexSystemItemManage.getNotes());
        }
        return result;
    }

    /**
     * 指标体系分类导出
     * @param params
     * @param httpResponse
     */
    @Override
    public void exportIndexSystemItemList(IndexSystemItemParams params, HttpServletResponse httpResponse) {
        params.setCurrent(1);
        params.setSize(20000);
        IPage<IndexSystemItemDetailResult> indexSystemItemList = getIndexSystemItemList(params);
        List<IndexSystemItemDetailResult> records = indexSystemItemList.getRecords();
        List<String> includeColumns = new LinkedList<>();
        includeColumns.add("firstIndexName");
        includeColumns.add("secondIndexName");
        includeColumns.add("thirdIndexName");
        includeColumns.add("fourIndexName");
        includeColumns.add("stKeywordCount");
        includeColumns.add("keywordCount");
        includeColumns.add("lastModifier");
        includeColumns.add("lastModifierId");
        includeColumns.add("wPdateDt");
        FileUtil.writeExcel(httpResponse, "指标体系分类列表","分类列表", records, IndexSystemItemResult.class, includeColumns);
    }

    /**
     * 删除数据，硬删除
     *
     * @return
     */
    @Override
    public boolean clearIndexSystemItem() {
        return this.remove(new QueryWrapper<>());
    }

    /**
     * 找出最后一级指标在某个时间范围内更新的数据
     *
     * @param indexId
     * @param indexLevel
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<IndexSystemItemManage> findLastLevelModifyByRange(Long indexId, String indexLevel, Date startTime, Date endTime) {
        QueryWrapper<IndexSystemItemManage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(IndexSystemItemManageColumnEnum.COLUMN_INDEX_ID.getValue(), indexId);
        queryWrapper.eq(IndexSystemItemManageColumnEnum.COLUMN_INDEX_LEVEL.getValue(), indexLevel);
        if(startTime != null) {
            queryWrapper.ge(IndexSystemItemManageColumnEnum.COLUMN_W_PDATE_DT.getValue(), startTime);
        }
        if(endTime != null) {
            queryWrapper.le(IndexSystemItemManageColumnEnum.COLUMN_W_PDATE_DT.getValue(), endTime);
        }
        return this.list(queryWrapper);
    }


    /**
     * 查找最近修改时间范围内的数据
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<IndexSystemRangeResult> findLastLevelModifyByRange(Date startTime, Date endTime) {
        return this.baseMapper.findLastLevelModifyByRange(startTime, endTime);
    }

    /**
     * 根据参数找出对应的指标体系数据
     *
     * @param itemId
     * @param itemName
     * @param indexId
     * @return
     */
    @Override
    public List<IndexSystemItemManage> findIndexSystemItem(String itemId, String itemName, String indexId) {
        return this.baseMapper.findIndexSystemItem(itemId, itemName, indexId);
    }

    /**
     * 指标分类合并
     * @param param
     * @return
     */
    @Override
    public Boolean mergeIndexSystemItem(MergeIndexSystemParam param) {
        IndexSystemItemResult mergeNode = param.getMergeNode();
        List<IndexSystemItemResult> otherNodes = param.getOtherNodes();
        if(mergeNode == null || CollectionUtils.isEmpty(otherNodes)){
            throw new ServiceException("被合并的节点和选中的节点都不能为空，参数有误！");
        }

        Long newParentId = mergeNode.getParentId();
        String newParentIdPath = getParentPath(mergeNode.getIdPath());
        String newParentNamePath = getParentPath(mergeNode.getNamePath());
        List<IndexSystemItemManage> upDateList = new LinkedList<>();
        for(IndexSystemItemResult indexSystemItemResult: otherNodes){
            LambdaQueryWrapper<IndexSystemItemManage> queryWrapper = new QueryWrapper<IndexSystemItemManage>().lambda();
            String parentIdPath = getParentPath(indexSystemItemResult.getIdPath());
            String parentNamePath = getParentPath(indexSystemItemResult.getNamePath());
            queryWrapper.likeRight(IndexSystemItemManage::getParentIdPath, indexSystemItemResult.getIdPath());
            queryWrapper.eq(IndexSystemItemManage::getIndexId, indexSystemItemResult.getIndexType());
            List<IndexSystemItemManage> list = list(queryWrapper);
            for(IndexSystemItemManage item:list){
                if(item.getDataId().longValue() ==  indexSystemItemResult.getDataId().longValue()){
                    //被合并的节点，父节点id变更
                    item.setParentId(newParentId);
                    item.setStatus("-1");
                    item.setWPdateDt(LocalDateTime.now());
                    //所属当前分类的关键词处理（将分类与标准关键词关联关系表中的此分类id改成合并后的分类id）
                    standardKeywordIndexItemService.updateIndexItemId(item.getDataId(), mergeNode.getDataId());
                    upDateList.add(item);
                    continue;
                }
                String itemParentIdPath = item.getParentIdPath();
                itemParentIdPath = itemParentIdPath.replace(parentIdPath, newParentIdPath);
                String itemParentNamePath = item.getParentNamePath();
                itemParentNamePath = itemParentNamePath.replace(parentNamePath, newParentNamePath);
                item.setParentIdPath(itemParentIdPath);
                item.setParentNamePath(itemParentNamePath);
                item.setWPdateDt(LocalDateTime.now());
                upDateList.add(item);
            }
        }

        // 设置合并节点更新时间
        IndexSystemItemManage mergeInfo = this.getById(mergeNode.getDataId());
        mergeInfo.setWPdateDt(LocalDateTime.now());

        if(upDateList.size()>0){
            updateBatchById(upDateList);
        }
        return true;
    }

    /**
     * 获取父级路径
     * @return
     */
    private String getParentPath(String path){
        String result = path;
        int i = path.lastIndexOf(Constants.SPLIT_CHART);
        if(i>0){
            result = path.substring(0, i);
        }
        return result;
    }


    /**
     * 指标体系分类id处理
     */
    private String handlerIndexIdPath(IndexSystemItemParams params) {
        StringBuilder result = new StringBuilder();
        if (StringUtils.isNotEmpty(params.getFourIndexId())) {
            result.append(params.getFirstIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getSecondIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getThirdIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getFourIndexId());
        } else if (StringUtils.isNotEmpty(params.getThirdIndexId())) {
            result.append(params.getFirstIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getSecondIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getThirdIndexId()).append(Constants.SPLIT_CHART);
        } else if (StringUtils.isNotEmpty(params.getSecondIndexId())) {
            result.append(params.getFirstIndexId()).append(Constants.SPLIT_CHART);
            result.append(params.getSecondIndexId()).append(Constants.SPLIT_CHART);
        } else if (StringUtils.isNotEmpty(params.getFirstIndexId())) {
            result.append(params.getFirstIndexId()).append(Constants.SPLIT_CHART);
        }
        if (result.length() <= 0) {
            return null;
        }
        return result.toString();
    }

}
