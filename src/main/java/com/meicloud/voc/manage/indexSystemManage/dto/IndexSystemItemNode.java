package com.meicloud.voc.manage.indexSystemManage.dto;

import com.meicloud.voc.manage.indexSystemManage.entity.IndexSystemItemManage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 指标体系管理
 * </p>
 *
 * <AUTHOR> ouyang
 * @since 2022-05-06
 */
@Data
@ApiModel("指标体系节点")
public class IndexSystemItemNode extends IndexSystemItemManage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("子节点")
    private List<IndexSystemItemNode> children;

}
