package com.meicloud.voc.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Session过期策略-动态Url重定向
 */
@Slf4j
public class DynamicUrlRedirectSessionInformationExpiredStrategy implements SessionInformationExpiredStrategy {

    private String destinationUrl;

    private final RedirectStrategy redirectStrategy;

    /**
     * DynamicUrlRedirectSessionInformationExpiredStrategy
     *
     * @param invalidSessionUrl
     */
    public DynamicUrlRedirectSessionInformationExpiredStrategy(String invalidSessionUrl) {
        this(invalidSessionUrl, new DefaultRedirectStrategy());
    }

    /**
     * DynamicUrlRedirectSessionInformationExpiredStrategy(String invalidSessionUrl, RedirectStrategy redirectStrategy)
     *
     * @param invalidSessionUrl
     * @param redirectStrategy
     */
    public DynamicUrlRedirectSessionInformationExpiredStrategy(String invalidSessionUrl, RedirectStrategy redirectStrategy) {
        Assert.isTrue(UrlUtils.isValidRedirectUrl(invalidSessionUrl), "url must start with '/' or with 'http(s)'");
        this.destinationUrl = invalidSessionUrl;
        this.redirectStrategy = redirectStrategy;
    }

    /**
     * Session过期
     *
     * @param event
     * @throws IOException
     */
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException {
        HttpServletRequest request = event.getRequest();
        String s = request.getRequestURL().toString();
        String requestURI = request.getRequestURI();
        String serverName = request.getServerName();
        String scheme = request.getScheme();
        int serverPort = request.getServerPort();
        String domain = scheme + "://" + serverName + (serverPort == 80 ? "" : ":" + serverPort);
        String target = destinationUrl + domain;
        log.info("session invalid resirect url -【{}】", target);
        this.redirectStrategy.sendRedirect(event.getRequest(), event.getResponse(), target);
    }
}
