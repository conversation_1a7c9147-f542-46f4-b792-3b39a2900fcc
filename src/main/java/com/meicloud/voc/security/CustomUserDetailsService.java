package com.meicloud.voc.security;

import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.cas.authentication.CasAssertionAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.meicloud.voc.common.exception.AuthorizedServiceException;
import com.meicloud.voc.common.exception.enums.UserAuthAccessExceptionEnum;
import com.meicloud.voc.company.entity.Company;
import com.meicloud.voc.company.service.ICompanyService;
import com.meicloud.voc.group.entity.GroupDateDetail;
import com.meicloud.voc.group.service.IGroupService;
import com.meicloud.voc.security.dto.CustomUserDetail;
import com.meicloud.voc.security.dto.UserAuthBo;
import com.meicloud.voc.user.entity.User;
import com.meicloud.voc.user.service.IUserService;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * CustomUserDetailsService
 */
@Slf4j
public class CustomUserDetailsService
        implements AuthenticationUserDetailsService<CasAssertionAuthenticationToken> {
    /**
     * 用户服务
     */
    @Autowired
    private IUserService userService;
    /**
     * 角色服务
     */
    @Autowired
    private IGroupService groupService;
    /**
     * 公司服务
     */
    @Autowired
    private ICompanyService companyService;

    /**
     * 加载用户详情
     *
     * @param token
     * @return
     * @throws UsernameNotFoundException
     */
    @Override
    public UserDetails loadUserDetails(CasAssertionAuthenticationToken token) throws UsernameNotFoundException {
        String userAccount = token.getName();
        String authUrl = "";
        if (userAccount.contains("^")) {
            String[] nameArray = userAccount.split("\\^");
            userAccount = nameArray[0];
            authUrl = nameArray[1];
        }
        log.info("loadUserByUsername: userAccount={}, authUrl={}", userAccount, authUrl);
        User user = this.userService.getByAccountAndAuthUrl(userAccount, authUrl);
        if (user == null) {
            log.error("账号{}不存在", userAccount);
            throw new AuthorizedServiceException(UserAuthAccessExceptionEnum.DEFAULT_CODE.getCode(),
                    "账号:" + userAccount + "不存在");
        }
        // 有效期校验
        long validEndDatetime = DateUtil.parse(user.getValidEndDate(), "yyyy-MM-dd HH:mm:ss").getTime();
        if (DateUtil.date().getTime() > validEndDatetime) {
            throw new AuthorizedServiceException(UserAuthAccessExceptionEnum.USER_EXPIRE.getCode(), "租户:" + authUrl
                    + "-账号:" + userAccount + "-" + UserAuthAccessExceptionEnum.USER_EXPIRE.getMessage());
        }

        Set<GrantedAuthority> authorities = new HashSet<>();
        authorities.add(new SimpleGrantedAuthority("TEST"));
        CustomUserDetail userDetail = new CustomUserDetail(user.getUserAccount(), "", authorities);
        BeanUtils.copyProperties(user, userDetail);
        userDetail.setName(user.getUserName());

        // 用户时间权限
        GroupDateDetail userGroupDateDetail = this.groupService.getGroupDateByCompanyAndUser(user.getCompanyId(),
                user.getUserAccount());
        UserAuthBo userAuthBo = new UserAuthBo();
        if (userGroupDateDetail != null) {
            long startTime = DateUtil.parse(userGroupDateDetail.getStartDate(), "yyyy-MM-dd").getTime();
            long endTime = DateUtil.parse(userGroupDateDetail.getEndDate(), "yyyy-MM-dd").getTime();
            userAuthBo.setStartTime(startTime);
            userAuthBo.setEndTime(endTime);
        }
        userDetail.setUserAuthBo(userAuthBo);
        // 租户信息
        Company company = this.companyService.getById(user.getCompanyId());
        userDetail.setLoginType(company.getLoginType());
        userDetail.setLogo(company.getLogo());
        userDetail.setAuthUrl(company.getAuthUrl());
        userDetail.setCompanyName(company.getCompanyName());
        return userDetail;
    }


}
