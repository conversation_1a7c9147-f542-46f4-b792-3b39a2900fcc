package com.meicloud.voc.security.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 响应结果
 * @param <T>
 */
@Data
@ApiModel("响应结果")
public class Result<T> {
    /**
     * 操作结果0表示成功，1表示失败
     */
    @ApiModelProperty("操作结果0表示成功，1表示失败")
    private Integer resCode;

    /**
     * 操作结果提示
     */
    @ApiModelProperty("操作结果提示")
    private String resMsg;

    /**
     * 数据
     */
    @ApiModelProperty("数据")
    private T data;
}
