package com.meicloud.voc.security.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.meicloud.voc.common.utils.ApiUtil;
import com.meicloud.voc.common.utils.SessionUtil;
import com.meicloud.voc.menu.service.IMenuService;
import com.meicloud.voc.security.api.dto.RequestParams;
import com.meicloud.voc.security.api.service.ISystemService;
import com.meicloud.voc.security.api.vo.MenuInfoVo;
import com.meicloud.voc.security.api.vo.Result;
import com.meicloud.voc.security.api.vo.RoleInfoVo;
import com.meicloud.voc.user.service.IUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "权限管理接口")
@RestController
@RequestMapping("/security")
@Slf4j
public class SystemController {

    @Autowired
	private IMenuService menuService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ISystemService systemService;

	private static final String LOG_MSG_PARAMS = "params:{}";

    @ApiOperation(value = "查询菜单列表")
    @PostMapping("/model/list")
    public Result<Map<String, List<MenuInfoVo>>> getModelList() {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.getModelList()");
    	}
    	Map<String,List<MenuInfoVo>> dataMap = new HashMap<String, List<MenuInfoVo>>();
    	try {
    		List<MenuInfoVo> dataList = menuService.getMenuList(SessionUtil.DEFAULT_COMPANYID);
    		dataMap.put("modelList", dataList);
    	} catch (Exception e) {
    		log.error("查询菜单列表异常", e);
    		return ApiUtil.getSysError();
    	}
		return ApiUtil.getSuccess(dataMap);
    }
    @ApiOperation(value = "查询用户列表")
    @RequestMapping("/user/list")
    public Result<Map<String, Object>> getUserList(@RequestBody RequestParams params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.getUserList()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	Map<String, Object> dataMap = new HashMap<String, Object>();
    	try {
    		dataMap = userService.getUserList(params.getUserAccount(), params.getUserName(), params.getPageNum(), params.getPageSize());
		} catch (Exception e) {
			log.error("查询用户列表", e);
			return ApiUtil.getSysError();
		}
    	return ApiUtil.getSuccess(dataMap);
    }
    
    @ApiOperation(value = "查询角色")
    @RequestMapping("/role/list")
    public Result<Map<String, List<RoleInfoVo>>> getRoleList(Integer roleId, String roleName) {
        if (log.isDebugEnabled()) {
            log.debug("SystemController.getRoleList()");
        }
        log.info("roleId:{},roleName:{}", roleId, roleName);
        Map<String,List<RoleInfoVo>> dataMap = new HashMap<String, List<RoleInfoVo>>();
        try {
        	List<RoleInfoVo> roleList = systemService.getRoleList(roleId, roleName);
        	dataMap.put("roles", roleList);
		} catch (Exception e) {
			log.error("查询角色异常", e);
			return ApiUtil.getSysError();
		}
        return ApiUtil.getSuccess(dataMap);
    }
    
    @ApiOperation(value = "角色创建")
    @PostMapping("/role/save")
    public Result<String> roleSave(@RequestBody RequestParams params) {
        if (log.isDebugEnabled()) {
            log.debug("SystemController.roleSave()");
        }
        log.info(LOG_MSG_PARAMS, params);
        int createRole = 0;
        try {
        	createRole = systemService.createRole(params);
        	if(createRole == -1) {
    			return ApiUtil.getDataExist("角色名称已存在");
    		}
		} catch (Exception e) {
			log.error("角色创建异常", e);
			return ApiUtil.getSysError();
		}
        return ApiUtil.getSuccess(createRole+"");
    }
    
    @ApiOperation(value = "角色修改")
    @PostMapping("/role/update")
    public Result<String> roleUpdate(@RequestBody RequestParams params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleUpdate()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int updateRole = 0;
    	try {
    		updateRole = systemService.updateRole(params);
    		if(updateRole == -1) {
    			return ApiUtil.getDataExist("角色名称已存在");
    		}
		} catch (Exception e) {
			log.error("角色修改异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(updateRole+"");
    }
    
    @ApiOperation(value = "角色删除")
    @PostMapping("/role/delete")
    public Result<Integer> roleDelete(@RequestBody RequestParams params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleDelete()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int updateRole = 0;
    	try {
    		updateRole = systemService.deleteRole(params.getRoleId());
		} catch (Exception e) {
			log.error("角色修改异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(updateRole);
    }
    
    @ApiOperation(value = "查询角色用户")
    @PostMapping("/role/queryRoleUser")
    public Result<Map<String, Object>> queryRoleUser(@RequestBody RequestParams params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.queryRoleUser()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	Map<String, Object> dataMap = null;
    	try {
    		dataMap = systemService.getRoleUserList(params);
		} catch (Exception e) {
			log.error("查询角色用户异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(dataMap);
    }
    
    @ApiOperation(value = "角色添加用户")
    @PostMapping("/role/roleAddUser")
    public Result<Integer> roleAddUser(@RequestBody String params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleAddUser()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int roleAddUserList = 0;
    	try {
    		roleAddUserList = systemService.roleAddUserList(params);
		} catch (Exception e) {
			log.error("角色添加用户异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(roleAddUserList);
    }
    
    @ApiOperation(value = "角色删除用户")
    @PostMapping("/role/roleDelUser")
    public Result<Integer> roleDelUser(@RequestBody String params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleDelUser()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int roleDeleteUserList = 0;
    	try {
    		roleDeleteUserList = systemService.roleDeleteUserList(params);
		} catch (Exception e) {
			log.error("角色删除用户异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(roleDeleteUserList);
    }
    
    @ApiOperation(value = "查询角色菜单")
    @PostMapping("/role/queryRoleModel")
    public Result<Map<String, List<MenuInfoVo>>> queryRoleModel(@RequestBody RequestParams params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.queryRoleModel()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	Map<String, List<MenuInfoVo>> dataMap = new HashMap<String, List<MenuInfoVo>>();
    	try {
    		List<MenuInfoVo> roleMenuList = systemService.getRoleMenuList(params);
    		dataMap.put("modelList", roleMenuList);
		} catch (Exception e) {
			log.error("查询角色菜单异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(dataMap);
    }
    
    @ApiOperation(value = "角色添加菜单")
    @PostMapping("/role/roleAddModel")
    public Result<Integer> roleAddModel(@RequestBody String params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleAddModel()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int roleAddMenuList = 0;
    	try {
//    		roleAddMenuList = systemService.roleAddMenuList(params);
    		roleAddMenuList = systemService.roleUpdateMenuList(params);
		} catch (Exception e) {
			log.error("角色添加菜单异常", e);
			return ApiUtil.getSysError(); 
		}
		return ApiUtil.getSuccess(roleAddMenuList);
    }
    
    @ApiOperation(value = "角色删除菜单")
    @PostMapping("/role/roleDelModel")
    public Result<Integer> roleDelModel(@RequestBody String params) {
    	if (log.isDebugEnabled()) {
    		log.debug("SystemController.roleDelModel()");
    	}
    	log.info(LOG_MSG_PARAMS, params);
    	int roleAddMenuList = 0;
    	try {
    		roleAddMenuList = systemService.roleDeleteMenuList(params);
		} catch (Exception e) {
			log.error("角色删除菜单异常", e);
			return ApiUtil.getSysError();
		}
		return ApiUtil.getSuccess(roleAddMenuList);
    }
    
}
