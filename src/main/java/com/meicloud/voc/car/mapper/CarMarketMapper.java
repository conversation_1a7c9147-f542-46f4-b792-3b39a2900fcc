package com.meicloud.voc.car.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meicloud.voc.car.entity.CarMarket;

//@DS("datachangan")
public interface CarMarketMapper extends BaseMapper<CarMarket> {

	/**
	 * 查询细分市场
	 * 
	 * @param brandIds
	 * @return
	 */
	List<CarMarket> getSearchCarMarket(@Param("brandCodes") List<String> brandCodes);

}
