package com.meicloud.voc.car.utils;

import com.meicloud.voc.car.CarHelper;
import com.meicloud.voc.car.enums.EsIndexType;
import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.ESUtil;
import com.meicloud.voc.utils.BeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * voc项目的通用es query builder 创建
 */
public class EsQueryBuilder {
    private BoolQueryBuilder boolQueryBuilder;
    private EsIndexType esIndexType;


    private EsQueryBuilder(EsIndexType esIndexType) {
        this.esIndexType = esIndexType;
        this.boolQueryBuilder = new BoolQueryBuilder();
    }

    /**
     * must 指标体系
     *
     * @param requestParams
     */
    public void mustIndexType(RequestParams requestParams) {
        mustIndexType(requestParams.getIndexTypeName());
    }

    /**
     * must 指标体系
     *
     * @param indexType
     */
    public void mustIndexType(String indexType) {
        if (StringUtils.isNotEmpty(indexType)) {
            if(EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_INDEX_TYPE, indexType));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_INDEX_TYPE, indexType));
            }
        }
    }

    /**
     * range 日期
     */
    public void rangeDate(RequestParams requestParams) {
        rangeDate(requestParams.getStartDate(), requestParams.getEndDate());
    }

    /**
     * range 日期
     *
     * @param startTime
     * @param endTime
     */
    public void rangeDate(String startTime, String endTime) {
        rangeGteDate(startTime);
        rangeLteDate(endTime);
    }

    /**
     * 大于某个时间
     *
     * @param startTime
     */
    public void rangeGteDate(String startTime) {
        if (StringUtils.isNotEmpty(startTime)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.DWD_CREATE_TIME).gte(startTime));
            } else {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.AGG_DATA_DATE).gte(startTime).timeZone(DateUtil.ASIA_SHANGHAI));
            }

        }
    }

    /**
     * 小于某个时间
     *
     * @param endTime
     */
    public void rangeLteDate(String endTime) {
        if (StringUtils.isNotEmpty(endTime)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.DWD_CREATE_TIME).lte(endTime));
            } else {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.AGG_DATA_DATE).lte(endTime).timeZone(DateUtil.ASIA_SHANGHAI));
            }
        }
    }

    /**
     * must 数据源条件
     *
     * @param requestParams
     */
    public void mustDataSource(RequestParams requestParams) {
        mustDataSource(requestParams.getDataSources());
    }

    /**
     * must 数据源条件
     *
     * @param dataSources
     */
    public void mustDataSource(List<String> dataSources) {
        if (!CollectionUtils.isEmpty(dataSources)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.DWD_DATA_SOURCE_NAME, dataSources));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_DATA_SOURCE, dataSources));
            }
        }
    }


    /**
     * must 前对数据进行处理
     * @param boolQueryBuilder
     * @param field
     * @param value
     */
    public void mustDtlExtractInfoFieldSplit(BoolQueryBuilder boolQueryBuilder, String field, String value) {
        if(StringUtils.isNotBlank(value)) {
            String[] values = value.split(",");
            mustDtlExtractInfoField(boolQueryBuilder, field, values);
        }
    }


    /**
     * must详情的extractInfo的相关信息
     * @param boolQueryBuilder
     * @param field
     * @param values
     */
    public void mustDtlExtractInfoField(BoolQueryBuilder boolQueryBuilder, String field, Collection<String> values) {
        if(values != null && values.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(field, values));
        }
    }



    /**
     * must详情的extractInfo的相关信息
     *
     * @param boolQueryBuilder
     * @param field
     * @param value
     */
    public void mustDtlExtractInfoField(BoolQueryBuilder boolQueryBuilder, String field, String... value) {
        if (StringUtils.isNotEmpty(field) && value != null && value.length > 0) {
            if(value.length == 1 && StringUtils.isBlank(value[0])) {
                return;
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery(field, value));
        }
    }

    /**
     * 判断是否执行nested
     * @param requestParams
     */
    public boolean isNestedExtractedInfo(RequestParams requestParams) {
        if(StringUtils.isNotBlank(requestParams.getIndexTypeName())) return true;
        if(StringUtils.isNotBlank(requestParams.getFirstIndexId())) return true;
        if(StringUtils.isNotBlank(requestParams.getSecondIndexId())) return true;
        if(StringUtils.isNotBlank(requestParams.getThirdIndexId())) return true;
        if(StringUtils.isNotBlank(requestParams.getFourIndexId())) return true;
        if(StringUtils.isNotBlank(requestParams.getStandardKeyword())) return true;
        if(StringUtils.isNotBlank(requestParams.getEmotionAttribute())) return true;
        if(StringUtils.isNotBlank(requestParams.getUserId())) return true;  // 当查询用户的时候只会查询有分析结果的数据
        return false;
    }

    private void filterBlankQuery(BoolQueryBuilder boolQueryBuilder, String field) {
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(field, ""));
        boolQueryBuilder.must(QueryBuilders.existsQuery(field));
    }
    /**
     * must详情的extractInfo的相关信息
     * @param requestParams
     * @return
     */
    public void nestedExtractedInfoDtl(RequestParams requestParams) {
        BoolQueryBuilder nestBoolQueryBuilder = QueryBuilders.boolQuery();
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_INDEX_TYPE, requestParams.getIndexTypeName());
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_FIRST_INDEX_ID, requestParams.getFirstIndexId());
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_SECOND_INDEX_ID, requestParams.getSecondIndexId());
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_THIRD_INDEX_ID, requestParams.getThirdIndexId());
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_FOUR_INDEX_ID, requestParams.getFourIndexId());

        // 判断是否不匹配结果
        if(requestParams.getUnMatch() == 0) {
            filterBlankQuery(nestBoolQueryBuilder, ESUtil.DWD_INDEX_TYPE);
            filterBlankQuery(nestBoolQueryBuilder, ESUtil.DWD_FIRST_INDEX_ID);
            filterBlankQuery(nestBoolQueryBuilder, ESUtil.DWD_SECOND_INDEX_ID);
            filterBlankQuery(nestBoolQueryBuilder, ESUtil.DWD_THIRD_INDEX_ID);
            filterBlankQuery(nestBoolQueryBuilder, ESUtil.DWD_FOUR_INDEX_ID);
        }

        // 因为前端到的关键词可能有两个都用到，所以后端增加两个的参数进来
        mustDtlExtractInfoFieldSplit(nestBoolQueryBuilder, ESUtil.DWD_STANDARD_KEYWORD, requestParams.getStandardKeyword());
        mustDtlExtractInfoField(nestBoolQueryBuilder, ESUtil.DWD_STANDARD_KEYWORD, requestParams.getStandardKeywords());

        mustDtlExtractInfoField(nestBoolQueryBuilder, ESUtil.DWD_EMOTION_ATTRIBUTE, requestParams.getEmotionAttribute());

        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery(ESUtil.DWD_EXTRACTED_INFO,
                nestBoolQueryBuilder, ScoreMode.None);
        boolQueryBuilder.must(QueryBuilders.nestedQuery(ESUtil.DWD_ANALYSIS_RESULT, nestedQueryBuilder, ScoreMode.None));
    }

    /**
     * dwd 表的analysisResult里的extractedinfo nested
     *
     * @param field
     * @param values
     * @return
     */
    public NestedQueryBuilder nestedExtractedInfo(String field, List<String> values) {
        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery(ESUtil.DWD_EXTRACTED_INFO,
                QueryBuilders.termsQuery(field, values), ScoreMode.None);
        return QueryBuilders.nestedQuery(ESUtil.DWD_ANALYSIS_RESULT, nestedQueryBuilder, ScoreMode.None);
    }

    public NestedQueryBuilder nestedExtractedInfo(String field, String... value) {
        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery(ESUtil.DWD_EXTRACTED_INFO,
                QueryBuilders.termsQuery(field, value), ScoreMode.None);
        return QueryBuilders.nestedQuery(ESUtil.DWD_ANALYSIS_RESULT, nestedQueryBuilder, ScoreMode.None);
    }

    /**
     * must 一级指标
     *
     * @param requestParams
     */
    public void mustFirstIndexId(RequestParams requestParams) {
        if (StringUtils.isNotBlank(requestParams.getFirstIndexId())) {
            List<String> firstIndexIds = Arrays.asList(requestParams.getFirstIndexId().split(","));
            mustFirstIndexId(firstIndexIds);
        }
    }


    /**
     * must 一级指标
     *
     * @param firstIndexIds
     */
    public void mustFirstIndexId(List<String> firstIndexIds) {
        if (!CollectionUtils.isEmpty(firstIndexIds)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FIRST_INDEX_ID, firstIndexIds));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FIRST_INDEX_ID, firstIndexIds));
            }
        }
    }

    /**
     * must 一级指标
     *
     * @param firstIndexId
     */
    public void mustFirstIndexId(String firstIndexId) {
        if (StringUtils.isNotBlank(firstIndexId)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FIRST_INDEX_ID, firstIndexId));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FIRST_INDEX_ID, firstIndexId));
            }
        }
    }

    /**
     * must 二级指标
     *
     * @param requestParams
     */
    public void mustSecondIndexId(RequestParams requestParams) {
        if (StringUtils.isNotBlank(requestParams.getSecondIndexId())) {
            List<String> secondIndexIds = Arrays.asList(requestParams.getSecondIndexId().split(","));
            mustSecondIndexId(secondIndexIds);
        }
    }

    /**
     * must 二级指标
     *
     * @param secondIndexIds
     */
    public void mustSecondIndexId(List<String> secondIndexIds) {
        if (!CollectionUtils.isEmpty(secondIndexIds)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_SECOND_INDEX_ID, secondIndexIds));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_SECOND_INDEX_ID, secondIndexIds));
            }
        }
    }

    /**
     * must 二级指标
     *
     * @param secondIndexId
     */
    public void mustSecondIndexId(String secondIndexId) {
        if (StringUtils.isNotBlank(secondIndexId)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_SECOND_INDEX_ID, secondIndexId));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_SECOND_INDEX_ID, secondIndexId));
            }
        }
    }

    /**
     * must 三级指标
     *
     * @param requestParams
     */
    public void mustThirdIndexId(RequestParams requestParams) {
        if (StringUtils.isNotBlank(requestParams.getThirdIndexId())) {
            List<String> thirdIndexIds = Arrays.asList(requestParams.getThirdIndexId().split(","));
            mustThirdIndexId(thirdIndexIds);
        }
    }

    /**
     * must 三级指标
     *
     * @param thirdIndexIds
     */
    public void mustThirdIndexId(List<String> thirdIndexIds) {
        if (!CollectionUtils.isEmpty(thirdIndexIds)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_THIRD_INDEX_ID, thirdIndexIds));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_THIRD_INDEX_ID, thirdIndexIds));
            }
        }
    }

    /**
     * must 三级指标
     *
     * @param thirdIndexId
     */
    public void mustThirdIndexId(String thirdIndexId) {
        if (StringUtils.isNotBlank(thirdIndexId)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_THIRD_INDEX_ID, thirdIndexId));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_THIRD_INDEX_ID, thirdIndexId));
            }
        }
    }


    /**
     * must 四级指标
     *
     * @param requestParams
     */
    public void mustFourIndexId(RequestParams requestParams) {
        if (StringUtils.isNotBlank(requestParams.getFourIndexId())) {
            List<String> fourthIndexIds = Arrays.asList(requestParams.getFourIndexId().split(","));
            mustFourIndexId(fourthIndexIds);
        }
    }

    /**
     * must 四级指标
     *
     * @param fourIndexIds
     */
    public void mustFourIndexId(List<String> fourIndexIds) {
        if (!CollectionUtils.isEmpty(fourIndexIds)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FOUR_INDEX_ID, fourIndexIds));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FOUR_INDEX_ID, fourIndexIds));
            }
        }
    }

    /**
     * must 四级指标
     *
     * @param fourIndexId
     */
    public void mustFourIndexId(String fourIndexId) {
        if (StringUtils.isNotBlank(fourIndexId)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FOUR_INDEX_ID, fourIndexId));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FOUR_INDEX_ID, fourIndexId));
            }
        }
    }

    /**
     * must 市场名
     *
     * @param requestParams
     */
    public void mustMarketName(RequestParams requestParams) {
        mustMarketName(requestParams.getMarketNames());
    }

    /**
     * must 市场名
     *
     * @param marketNames
     */
    public void mustMarketName(List<String> marketNames) {
        if (!CollectionUtils.isEmpty(marketNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_MARKET_NAME, marketNames));
        }
    }


    /**
     * must 情感
     *
     * @param requestParams
     */
    public void mustEmotionAttribute(RequestParams requestParams) {
        mustEmotionAttribute(requestParams.getEmotionAttribute());
    }

    /**
     * must 情感
     *
     * @param emotionAttribute
     */
    public void mustEmotionAttribute(String emotionAttribute) {
        if (StringUtils.isNotBlank(emotionAttribute)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_EMOTION_ATTRIBUTE, emotionAttribute));
            } else {
                boolQueryBuilder.must(QueryBuilders.termQuery(ESUtil.EMOTION_ATTRIBUTE, emotionAttribute));
            }
        }
    }

    /**
     * must 是否外部数据
     *
     * @param requestParams
     */
    public void mustIsOuter(RequestParams requestParams) {
        mustIsOuter(requestParams.getIsOuter());
    }

    /**
     * must 是否外部数据
     *
     * @param isOuter
     */
    public void mustIsOuter(String isOuter) {
        if (StringUtils.isNotBlank(isOuter)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(ESUtil.IS_OUTER, isOuter));
        }
    }

    /**
     * must 车系名
     *
     * @param requestParams
     */
    public void mustSeriesName(RequestParams requestParams) {
        mustSeriesName(requestParams.getSeriesNames());
    }

    /**
     * must 车系名
     *
     * @param requestParams
     */
    public void mustModelName(RequestParams requestParams) {
        mustModelName(requestParams.getModelNames());
    }

    /**
     * must 车型
     *
     * @param modelNames
     */
    public void mustModelName(List<String> modelNames) {
        if (!CollectionUtils.isEmpty(modelNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_MODEL_NAME, modelNames));
        }
    }

    /**
     * must 车系名
     *
     * @param seriesNames
     */
    public void mustSeriesName(List<String> seriesNames) {
        if (!CollectionUtils.isEmpty(seriesNames)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.filter(QueryBuilders.existsQuery(ESUtil.AGG_BRAND_NAME));
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_SERIES_NAME, seriesNames));
        }
    }


    /**
     * must 长安集团
     */
    public void mustChanganGroup() {
        BoolQueryBuilder allBrandBuild = QueryBuilders.boolQuery();
        //  当品牌名为空时，即默认所有的品牌，对应的是长安4个品牌外部数据+长安内部数据
        CarHelper carHelper = BeanUtil.getBean(CarHelper.class);
        List<String> changanBrandNames = carHelper != null ? carHelper.getChanganBrandNames() : null;
        if (changanBrandNames != null && !changanBrandNames.isEmpty()) {
            allBrandBuild.should(QueryBuilders.termsQuery(ESUtil.AGG_BRAND_NAME, changanBrandNames)); // 长安4个品牌数据
        }
        allBrandBuild.should(QueryBuilders.termsQuery(ESUtil.IS_OUTER, "否")); // 内部数据
        boolQueryBuilder.must(allBrandBuild);
    }

    /**
     * must 品牌名
     *
     * @param requestParams
     */
    public void mustBrandName(RequestParams requestParams) {
        mustBrandName(requestParams.getBrandNames(), requestParams.isChanganGroup());
    }

    /**
     * must 品牌名
     *
     * @param brandNames
     * @param changanGroup
     */
    public void mustBrandName(List<String> brandNames, boolean changanGroup) {
        if (!CollectionUtils.isEmpty(brandNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_BRAND_NAME, brandNames));
        } else {
            if (changanGroup) {
                mustChanganGroup();
            }
        }
    }

    /**
     * must 是否关键词
     *
     * @param requestParams
     */
    public void mustStandardKeyword(RequestParams requestParams) {
        mustStandardKeyword(requestParams.getStandardKeyword());
    }

    /**
     * must 是否多个标准关键词
     * @param requestParams
     */
    public void mustStandardKeywords(RequestParams requestParams) {
        mustStandardKeywords(requestParams.getStandardKeywords());
    }

    /**
     * must 是否关键词
     *
     * @param standardKeyword
     */
    public void mustStandardKeyword(String standardKeyword) {
        if (StringUtils.isNotEmpty(standardKeyword)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                NestedQueryBuilder nestedQueryBuilder = nestedExtractedInfo(ESUtil.DWD_STANDARD_KEYWORD, standardKeyword);
                boolQueryBuilder.must(nestedQueryBuilder);
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_STANDARDKEYWORD, standardKeyword));

            }
        }
    }

    /**
     * must 是否多个标准关键词
     * @param standardKeywords
     */
    public void mustStandardKeywords(List<String> standardKeywords) {
        if (!CollectionUtils.isEmpty(standardKeywords)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                NestedQueryBuilder nestedQueryBuilder = nestedExtractedInfo(ESUtil.DWD_STANDARD_KEYWORD, standardKeywords);
                boolQueryBuilder.must(nestedQueryBuilder);
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_STANDARDKEYWORD, standardKeywords));

            }
        }
    }

    /**
     * must 关键词
     *
     * @param standardKeywords
     */
    public void mustStandardKeyword(List<String> standardKeywords) {
        if (!CollectionUtils.isEmpty(standardKeywords)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                NestedQueryBuilder nestedQueryBuilder = nestedExtractedInfo(ESUtil.DWD_STANDARD_KEYWORD, standardKeywords);
                boolQueryBuilder.must(nestedQueryBuilder);
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_STANDARDKEYWORD, standardKeywords));

            }
        }
    }

    /**
     * must 省份
     *
     * @param requestParams
     */
    public void mustProvince(RequestParams requestParams) {
        mustProvince(requestParams.getProvinces());
    }

    /**
     * must 省份
     *
     * @param provinces
     */

    public void mustProvince(List<String> provinces) {
        if (!CollectionUtils.isEmpty(provinces)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_PROVINCE, provinces));
        }
    }

    /**
     * must oneId
     *
     * @param requestParams
     */
    public void mustOneId(RequestParams requestParams) {
        mustOneId(requestParams.getUserId());
    }

    /**
     * must oneId
     *
     * @param oneId
     */
    public void mustOneId(String oneId) {
        if (EsIndexType.DWD_DTL.equals(esIndexType)) {
            if (StringUtils.isNotBlank(oneId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery(ESUtil.DWD_ONE_ID, oneId));
            }
        }
    }

    /**
     * must 是否车主
     *
     * @param requestParams
     */
    public void mustCarOwner(RequestParams requestParams) {
        mustCarOwner(requestParams.getCarOwner());
    }

    /**
     * must 是否车主
     *
     * @param carOwner
     */
    public void mustCarOwner(Integer carOwner) {
        if (carOwner != null && carOwner > 0) {
            if (carOwner == 1) {
                boolQueryBuilder.must(QueryBuilders.termQuery("isCarOwner", 1));
            } else if (carOwner == 2) {
                //TODO  后续刷数后可能用 0查询，需要刷数，先临时处理
//                boolQueryBuilder.must(QueryBuilders.termQuery("isCarOwner", 0));
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("isCarOwner", 1));
            }
        }
    }

    /**
     * 关键词模糊查询(暂时不支持dwd明细表)
     *
     * @param standardKeywords
     */
    public void fuzzyStandardKeyword(String standardKeywords) {
        if (!EsIndexType.DWD_DTL.equals(esIndexType)) {
            if (StringUtils.isNotEmpty(standardKeywords)) {
                // 模糊查询
                //增加对中文逗号的支持
                standardKeywords = standardKeywords.trim().replaceAll("，", ",");
                String names = standardKeywords.replaceAll("(\\s{0,},\\s{0,})", "|");
                RegexpQueryBuilder regexpQueryBuilder = QueryBuilders.regexpQuery(ESUtil.AGG_STANDARDKEYWORD, ".*(" + names + ").*");
                boolQueryBuilder.must(regexpQueryBuilder);
            } else {
                boolQueryBuilder.must(QueryBuilders.existsQuery(ESUtil.AGG_STANDARDKEYWORD));
            }
        }
    }

    /**
     * must 一级指标
     *
     * @param firstIndexNames
     */
    public void mustFirstIndexName(List<String> firstIndexNames) {
        if (!CollectionUtils.isEmpty(firstIndexNames)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FIRST_INDEX_NAME, firstIndexNames));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FIRST_INDEX_NAME, firstIndexNames));
            }
        }
    }

    /**
     * must 一级指标
     *
     * @param firstIndexName
     */
    public void mustFirstIndexName(String firstIndexName) {
        if (StringUtils.isNotBlank(firstIndexName)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FIRST_INDEX_NAME, firstIndexName));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FIRST_INDEX_NAME, firstIndexName));
            }
        }
    }

    /**
     * must 二级指标
     *
     * @param secondIndexNames
     */
    public void mustSecondIndexName(List<String> secondIndexNames) {
        if (!CollectionUtils.isEmpty(secondIndexNames)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_SECOND_INDEX_NAME, secondIndexNames));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_SECOND_INDEX_NAME, secondIndexNames));
            }
        }
    }

    /**
     * must 二级指标
     *
     * @param secondIndexName
     */
    public void mustSecondIndexName(String secondIndexName) {
        if (StringUtils.isNotBlank(secondIndexName)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_SECOND_INDEX_NAME, secondIndexName));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_SECOND_INDEX_NAME, secondIndexName));
            }
        }
    }

    /**
     * must 三级指标
     *
     * @param thirdIndexNames
     */
    public void mustThirdIndexName(List<String> thirdIndexNames) {
        if (!CollectionUtils.isEmpty(thirdIndexNames)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_THIRD_INDEX_NAME, thirdIndexNames));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_THIRD_INDEX_NAME, thirdIndexNames));
            }
        }
    }

    /**
     * must 三级指标
     *
     * @param thirdIndexName
     */
    public void mustThirdIndexName(String thirdIndexName) {
        if (StringUtils.isNotBlank(thirdIndexName)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_THIRD_INDEX_NAME, thirdIndexName));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_THIRD_INDEX_NAME, thirdIndexName));
            }
        }
    }

    /**
     * must 四级指标
     *
     * @param fourIndexNames
     */
    public void mustFourIndexName(List<String> fourIndexNames) {
        if (!CollectionUtils.isEmpty(fourIndexNames)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FOUR_INDEX_NAME, fourIndexNames));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FOUR_INDEX_NAME, fourIndexNames));
            }
        }
    }

    /**
     * must 四级指标
     *
     * @param fourIndexName
     */
    public void mustFourIndexName(String fourIndexName) {
        if (StringUtils.isNotBlank(fourIndexName)) {
            if (EsIndexType.DWD_DTL.equals(esIndexType)) {
                boolQueryBuilder.must(nestedExtractedInfo(ESUtil.DWD_FOUR_INDEX_NAME, fourIndexName));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_FOUR_INDEX_NAME, fourIndexName));
            }
        }
    }

    /**
     * must  charge人
     * @param charge
     */
    public void mustCharge(String charge) {
        if (StringUtils.isNotBlank(charge)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_CHARGE, charge));
        }
    }

    /**
     * must 部门
     * @param department
     */
    public void mustDepartment(String department) {
        if (StringUtils.isNotBlank(department)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_DEPARTMENT, department));
        }
    }

    /**
     * must 清晰度
     * @param clarity
     */
    public void mustClarity(String clarity) {
        if (StringUtils.isNotBlank(clarity)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.AGG_CLARITY, clarity));
        }
    }


    /**
     * must field
     * @param field
     */
    public void mustField(String field) {
        if (StringUtils.isNotBlank(field)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.FIELD, field));
        }
    }

    public void mustId(RequestParams requestParams){
        this.mustId(requestParams.getId());
    }



    /**
     * 根据数据查找
     * @param id
     */
    public void mustId(String id) {
        if (StringUtils.isNotBlank(id)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(ESUtil.DATA_ID, id));
        }
    }

    /**
     * 公用查询条件
     */
    public void  commonQuery(RequestParams requestParams) {
        rangeDate(requestParams);

        //指标体系
        if(EsIndexType.DWD_DTL.equals(esIndexType)){
            if(isNestedExtractedInfo(requestParams)) {
                nestedExtractedInfoDtl(requestParams);
            }
        } else {
            mustIndexType(requestParams);
            mustFirstIndexId(requestParams);
            mustSecondIndexId(requestParams);
            mustThirdIndexId(requestParams);
            mustFourIndexId(requestParams);
            mustStandardKeyword(requestParams);
            mustStandardKeywords(requestParams);
            mustEmotionAttribute(requestParams);
        }

        // 品牌
        mustMarketName(requestParams);
        mustBrandName(requestParams);
        mustSeriesName(requestParams);

        // 数据源
        mustDataSource(requestParams);
        mustIsOuter(requestParams);
    }


    /**
     * 初始化 esQueryBuilder
     *
     * @param esIndexType
     * @return
     */
    public static EsQueryBuilder init(EsIndexType esIndexType) {
        EsQueryBuilder esQueryBuilder = new EsQueryBuilder(esIndexType);
        return esQueryBuilder;
    }


    /**
     * 通用的 keywordMention
     *
     * @param requestParams
     */
    private void commonKeywordMention(RequestParams requestParams) {
        commonQuery(requestParams);
    }

    /**
     * 通用的 keywordMentionModel
     *
     * @param requestParams
     */
    private void commonKeywordMentionModel(RequestParams requestParams) {
        commonQuery(requestParams);
        mustProvince(requestParams);
        mustModelName(requestParams);
    }

    /**
     * 通用的 keywordMentionRegion
     *
     * @param requestParams
     */
    private void commonKeywordMentionRegion(RequestParams requestParams) {
        commonQuery(requestParams);
        mustProvince(requestParams);
    }


    /**
     * 通用 brandMention
     *
     * @param requestParams
     */
    private void commonBrandMention(RequestParams requestParams) {
        commonQuery(requestParams);
    }

    /**
     * 通用的 keywordMentionTotal
     *
     * @param requestParams
     */
    private void commonKeywordMentionTotal(RequestParams requestParams) {
        commonQuery(requestParams);
    }

    /**
     * 通用的 dwdDtl
     *
     * @param requestParams
     */
    private void commonDwdDtl(RequestParams requestParams) {
        commonQuery(requestParams);
        mustId(requestParams);
        mustOneId(requestParams);
        mustProvince(requestParams);
        mustModelName(requestParams);
        mustCarOwner(requestParams);
    }


    /**
     * 生成查询条件
     *
     * @param requestParams
     * @return
     */
    public EsQueryBuilder setParams(RequestParams requestParams) {
        switch (esIndexType) {
            case KEYWORD_MENTION:
                commonKeywordMention(requestParams);
                break;
            case KEYWORD_MENTION_MODEL:
                commonKeywordMentionModel(requestParams);
                break;
            case KEYWORD_MENTION_REGION:
                commonKeywordMentionRegion(requestParams);
                break;
            case BRAND_MENTION:
                commonBrandMention(requestParams);
                break;
            case KEYWORD_MENTION_TOTAL:
                commonKeywordMentionTotal(requestParams);
                break;
            case DWD_DTL:
                commonDwdDtl(requestParams);
                break;
            default:
                commonQuery(requestParams);
                break;
        }
        return this;
    }

    /**
     * 生成
     *
     * @return
     */
    public BoolQueryBuilder build() {
        return boolQueryBuilder;
    }

}
