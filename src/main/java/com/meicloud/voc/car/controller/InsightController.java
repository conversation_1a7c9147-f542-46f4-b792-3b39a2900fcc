package com.meicloud.voc.car.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.meicloud.voc.car.service.IInsightService;
import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.dto.Result;
import com.meicloud.voc.common.dto.ResultParams;
import com.meicloud.voc.common.enums.IndexTypeEnum;
import com.meicloud.voc.common.enums.SortTypeEnum;
import com.meicloud.voc.common.utils.DataUtil;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.common.utils.ESUtil;
import com.meicloud.voc.common.utils.JsonUtil;
import com.meicloud.voc.common.utils.ListUtil;
import com.meicloud.voc.common.utils.ResultUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 洞察报告
 */
@Api(tags = "洞察报告")
@RestController
@RequestMapping("/insight")
@Slf4j
public class InsightController {

    @Resource
    private IInsightService insightService;

    /**
     * 体验值
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "体验值")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/experience")
    public Result<Map<String, Object>> getExperience(@RequestBody RequestParams params) {
        log.info("getExperience=" + JsonUtil.toJsonStr(params));
        Map<String, Object> dataMap;
        try {
        	Map<String, String> indexMap;
            if (IndexTypeEnum.CPT.getCode().equals(params.getIndexType())) {
                dataMap = insightService.getVocExperienceMap(params, ESUtil.AGG_SECOND_INDEX_ID);
                indexMap = DataUtil.getSecondIndexMap(params.getIndexType());
            } else {
                dataMap = insightService.getVocExperienceMap(params, ESUtil.AGG_FIRST_INDEX_ID);
                indexMap = DataUtil.getFirstIndexMap(params.getIndexType());
            }
            List<ResultParams> dataList = (List<ResultParams>) dataMap.get("dataList");
            Map<String, String> addIndexMap = new HashMap<String, String>();
            for (Entry<String, String> entry : indexMap.entrySet()) {
            	boolean isAdd = false;
            	String indexId = entry.getKey();
            	for (ResultParams info : dataList) {
            		if(indexId.equals(info.getKeyWord())) {
            			isAdd = true;
            			break;
            		}
            	}
            	if(isAdd || CollectionUtils.isEmpty(dataList)) {
            		addIndexMap.put(indexId, entry.getValue());
            	}
            }
            for (Entry<String, String> entry : addIndexMap.entrySet()) {
            	ResultParams info = new ResultParams();
            	info.setKeyCode(entry.getKey());
            	info.setKeyWord(entry.getValue());
            	info.setExperienceValue(0.0);
            	info.setPositiveMentionValue(0L);
            	info.setNeutralMentionValue(0L);
            	info.setNegativeMentionValue(0L);
            	info.setTotalMentionValue(0L);
				dataList.add(info);
            }
            ListUtil.sortList(dataList, SortTypeEnum.INDEX_ID_ASC);
        } catch (Exception e) {
            log.error("获取洞察报告体验值异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(dataMap);
    }

    /**
     * 获取指定类型的人群特征
     *
     * @param params
     * @param dataType
     * @return
     */
    @ApiOperation(value = "获取指定类型的人群特征")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/population/dataType")
    public Result<Map<String, Object>> getPopulationByType(@RequestBody RequestParams params,
                                                           @ApiParam(name = "dataType", value = "isCarOwner车主，province省份,seriesCode车系，city城市级别, gender性别，age年龄")
                                                           @RequestParam(required = false) String dataType) {
        log.info("getPopulationByType=" + JsonUtil.toJsonStr(params));
        Map<String, Object> dataMap = null;
        try {
            dataMap = insightService.getCrowdFeatures(params, dataType);
        } catch (Exception e) {
            log.error("获取人群特征异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(dataMap);
    }

    /**
     * 人群特征
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "人群特征")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/population")
    public Result<Map<String, Object>> getPopulation(@RequestBody RequestParams params) {
        log.info("getPopulation=" + JsonUtil.toJsonStr(params));
        Map<String, Object> dataMap = new HashMap<>();
        try {
            dataMap = insightService.getCrowdFeatures(params, null);
        } catch (Exception e) {
            log.error("获取人群特征异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(dataMap);
    }

    public Map<String, Object> setPopulationMap(Map<String, Object> carMap) {
        Map<String, Object> map = new HashMap<>();
        List<ResultParams> dataList = (List<ResultParams>) carMap.get(ESUtil.DETAILS);
        if (dataList == null) {
            return map;
        }
        for (ResultParams info : dataList) {
            map.put(info.getKeyWord(), info.getMentionRate());
        }
        return map;
    }


    /**
     * 小安吐槽
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "小安吐槽")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/tsukkomi")
    public Result<List<ResultParams>> getTsukkomi(@RequestBody RequestParams params) {
        log.info("getTsukkomi=" + JsonUtil.toJsonStr(params));
        List<ResultParams> experienceList;
        try {
            List<String> dataSources = new ArrayList<>();
            dataSources.add("incallAPP-小安吐槽");
            params.setDataSources(dataSources);
            experienceList = insightService.getTsukkomi(params);
        } catch (Exception e) {
            log.error("获取小安吐槽异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(experienceList);
    }

    @ApiOperation(value = "指标排名")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/ranking")
    public Result<Map<String, List<ResultParams>>> getRanking(@RequestBody RequestParams params) {
        log.info("getRanking=" + JsonUtil.toJsonStr(params));
        Map<String, List<ResultParams>> dataMap = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(params.getFourIndexId())) {
                dataMap = insightService.getRankingMap(params, ESUtil.AGG_STANDARDKEYWORD);
            } else if (StringUtils.isNotBlank(params.getThirdIndexId())) {
                dataMap = insightService.getRankingMap(params, ESUtil.AGG_FOUR_INDEX_ID);
            } else if (StringUtils.isNotBlank(params.getSecondIndexId())) {
                dataMap = insightService.getRankingMap(params, ESUtil.AGG_THIRD_INDEX_ID);
            } else if (StringUtils.isNotBlank(params.getFirstIndexId())) {
                dataMap = insightService.getRankingMap(params, ESUtil.AGG_SECOND_INDEX_ID);
            } else {
                dataMap = insightService.getRankingMap(params, ESUtil.AGG_FIRST_INDEX_ID);
            }
        } catch (Exception e) {
            log.error("获取指标排名异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(dataMap);
    }


    /**
     * 指标排名
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "TOP问题")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/TopList")
    public Result<List<ResultParams>> getKeywordTopList(@RequestBody RequestParams params) {
        log.info("getKeywordTopList=" + JsonUtil.toJsonStr(params));
        List<ResultParams> topList = null;
        try {
            topList = insightService.getTopList(params);
        } catch (Exception e) {
            log.error("获取TOP问题异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(topList);
    }

    /**
     * 竞品对比-品牌
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "竞品对比-品牌")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/rivalCompare/brand")
    public Result<List<ResultParams>> getRivalCompareBrand(@RequestBody RequestParams params) {
        params.setIsOuter("是");
        log.info("getRivalCompareBrand=" + JsonUtil.toJsonStr(params));
        List<ResultParams> rivalList = null;
        try {
            rivalList = insightService.getVocExperience(params, ESUtil.AGG_BRAND_NAME);
        } catch (Exception e) {
            log.error("获取竞品对比异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(rivalList);
    }

    /**
     * 竞品对比-车系
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "竞品对比-车系")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/rivalCompare/series")
    public Result<List<ResultParams>> getRivalCompareSeries(@RequestBody RequestParams params) {
        params.setIsOuter("是");
        log.info("getRivalCompareSeries=" + JsonUtil.toJsonStr(params));
        List<ResultParams> rivalList = null;
        try {
            rivalList = insightService.getVocExperience(params, ESUtil.AGG_SERIES_NAME);
        } catch (Exception e) {
            log.error("获取竞品对比异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(rivalList);
    }

    @ApiOperation(value = "区域对比")
    @ApiImplicitParam(name = "params", value = "请求参数", required = true, dataType = "RequestParams")
    @PostMapping("/areaCompare")
    public Result<List<ResultParams>> getAreaCompare(@RequestBody RequestParams params) {
        log.info("getAreaCompare=" + JsonUtil.toJsonStr(params));
        List<ResultParams> dataList = null;
        try {
            dataList = insightService.getRegionVocExperience(params, ESUtil.AGG_PROVINCE);

            String startDate = params.getStartDate();
            params.setStartDate(DateUtil.getPerStartDate(startDate, params.getEndDate(), params.getDateType()));
            params.setEndDate(DateUtil.getPerEndDate(startDate));
            List<ResultParams> perDataList = insightService.getRegionVocExperience(params, ESUtil.AGG_PROVINCE);
            for (ResultParams info : dataList) {
                for (ResultParams perInfo : perDataList) {
                    if (info.getKeyCode().equals(perInfo.getKeyCode())) {
                        info.setMomExperienceValueRate(DataUtil.getCycleRate(perInfo.getExperienceValue(),
                                info.getExperienceValue()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取区域对比异常", e);
            return ResultUtil.getSysError();
        }
        return ResultUtil.getSuccess(dataList);
    }


}
