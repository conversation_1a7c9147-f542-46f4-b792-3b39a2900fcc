package com.meicloud.voc.car.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meicloud.voc.car.CarHelper;
import com.meicloud.voc.car.entity.CarSeries;
import com.meicloud.voc.car.entity.DwsKeywordExperience;
import com.meicloud.voc.car.entity.DwsKeywordMentionDay;
import com.meicloud.voc.car.entity.IndexSystem;
import com.meicloud.voc.car.enums.ArgTypeEnum;
import com.meicloud.voc.car.enums.DateEnum;
import com.meicloud.voc.car.enums.EsIndexType;
import com.meicloud.voc.car.utils.EsQueryBuilder;
import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.exception.ServiceException;
import com.meicloud.voc.common.utils.*;
import com.meicloud.voc.es.ESIndexClient;
import com.meicloud.voc.manage.indexSystemManage.service.IIndexSystemManageService;
import com.meicloud.voc.product.entity.IndexAnalysisTreeIndexSystemNode;
import com.meicloud.voc.product.entity.SelfBrandSourceAnalysisResult;
import com.meicloud.voc.selfAnalysis.dto.StKeywordDetailParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.LongBounds;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DwsKeywordMentionDayService {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    @Qualifier("keywordMentionDay")
    private ESIndexClient keywordMentionDay;

    @Resource
    @Qualifier("keywordMentionMonth")
    private ESIndexClient keywordMentionMonth;

    @Resource
    @Qualifier("keywordMentionYear")
    private ESIndexClient keywordMentionYear;

    @Autowired
    private IIndexSystemManageService indexSystemManageService;

    @Autowired
    private CarHelper carHelper;
    
    private static final String CHANG_AN_GROUP = "长安汽车集团";
    private static final String EMOTION_ATTRIBUTE_AGG = "emotionAttributeAgg";
    private static final String MENTION_VALUE_SUM = "mentionValueSum";
    private static final String SUM_MENTION_VALUE_SUM = "sum#mentionValueSum";
    private static final String STERMS_EMOTIONATTRIBUTE_AGG = "sterms#emotionAttributeAgg";
    private static final String DATA_SOURCE_AGG = "dataSourceAgg";
    private static final String STERMS_DATASOURCE_AGG = "sterms#dataSourceAgg";
    private static final String INDEX_AGG = "indexAgg";
    private static final String STERMS_INDEX_AGG = "sterms#indexAgg";
    private static final String DATA_DATE_AGG = "dataDateAgg";
    private static final String EXPERIENCE_VALUE = "experienceValue";

    private SearchResponse doEsSearch(SearchSourceBuilder sourceBuilder, String dateType) throws IOException {
        SearchResponse search = null;
        if (DateEnum.DAY.getType().equals(dateType)) {
            search = keywordMentionDay.search(sourceBuilder);
        } else if (DateEnum.MONTH.getType().equals(dateType)) {
            search = keywordMentionMonth.search(sourceBuilder);
        } else if (DateEnum.SEASON.getType().equals(dateType)) {
            search = keywordMentionMonth.search(sourceBuilder);
        } else if (DateEnum.YEAR.getType().equals(dateType)) {
            search = keywordMentionYear.search(sourceBuilder);
        } else {
            search = keywordMentionDay.search(sourceBuilder);
        }
        return search;
    }

    private BoolQueryBuilder getBoolQueryBuilder(RequestParams requestParams) {
        return EsQueryBuilder.init(EsIndexType.KEYWORD_MENTION).setParams(requestParams).build();
    }

    /**
     * 计算top关键词
     *
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> keywordTopList(RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);

        // Todo: 后续采用standardKeywordId查询
        int aggTopN = requestParams.getTopN() > 0 ? requestParams.getTopN() : 100000;
		AggregationBuilder keywordAgg = AggregationBuilders.terms("standardKeywordAgg").field("standardKeyword").size(aggTopN)
            .shardSize(21000).order(BucketOrder.aggregation(ESUtil.MENTION_VALUE_SUM, false))
        .subAggregation(
                AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(1000000).subAggregation(
                        AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                )
        ).subAggregation(
                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
        );
        AggregationBuilder sumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE);
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(keywordAgg).aggregation(sumAgg).size(0);
        SearchResponse search = null;

        // 重置关键词和情感属性的字段
        RequestParams sumRequestParams = BeanCopyUtils.copyProperties(requestParams, RequestParams.class);
        sumRequestParams.setEmotionAttribute(null);
        sumRequestParams.setStandardKeyword(null);
        BoolQueryBuilder sumBoolQueryBuilder = this.getBoolQueryBuilder(sumRequestParams);
        SearchSourceBuilder sumSourceBuilder = SearchSourceBuilder.searchSource().query(sumBoolQueryBuilder).aggregation(sumAgg).size(0);

        SearchResponse sumSearch = null;

        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            sumSearch = doEsSearch(sumSourceBuilder, requestParams.getDateType());

            JSONObject sumQueryResult = JSONObject.parseObject(sumSearch.toString());
			long totalMentionValueSum = sumQueryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);

            //分组计算正负面的提及率
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            List<DwsKeywordMentionDay> resultList = new ArrayList<>();

            JSONArray standardKeywordBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("sterms#standardKeywordAgg").getJSONArray(ESUtil.BUCKETS);
            for (int n = 0; n < standardKeywordBuckets.size(); n++) {
                JSONObject standardKeywordBucket = standardKeywordBuckets.getJSONObject(n);
                String standardKeyword = standardKeywordBucket.getString("key");
                double positiveMentionRate = 0; //正面提及率
                double negativeMentionRate = 0; //负面提及率
                double neutralMentionRate = 0;
				JSONArray emotionAttributeBuckets = standardKeywordBucket.getJSONObject(STERMS_EMOTIONATTRIBUTE_AGG).getJSONArray(ESUtil.BUCKETS);
                for (int k = 0; k < emotionAttributeBuckets.size(); k++) {
                    DwsKeywordMentionDay result = new DwsKeywordMentionDay();
                    result.setStandardKeyword(standardKeyword);
                    result.setKeyword(standardKeyword);
                    JSONObject emotionAttributeBucket = emotionAttributeBuckets.getJSONObject(k);
                    String emotionAttribute = (emotionAttributeBucket).getString("key");
                    long emotionAttributeMentionValueSum = (emotionAttributeBucket).getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                    if ("正面".equals(emotionAttribute) && totalMentionValueSum > 0) {
                        positiveMentionRate = DataUtil.getMentionRate(totalMentionValueSum, emotionAttributeMentionValueSum);
                        result.setPositiveMentionValue(emotionAttributeMentionValueSum);
                        result.setMentionRate(positiveMentionRate);
                    } else if ("负面".equals(emotionAttribute) && totalMentionValueSum > 0) {
                        negativeMentionRate = DataUtil.getMentionRate(totalMentionValueSum, emotionAttributeMentionValueSum);
                        result.setNegativeMentionValue(emotionAttributeMentionValueSum);
                        result.setMentionRate(negativeMentionRate);
                    } else if ("中性".equals(emotionAttribute) && totalMentionValueSum > 0) {
                        neutralMentionRate = DataUtil.getMentionRate(totalMentionValueSum, emotionAttributeMentionValueSum);
                        result.setNeutralMentionValue(emotionAttributeMentionValueSum);
                        result.setMentionRate(neutralMentionRate);
                    }
                    result.setEmotionAttribute(emotionAttribute);
                    result.setStandardKeywordMentionValue(emotionAttributeMentionValueSum);
                    result.setTotalMentionValue(emotionAttributeMentionValueSum); //这里的意思是因为另外一个后端开发给到前端的字段不一样，所以暂时先把正确的值set到这个字段
                    result.setMentionRate(DataUtil.getMentionRate(totalMentionValueSum, emotionAttributeMentionValueSum));
                    resultList.add(result);
                }
            }

            return resultList;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            ;
        }
        return new ArrayList<>();
    }

    /**
     * 计算体验值趋势
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> customerTrendExperience(RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);

		AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);
        AggregationBuilder dataBucketAgg = getDateAggregationBuilder(requestParams.getDateType(), requestParams.getStartDate(), requestParams.getEndDate());

        AggregationBuilder agg = dataBucketAgg.subAggregation(
                dataSourceAgg.subAggregation(
                        AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).subAggregation(
                                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                        ))
        ).subAggregation(
                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
        );
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            //search = keywordMentionDay.search(sourceBuilder);
            //分组计算正负面的提及率，然后计算 体验值
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            List<DwsKeywordMentionDay> resultList = new ArrayList<>();
            JSONArray dataDateBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("date_histogram#dataDateAgg").getJSONArray(ESUtil.BUCKETS);
            for (int i = 0; i < dataDateBuckets.size(); i++) {
                DwsKeywordMentionDay result = new DwsKeywordMentionDay();
                JSONObject dataDateBucket = dataDateBuckets.getJSONObject(i);
                String dataDate = dataDateBucket.getString("key_as_string");
                dataDate = DateUtil.formatDate(dataDate, requestParams.getDateType());
                long totalMentionValueSum = dataDateBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);

                JSONArray dataSourceBuckets = dataDateBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);
                result.setTotalMentionValue(totalMentionValueSum);

                DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);

                //计算体验值
                result.setDataDate(dataDate);
                result.setPositiveMentionValue(dwsKeywordExperience.getPosMention());
                result.setNegativeMentionValue(dwsKeywordExperience.getNegMention());
                result.setNeutralMentionValue(dwsKeywordExperience.getNeuMention());
                result.setExperienceValue(dwsKeywordExperience.getExperience());
                result.setPositiveMentionRate(DataUtil.getPosRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                result.setNegativeMentionRate(DataUtil.getNegRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                resultList.add(result);
            }

            return orderDwsKeywordMentionListByDataDate(resultList, requestParams.getDateType());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            ;
        }
        return new ArrayList<>();
    }


    /**
     * 领域分布
     *
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> territoryDistribute(RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);
        AggregationBuilder agg = AggregationBuilders.terms("firstIndexNameAgg").field("firstIndexName").size(10000).subAggregation(
                AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(10000).subAggregation(
                        AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                )
        );
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            List<DwsKeywordMentionDay> resultList = new ArrayList<>();
            JSONArray firstIndexNameBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("sterms#firstIndexNameAgg").getJSONArray(ESUtil.BUCKETS);
            for (int i = 0; i < firstIndexNameBuckets.size(); i++) {
                DwsKeywordMentionDay result = new DwsKeywordMentionDay();
                JSONObject firstIndexNameBucket = firstIndexNameBuckets.getJSONObject(i);
                String firstIndexName = firstIndexNameBucket.getString("key");
                JSONArray emotionAttributeBuckets = firstIndexNameBucket.getJSONObject(STERMS_EMOTIONATTRIBUTE_AGG).getJSONArray(ESUtil.BUCKETS);
                for (int n = 0; n < emotionAttributeBuckets.size(); n++) {
                    JSONObject emotionAttributeBucket = emotionAttributeBuckets.getJSONObject(n);
                    String emotionAttribute = emotionAttributeBucket.getString("key");
                    long mentionValueSum = emotionAttributeBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                    if ("正面".equals(emotionAttribute)) {
                        result.setPositiveMentionValue(mentionValueSum);
                    } else if ("负面".equals(emotionAttribute)) {
                        result.setNegativeMentionValue(mentionValueSum);
                    } else if ("中性".equals(emotionAttribute)) {
                        result.setNeutralMentionValue(mentionValueSum);
                    }
                }
                result.setFirstIndexName(firstIndexName);
                resultList.add(result);
            }
            return resultList;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            ;
        }
        return new ArrayList<>();
    }

    /**
     * 计算场景的体验值
     *
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> customerExperience4Scene(RequestParams requestParams) {
        List<DwsKeywordMentionDay> dwsKeywordMentionDays = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);
        String aggIndex = indexSystemManageService.getAggIndexByParams(requestParams);
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(aggIndex, ""));

        AggregationBuilder mentionValueSumAgg = null;
        if (requestParams.getCarOwner() != null && requestParams.getCarOwner() == 1) {
            //车主
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field("carOwnerMentionValue");
        } else if (requestParams.getCarOwner() != null && requestParams.getCarOwner() == 2) {
            //非车主
            Script script = new Script("doc.mentionValue.value - doc.carOwnerMentionValue.value");
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).script(script);
        } else {
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE);
        }

        AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);
        TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders.topHits("topHitsAgg")
                .fetchSource(new String[]{ESUtil.AGG_FIRST_INDEX_ID, ESUtil.AGG_SECOND_INDEX_ID, ESUtil.AGG_THIRD_INDEX_ID, ESUtil.AGG_FOUR_INDEX_NAME}, null).size(1);
		AggregationBuilder agg = AggregationBuilders.terms(INDEX_AGG).field(aggIndex).size(10000).subAggregation(
                dataSourceAgg.subAggregation(
                        AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(10000).subAggregation(
                                mentionValueSumAgg
                        )
                )
        ).subAggregation(mentionValueSumAgg).subAggregation(topHitsAggregationBuilder);

        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        String indexType = requestParams.getIndexType();
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
			JSONArray indexBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(STERMS_INDEX_AGG).getJSONArray(ESUtil.BUCKETS);
            //Map<String, DwsKeywordMentionDay> indexMap = new HashMap<>();

            for (int i = 0; i < indexBuckets.size(); i++) {
                DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();
                JSONObject indexBucket = indexBuckets.getJSONObject(i);
                String indexId = indexBucket.getString("key");
                String indexName = DataUtil.getFourIndexName(indexType, indexId);
                long totalMentionValue = indexBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                JSONArray hitsArr = indexBucket.getJSONObject("top_hits#topHitsAgg").getJSONObject("hits").getJSONArray("hits");
                if (hitsArr.size() > 0) {
                    JSONObject indexObject = hitsArr.getJSONObject(0).getJSONObject("_source");
                    dwsKeywordMentionDay.setFirstIndexId(indexObject.getString(ESUtil.AGG_FIRST_INDEX_ID));
                    dwsKeywordMentionDay.setSecondIndexId(indexObject.getString(ESUtil.AGG_SECOND_INDEX_ID));
                    dwsKeywordMentionDay.setThirdIndexId(indexObject.getString(ESUtil.AGG_THIRD_INDEX_ID));
                    indexName = indexObject.getString(ESUtil.AGG_FOUR_INDEX_NAME);
                }
				JSONArray dataSourceBuckets = indexBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);

                DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);
                dwsKeywordMentionDay.setIndexId(indexId);
                dwsKeywordMentionDay.setIndexName(indexName);
                dwsKeywordMentionDay.setTotalMentionValue(totalMentionValue);
                dwsKeywordMentionDay.setPositiveMentionValue(dwsKeywordExperience.getPosMention());
                dwsKeywordMentionDay.setNegativeMentionValue(dwsKeywordExperience.getNegMention());
                dwsKeywordMentionDay.setNeutralMentionValue(dwsKeywordExperience.getNeuMention());
                dwsKeywordMentionDay.setExperienceValue(dwsKeywordExperience.getExperience());
                dwsKeywordMentionDay.setPositiveMentionRate(DataUtil.getPosRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDay.setNegativeMentionRate(DataUtil.getNegRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDays.add(dwsKeywordMentionDay);
                //indexMap.put(indexId, dwsKeywordMentionDay);
            }

//            List<IndexSystem> indexSystems = carHelper.getSearchIndex(requestParams.getFirstIndexId(),
//                    requestParams.getSecondIndexId(),
//                    requestParams.getThirdIndexId(),
//                    requestParams.getFourIndexId(),
//                    requestParams.getIndexTypeName());
//            for (IndexSystem indexSystem : indexSystems) {
//                if (indexMap.containsKey(indexSystem.getFourIndexId())) {
//                    dwsKeywordMentionDays.add(indexMap.get(indexSystem.getFourIndexId()));
//                } else {
//                    DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay(indexSystem.getFourIndexId(), indexSystem.getFourIndexName());
//                    dwsKeywordMentionDay.setFirstIndexId(indexSystem.getFirstIndexId());
//                    dwsKeywordMentionDay.setSecondIndexId(indexSystem.getSecondIndexId());
//                    dwsKeywordMentionDay.setThirdIndexId(indexSystem.getThirdIndexId());
//                    dwsKeywordMentionDays.add(dwsKeywordMentionDay);
//                }
//            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return dwsKeywordMentionDays;
    }

    /**
     * 计算车系的体验值
     *
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> customerExperience4Series(RequestParams requestParams) {

        List<DwsKeywordMentionDay> dwsKeywordMentionDays = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(ESUtil.AGG_SERIES_NAME, ""));

        AggregationBuilder mentionValueSumAgg = null;
        if (requestParams.getCarOwner() != null && requestParams.getCarOwner() == 1) {
            //车主
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field("carOwnerMentionValue");
        } else if (requestParams.getCarOwner() != null && requestParams.getCarOwner() == 2) {
            //非车主
            Script script = new Script("doc.mentionValue.value - doc.carOwnerMentionValue.value");
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).script(script);
        } else {
            mentionValueSumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE);
        }

        AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);

        AggregationBuilder agg = AggregationBuilders.terms("seriesAgg").field("seriesName").size(10000).subAggregation(
                dataSourceAgg.subAggregation(
                        AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(10000).subAggregation(
                                mentionValueSumAgg
                        )
                )
        ).subAggregation(mentionValueSumAgg);

        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = keywordMentionDay.search(sourceBuilder);
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            JSONArray seriesBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("sterms#seriesAgg").getJSONArray(ESUtil.BUCKETS);
            //Map<String, DwsKeywordMentionDay> carSeriesMap = new HashMap<>();
            for (int i = 0; i < seriesBuckets.size(); i++) {
                DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();
                JSONObject seriesBucket = seriesBuckets.getJSONObject(i);
                String seriesName = seriesBucket.getString("key");
                long totalMentionValue = seriesBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);

                JSONArray dataSourceBuckets = seriesBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);
                dwsKeywordMentionDay.setTotalMentionValue(totalMentionValue);

                DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);
                dwsKeywordMentionDay.setTotalMentionValue(totalMentionValue);
                dwsKeywordMentionDay.setSeriesName(seriesName);
                dwsKeywordMentionDay.setPositiveMentionValue(dwsKeywordExperience.getPosMention());
                dwsKeywordMentionDay.setNegativeMentionValue(dwsKeywordExperience.getNegMention());
                dwsKeywordMentionDay.setNeutralMentionValue(dwsKeywordExperience.getNeuMention());
                dwsKeywordMentionDay.setExperienceValue(dwsKeywordExperience.getExperience());
                dwsKeywordMentionDay.setPositiveMentionRate(DataUtil.getPosRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDay.setNegativeMentionRate(DataUtil.getNegRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDays.add(dwsKeywordMentionDay);
                //carSeriesMap.put(seriesName, dwsKeywordMentionDay);
            }
//            List<String> brandNames = requestParams.getBrandNames();
//            if (CollectionUtils.isEmpty(requestParams.getBrandNames())) {
//                brandNames = carHelper.getChanganBrandNames();
//            }
//            List<CarSeries> carSeriesList = carHelper.getCarSeries(brandNames, requestParams.getMarketNames());
//            for (CarSeries carSeries : carSeriesList) {
//                if (carSeriesMap.containsKey(carSeries.getSeriesName())) {
//                    dwsKeywordMentionDays.add(carSeriesMap.get(carSeries.getSeriesName()));
//                } else {
//                    DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();
//                    dwsKeywordMentionDay.setSeriesName(carSeries.getSeriesName());
//                    dwsKeywordMentionDay.setTotalMentionValue(0);
//                    dwsKeywordMentionDay.setPositiveMentionValue(0);
//                    dwsKeywordMentionDay.setNegativeMentionValue(0);
//                    dwsKeywordMentionDay.setNeutralMentionValue(0);
//                    dwsKeywordMentionDay.setExperienceValue(0);
//                    dwsKeywordMentionDay.setPositiveMentionRate(0.0);
//                    dwsKeywordMentionDay.setNegativeMentionRate(0.0);
//                    dwsKeywordMentionDays.add(dwsKeywordMentionDay);
//                }
//            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            ;
        }
        return dwsKeywordMentionDays;
    }

    private String getIndexAggField(RequestParams requestParams) {
        Integer indexLevel = indexSystemManageService.getIndexLevel(requestParams);
        String field = ESUtil.AGG_FIRST_INDEX_ID;

        if (StringUtils.isNotEmpty(requestParams.getFirstIndexId()) && indexLevel > 1) {
            field = ESUtil.AGG_SECOND_INDEX_ID;
        }
        if (StringUtils.isNotEmpty(requestParams.getSecondIndexId()) && indexLevel > 2) {
            field = ESUtil.AGG_THIRD_INDEX_ID;
        }
        if (StringUtils.isNotEmpty(requestParams.getThirdIndexId()) && indexLevel > 3) {
            field = ESUtil.AGG_FOUR_INDEX_ID;
        }

        return field;
    }

    //voc体验值
    public List<DwsKeywordMentionDay> vocExperience(RequestParams requestParams) {
    	String indexType = requestParams.getIndexType();
        List<DwsKeywordMentionDay> dwsKeywordMentionDays = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);

        String indexAggField = getIndexAggField(requestParams);
        AggregationBuilder indexAgg = AggregationBuilders.terms(INDEX_AGG).field(indexAggField).size(1000);

        AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);

        AggregationBuilder agg = indexAgg.subAggregation(
                dataSourceAgg.subAggregation(
                        AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(100000).subAggregation(
                                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                        )
                )

        ).subAggregation(
                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
        );
        List<IndexAnalysisTreeIndexSystemNode> indexSystems = carHelper.getNextIndex(requestParams);

        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            JSONArray indexBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(STERMS_INDEX_AGG).getJSONArray(ESUtil.BUCKETS);
            Map<String, DwsKeywordMentionDay> dwsKeywordMentionDayMap = new HashMap<>();

            for (int i = 0; i < indexBuckets.size(); i++) {

                DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();

                JSONObject indexBucket = indexBuckets.getJSONObject(i);
                String indexId = indexBucket.getString("key");
                long totalMentionValue = indexBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                JSONArray dataSourceBuckets = indexBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);

                DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);
                dwsKeywordMentionDay.setIndexId(indexId);
                String indexName = DataUtil.getCodeName(indexType, indexAggField, indexId);
                dwsKeywordMentionDay.setIndexName(indexName);
                dwsKeywordMentionDay.setTotalMentionValue(totalMentionValue);
                dwsKeywordMentionDay.setPositiveMentionValue(dwsKeywordExperience.getPosMention());
                dwsKeywordMentionDay.setNegativeMentionValue(dwsKeywordExperience.getNegMention());
                dwsKeywordMentionDay.setNeutralMentionValue(dwsKeywordExperience.getNeuMention());
                dwsKeywordMentionDay.setExperienceValue(dwsKeywordExperience.getExperience());
                dwsKeywordMentionDay.setPositiveMentionRate(DataUtil.getPosRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDay.setNegativeMentionRate(DataUtil.getNegRate(dwsKeywordExperience.getPosMention(),
                        dwsKeywordExperience.getNeuMention(),
                        dwsKeywordExperience.getNegMention()));
                dwsKeywordMentionDayMap.put(indexId, dwsKeywordMentionDay);
                // dwsKeywordMentionDays.add(dwsKeywordMentionDay);
            }

            for (IndexAnalysisTreeIndexSystemNode indexSystem : indexSystems) {
                if (dwsKeywordMentionDayMap.containsKey(indexSystem.getIndexId())) {
                    dwsKeywordMentionDays.add(dwsKeywordMentionDayMap.get(indexSystem.getIndexId()));
                } else {
                    DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay(indexSystem.getIndexId(), indexSystem.getIndexName());
                    dwsKeywordMentionDays.add(dwsKeywordMentionDay);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            ;
        }
        dwsKeywordMentionDays.sort(new IndexOrderComparator<>());
        return dwsKeywordMentionDays;
    }


    /**
     * 计算提及量
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> mentionValueTrend(RequestParams requestParams) {
        return mentionValueTrend(requestParams, null);
    }


    /**
     * 处理提及率趋势
     * @param dataDateBucket
     * @param dateType
     * @param argType
     * @return
     */
    private DwsKeywordMentionDay detailMentionValueTrend(JSONObject dataDateBucket, String dateType, String argType) {
        DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();
        String dataDate = dataDateBucket.getString("key_as_string");
        dataDate = DateUtil.formatDate(dataDate, dateType);
        long totalMentionValue = dataDateBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
        int argSize = 1;

        if(StringUtils.isNotBlank(argType)) {
            argSize = dataDateBucket.getJSONObject("sterms#argAgg").getJSONArray(ESUtil.BUCKETS).size();
            if(argSize == 0) argSize = 1;
        }

        JSONArray emotionAttributeBuckets = dataDateBucket.getJSONObject(STERMS_EMOTIONATTRIBUTE_AGG).getJSONArray(ESUtil.BUCKETS);
        for (int n = 0; n < emotionAttributeBuckets.size(); n++) {
            JSONObject emotionAttributeBucket = emotionAttributeBuckets.getJSONObject(n);
            String emotionAttribute = emotionAttributeBucket.getString("key");
            long mentionValueSum = emotionAttributeBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
            if ("正面".equals(emotionAttribute)) {
                dwsKeywordMentionDay.setPositiveMentionValue(mentionValueSum / argSize);
            } else if ("负面".equals(emotionAttribute)) {
                dwsKeywordMentionDay.setNegativeMentionValue(mentionValueSum / argSize);
            } else if ("中性".equals(emotionAttribute)) {
                dwsKeywordMentionDay.setNeutralMentionValue(mentionValueSum /argSize);
            }
        }

        dwsKeywordMentionDay.setTotalMentionValue(totalMentionValue /argSize);
        dwsKeywordMentionDay.setDataDate(dataDate);
        return dwsKeywordMentionDay;
    }
    /**
     * 计算提及量趋势
     *
     * @param requestParams
     * @param argType 计算平均的类型， 如果为空不计算
     * @return
     */
    public List<DwsKeywordMentionDay> mentionValueTrend(RequestParams requestParams, String argType) {
        List<DwsKeywordMentionDay> dwsKeywordMentionDays = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);

        AggregationBuilder dataBucketAgg = getDateAggregationBuilder(requestParams.getDateType(), requestParams.getStartDate(), requestParams.getEndDate());
        if(StringUtils.isNotBlank(argType)) {
            if(ArgTypeEnum.BRAND.getType().equals(argType)) {
                dataBucketAgg.subAggregation(AggregationBuilders.terms("argAgg").field(ESUtil.AGG_BRAND_NAME).size(100));
            } else if(ArgTypeEnum.CAR_SERIES.getType().equals(argType)) {
                dataBucketAgg.subAggregation(AggregationBuilders.terms("argAgg").field(ESUtil.AGG_SERIES_NAME).size(1000));
            }
        }

        AggregationBuilder agg = dataBucketAgg.subAggregation(
                AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).size(10000).subAggregation(
                        AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                )
        ).subAggregation(
                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
        );

        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            JSONArray dataDateBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("date_histogram#dataDateAgg").getJSONArray(ESUtil.BUCKETS);
            for (int i = 0; i < dataDateBuckets.size(); i++) {
                JSONObject dataDateBucket = dataDateBuckets.getJSONObject(i);
                DwsKeywordMentionDay dwsKeywordMentionDay = detailMentionValueTrend(dataDateBucket, requestParams.getDateType(), argType);
                dwsKeywordMentionDays.add(dwsKeywordMentionDay);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return orderDwsKeywordMentionListByDataDate(dwsKeywordMentionDays, requestParams.getDateType());
    }

    /**
     * 归因分析品牌对比-计算top关键词
     * @param requestParams
     * @return
     */
    public List<DwsKeywordMentionDay> selfBrandCompareKeywordTopList(RequestParams requestParams) {
        List<DwsKeywordMentionDay> resultList = this.keywordTopList(requestParams);
        ListUtil.sortDwsList(resultList, requestParams.getSortName());
        return resultList;
    }


    /**
     * 计算关键词提及量环比
     *
     * @param requestParams
     * @return
     */
    public DwsKeywordMentionDay keywordMentionTotalWithCycle(StKeywordDetailParams requestParams) {
        DwsKeywordMentionDay dwsKeywordMentionDay = keywordMentionTotal(requestParams);
        //计算环比
        String momStartTime = null;
        String momEndTime = null;
        try {
            momStartTime = com.meicloud.voc.common.utils.DateUtil.getPerStartDate(requestParams.getStartDate(), requestParams.getEndDate(), DateEnum.MONTH.getType());
            momEndTime = com.meicloud.voc.common.utils.DateUtil.getPerEndDate(requestParams.getStartDate());
        } catch (ParseException e) {
            logger.error("计算环比时间出错", e);
            throw new ServiceException("计算环比时间出错");
        }
        requestParams.setStartDate(momStartTime);
        requestParams.setEndDate(momEndTime);
        DwsKeywordMentionDay dwsKeywordExperienceMon = keywordMentionTotal(requestParams);
        dwsKeywordMentionDay.setMomStandardKeywordMentionRate(DataUtil.getCycleRate(dwsKeywordExperienceMon.getStandardKeywordMentionValue(),
                dwsKeywordMentionDay.getStandardKeywordMentionValue()));

        return dwsKeywordMentionDay;
    }

    /**
     * 计算关键词提及量
     *
     * @param requestParams
     * @return
     */
    public DwsKeywordMentionDay keywordMentionTotal(StKeywordDetailParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (StringUtils.isNotEmpty(requestParams.getStartDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.AGG_DATA_DATE).gte(requestParams.getStartDate()).timeZone(DateUtil.ASIA_SHANGHAI));
        }
        if (StringUtils.isNotEmpty(requestParams.getEndDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(ESUtil.AGG_DATA_DATE).lte(requestParams.getEndDate()).timeZone(DateUtil.ASIA_SHANGHAI));
        }
        if (!CollectionUtils.isEmpty(requestParams.getIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("standardKeywordId", requestParams.getIds()));
        }

        AggregationBuilder agg = AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE);
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        DwsKeywordMentionDay dwsKeywordMentionDay = new DwsKeywordMentionDay();
        try {
            search = doEsSearch(sourceBuilder, DateEnum.MONTH.getType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            long totalMentionValueSum = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
            dwsKeywordMentionDay.setStandardKeywordMentionValue(totalMentionValueSum);
            return dwsKeywordMentionDay;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            ;
            throw new ServiceException("计算所选关键词提及量失败");
        }
    }

    //来源分析
    public SelfBrandSourceAnalysisResult sourceAnalysis(RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = this.getBoolQueryBuilder(requestParams);
        AggregationBuilder agg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100000);
        AggregationBuilder sumAgg = AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE);
        agg.subAggregation(sumAgg);
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).aggregation(sumAgg).size(0);
        SearchResponse search = null;
        List<Map<String, Object>> resultList = new ArrayList<>();
        SelfBrandSourceAnalysisResult selfBrandSourceAnalysisResult = new SelfBrandSourceAnalysisResult();

        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            JSONArray dataSourceBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);
            long totalMatchData = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
            selfBrandSourceAnalysisResult.setTotal(totalMatchData);
            for (int i = 0; i < dataSourceBuckets.size(); i++) {
                JSONObject dataSourceBucket = dataSourceBuckets.getJSONObject(i);
                long dataSourceMentionValue = dataSourceBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                String dataSourceName = dataSourceBucket.getString("key");
                Map<String, Object> item = new HashMap<>();

                item.put(ESUtil.AGG_DATA_SOURCE, dataSourceName);
                item.put("dataCount", dataSourceMentionValue);
                if (totalMatchData > 0) {
                    item.put("dataPer", (double) dataSourceMentionValue / totalMatchData);
                } else {
                    item.put("dataPer", 0);
                }
                resultList.add(item);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            ;
        }

        selfBrandSourceAnalysisResult.setSourceAnalysisList(resultList);
        return selfBrandSourceAnalysisResult;
    }

    /**
     * 根据日期进行排序
     *
     * @param dwsKeywordMentionDayList
     * @param dateType
     * @return
     */
    private List<DwsKeywordMentionDay> orderDwsKeywordMentionListByDataDate(List<DwsKeywordMentionDay> dwsKeywordMentionDayList, String dateType) {
        return dwsKeywordMentionDayList.stream().sorted((Comparator<DwsKeywordMentionDay>) (o1, o2) -> {
            try {
                Date d1 = DateUtil.parseDate(o1.getDataDate(), dateType);
                Date d2 = DateUtil.parseDate(o2.getDataDate(), dateType);
                return d1.compareTo(d2);
            } catch (ParseException e) {
                log.error(e.getMessage(), e);
                ;
                return 0;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 根据日期类型，返回es聚合aggregation
     */
    public AggregationBuilder getDateAggregationBuilder(String dateType, String startDate, String endDate) {
        AggregationBuilder agg = null;
        // es版本升级修改ExtendedBounds -> LongBounds
		if (DateEnum.DAY.getType().equals(dateType)) {
            agg = AggregationBuilders.dateHistogram(DATA_DATE_AGG).field(ESUtil.AGG_DATA_DATE).
                    timeZone(ZoneId.of(DateUtil.ASIA_SHANGHAI)).format(DateUtil.YYYY_MM_DD)
                    .calendarInterval(DateHistogramInterval.DAY)
                    .minDocCount(0)
                    .extendedBounds(new LongBounds(startDate, endDate));
        } else if (DateEnum.MONTH.getType().equals(dateType) || DateEnum.SEASON.getType().equals(dateType)) {
            agg = AggregationBuilders.dateHistogram(DATA_DATE_AGG).field(ESUtil.AGG_DATA_DATE).
                    timeZone(ZoneId.of(DateUtil.ASIA_SHANGHAI)).format(DateUtil.YYYY_MM_DD)
                    .calendarInterval(DateHistogramInterval.MONTH)
                    .minDocCount(0).extendedBounds(new LongBounds(startDate, endDate));
        } else if (DateEnum.YEAR.getType().equals(dateType)) {
            agg = AggregationBuilders.dateHistogram(DATA_DATE_AGG).field(ESUtil.AGG_DATA_DATE).
                    timeZone(ZoneId.of(DateUtil.ASIA_SHANGHAI)).format(DateUtil.YYYY_MM_DD)
                    .calendarInterval(DateHistogramInterval.YEAR)
                    .minDocCount(0).extendedBounds(new LongBounds(startDate, endDate));
        } else {
            // 如果日期类型不是day，month就默认month
            agg = AggregationBuilders.dateHistogram(DATA_DATE_AGG).field(ESUtil.AGG_DATA_DATE).
                    timeZone(ZoneId.of(DateUtil.ASIA_SHANGHAI)).format(DateUtil.YYYY_MM_DD)
                    .calendarInterval(DateHistogramInterval.DAY)
                    .minDocCount(0).extendedBounds(new LongBounds(startDate, endDate));
        }
        return agg;
    }

    /**
     * 根据dataSource计算体验值
     */
    public DwsKeywordExperience calExperience(JSONArray dataSourceBuckets) {
        long posMention = 0;
        long neuMention = 0;
        long negMention = 0;
        double posMentionWeight = 0;
        double neuMentionWeight = 0;
        double negMentionWeight = 0;
        double experience = 0;

        for (int j = 0; j < dataSourceBuckets.size(); j++) {
            JSONObject dataSourceBucket = dataSourceBuckets.getJSONObject(j);
            String dataSource = dataSourceBucket.getString("key");

            double weight = DataUtil.getDataSouceWeight(dataSource);
            JSONArray emotionAttributeBuckets = dataSourceBucket.getJSONObject(STERMS_EMOTIONATTRIBUTE_AGG).getJSONArray(ESUtil.BUCKETS);
            for (int n = 0; n < emotionAttributeBuckets.size(); n++) {
                JSONObject emotionAttributeBucket = emotionAttributeBuckets.getJSONObject(n);
                String emotionAttribute = emotionAttributeBucket.getString("key");
                long mentionValueSum = emotionAttributeBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                if ("正面".equals(emotionAttribute)) {
                    posMention += mentionValueSum;
                    posMentionWeight += DataUtil.getWeightMention(weight, mentionValueSum);
                } else if ("负面".equals(emotionAttribute)) {
                    negMention += mentionValueSum;
                    negMentionWeight += DataUtil.getWeightMention(weight, mentionValueSum);
                } else if ("中性".equals(emotionAttribute)) {
                    neuMention += mentionValueSum;
                    neuMentionWeight += DataUtil.getWeightMention(weight, mentionValueSum);
                }
            }
        }

        experience = DataUtil.getExperience(posMentionWeight, neuMentionWeight, negMentionWeight);
        return new DwsKeywordExperience(posMention, neuMention, negMention, experience);
    }

    /**
     * 计算平均提及量
     * @param brandBuckets
     * @return
     */
    public DwsKeywordExperience calArgMentionValue(JSONArray brandBuckets) {
        long posMention = 0;
        long neuMention = 0;
        long negMention = 0;
        int bucketsSize = brandBuckets.size();
        for (int j = 0; j < bucketsSize; j++) {
            JSONObject brandBucket = brandBuckets.getJSONObject(j);
            JSONArray emotionAttributeBuckets = brandBucket.getJSONObject(STERMS_EMOTIONATTRIBUTE_AGG).getJSONArray(ESUtil.BUCKETS);
            for (int n = 0; n < emotionAttributeBuckets.size(); n++) {
                JSONObject emotionAttributeBucket = emotionAttributeBuckets.getJSONObject(n);
                String emotionAttribute = emotionAttributeBucket.getString("key");
                long mentionValueSum = emotionAttributeBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);
                if ("正面".equals(emotionAttribute)) {
                    posMention += mentionValueSum;
                } else if ("负面".equals(emotionAttribute)) {
                    negMention += mentionValueSum;
                } else if ("中性".equals(emotionAttribute)) {
                    neuMention += mentionValueSum;
                }
            }
        }

        long argPosMention = bucketsSize > 0 ? posMention / bucketsSize : 0;
        long argNeuMention = bucketsSize > 0 ? neuMention / bucketsSize : 0;
        long argNegMention = bucketsSize > 0 ? negMention / bucketsSize : 0;

        return new DwsKeywordExperience(argPosMention, argNeuMention, argNegMention, 0);
    }

    /**
     * 获取Agg聚合
     * @param requestParams
     * @return
     */
    private Script getAggScript(RequestParams requestParams) {
        Integer indexLevel = indexSystemManageService.getIndexLevel(requestParams);
        Script indexAggScript = new Script("doc.firstIndexId+'##'+doc.firstIndexName");
        if (StringUtils.isNotEmpty(requestParams.getFirstIndexId()) && indexLevel > 1) {
            indexAggScript = new Script("doc.secondIndexId+'##'+doc.secondIndexName");

        }
        if (StringUtils.isNotEmpty(requestParams.getSecondIndexId()) && indexLevel > 2) {
            indexAggScript = new Script("doc.thirdIndexId+'##'+doc.thirdIndexName");

        }
        if (StringUtils.isNotEmpty(requestParams.getThirdIndexId()) && indexLevel > 3) {
            indexAggScript = new Script("doc.fourIndexId+'##'+doc.fourIndexName");
        }
        return indexAggScript;
    }

    /**
     * 根据参数计算指标的提及量
     *
     * @param requestParams
     * @return
     */
    public List<Map<String, Object>> customerVocExperienceFirstIndex(RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = getBoolQueryBuilder(requestParams);
        Pattern indexInfoPattern = Pattern.compile("\\[(.*)]##\\[(.*)]");
        Script indexAggScript = getAggScript(requestParams);
        AggregationBuilder indexAgg = AggregationBuilders.terms(INDEX_AGG).script(indexAggScript).size(10000);

        AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);

        AggregationBuilder agg = AggregationBuilders.terms("brandNameAgg").field(ESUtil.AGG_BRAND_NAME).size(10000).subAggregation(
                indexAgg
                        .subAggregation(
                                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE))
                        .subAggregation(
                                dataSourceAgg.subAggregation(
                                        AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).subAggregation(
                                                AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                                        )
                                )
                        )
        );
        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        List<IndexAnalysisTreeIndexSystemNode> indexSystems = carHelper.getNextIndex(requestParams);
        List<Map<String, Object>> resultList = new ArrayList<>();

        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());

            //分组计算正负面的提及率，然后计算 体验值
            JSONObject queryResult = JSONObject.parseObject(search.toString());

            JSONArray brandNameBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject("sterms#brandNameAgg").getJSONArray(ESUtil.BUCKETS);
            for (int i = 0; i < brandNameBuckets.size(); i++) {
                JSONObject brandNameBucket = brandNameBuckets.getJSONObject(i);
                String brandName = brandNameBucket.getString("key");
                Map<String, Map<String, Object>> resultMap = new HashMap<>();
                JSONArray indexBuckets = brandNameBucket.getJSONObject(STERMS_INDEX_AGG).getJSONArray(ESUtil.BUCKETS);
				for (int k = 0; k < indexBuckets.size(); k++) {
                    Map<String, Object> result = new HashMap<>();
                    result.put(ESUtil.AGG_BRAND_NAME, brandName);
                    JSONObject indexBucket = indexBuckets.getJSONObject(k);
                    String indexInfo = indexBucket.getString("key");
                    Matcher indexInfoMatcher = indexInfoPattern.matcher(indexInfo);
                    if (indexInfoMatcher.find()) {
                        result.put(ESUtil.AGG_INDEX_ID, indexInfoMatcher.group(1));
                        result.put(ESUtil.INDEX_NAME, indexInfoMatcher.group(2));
                    }
                    long mentionValueSum = indexBucket.getJSONObject(SUM_MENTION_VALUE_SUM).getLongValue(ESUtil.VALUE);

                    JSONArray dataSourceBuckets = indexBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);
                    DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);

                    //计算体验值
                    result.put(EXPERIENCE_VALUE, dwsKeywordExperience.getExperience());
                    resultMap.put(indexInfoMatcher.group(1), result);
                }

                for (IndexAnalysisTreeIndexSystemNode indexSystem : indexSystems) {
                    Map<String, Object> result = resultMap.get(indexSystem.getIndexId());
                    String indexId = indexSystem.getIndexId();
                    String indexName = indexSystem.getIndexName();

                    if (null == result) {
                        result = new HashMap<>();
                        result.put(ESUtil.AGG_INDEX_ID, indexId);
                        result.put(ESUtil.INDEX_NAME, indexName);
                        result.put(EXPERIENCE_VALUE, 0);
                        result.put(ESUtil.AGG_BRAND_NAME, brandName);
                        //resultList.add(result);
                    }
                    resultList.add(result);
                }
            }
            resultList.sort(new IndexOrderComparator<>());
            return resultList;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            ;
            logger.error("查询关键词提及率异常：" + e.getMessage());
            throw new ServiceException("查询关键词提及率异常");
        }
    }

    /**
     * 计算长安集团的体验值
     *
     * @param requestParams
     * @return
     */
    public List<Map<String, Object>> customerVocExperience4ChanganGroup(@RequestBody RequestParams requestParams) {
        BoolQueryBuilder boolQueryBuilder = getBoolQueryBuilder(requestParams);

        Pattern indexInfoPattern = Pattern.compile("\\[(.*)]##\\[(.*)]");
        Script indexAggScript = getAggScript(requestParams);
        AggregationBuilder indexAgg = AggregationBuilders.terms(INDEX_AGG).script(indexAggScript).size(10000);

        AggregationBuilder dataSourceAgg = AggregationBuilders.terms(DATA_SOURCE_AGG).field(ESUtil.AGG_DATA_SOURCE).size(100);

        AggregationBuilder agg = indexAgg
                .subAggregation(
                        AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE))
                .subAggregation(
                        dataSourceAgg.subAggregation(
                                AggregationBuilders.terms(EMOTION_ATTRIBUTE_AGG).field(ESUtil.EMOTION_ATTRIBUTE).subAggregation(
                                        AggregationBuilders.sum(MENTION_VALUE_SUM).field(ESUtil.MENTION_VALUE)
                                )
                        )
                );
        List<IndexAnalysisTreeIndexSystemNode> indexSystems = carHelper.getNextIndex(requestParams);

        SearchSourceBuilder sourceBuilder = SearchSourceBuilder.searchSource().query(boolQueryBuilder).aggregation(agg).size(0);
        SearchResponse search = null;
        try {
            search = doEsSearch(sourceBuilder, requestParams.getDateType());

            //分组计算正负面的提及率，然后计算 体验值
            JSONObject queryResult = JSONObject.parseObject(search.toString());
            List<Map<String, Object>> resultList = new ArrayList<>();
            Map<String, Map<String, Object>> resultMap = new HashMap<>();
            JSONArray indexBuckets = queryResult.getJSONObject(ESUtil.AGGREGATIONS).getJSONObject(STERMS_INDEX_AGG).getJSONArray(ESUtil.BUCKETS);
            for (int i = 0; i < indexBuckets.size(); i++) {
                Map<String, Object> result = new HashMap<>();
                result.put(ESUtil.AGG_BRAND_NAME, CHANG_AN_GROUP);
                JSONObject indexBucket = indexBuckets.getJSONObject(i);
                String indexInfo = indexBucket.getString("key");
                Matcher indexInfoMatcher = indexInfoPattern.matcher(indexInfo);
                if (indexInfoMatcher.find()) {
                    result.put(ESUtil.AGG_INDEX_ID, indexInfoMatcher.group(1));
                    result.put(ESUtil.INDEX_NAME, indexInfoMatcher.group(2));
                }
                JSONArray dataSourceBuckets = indexBucket.getJSONObject(STERMS_DATASOURCE_AGG).getJSONArray(ESUtil.BUCKETS);
                DwsKeywordExperience dwsKeywordExperience = calExperience(dataSourceBuckets);

                //计算体验值
                result.put(EXPERIENCE_VALUE, dwsKeywordExperience.getExperience());
                resultMap.put(indexInfoMatcher.group(1), result);
            }

            for (IndexAnalysisTreeIndexSystemNode indexSystem : indexSystems) {
                Map<String, Object> result = resultMap.get(indexSystem.getIndexId());
                String indexId = indexSystem.getIndexId();
                String indexName = indexSystem.getIndexName();

                if (null == result) {
                    result = new HashMap<>();
                    result.put(ESUtil.AGG_INDEX_ID, indexId);
                    result.put(ESUtil.INDEX_NAME, indexName);
                    result.put(EXPERIENCE_VALUE, 0);
                    result.put(ESUtil.AGG_BRAND_NAME, CHANG_AN_GROUP);
                }
                resultList.add(result);

            }
            resultList.sort(new IndexOrderComparator<>());
            return resultList;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            logger.error("查询关键词提及率异常：" + e.getMessage());
            throw new ServiceException("查询关键词提及率异常");
        }
    }
}


