package com.meicloud.voc.car.entity;

import com.meicloud.voc.common.entity.CommonEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 地域
 */
@Data
@ApiModel("地域")
public class Region extends CommonEntity{

	/**
	 * 省
	 */
	@ApiModelProperty("省")
	private String province;

	/**
	 * 市
	 */
	@ApiModelProperty("市")
	private String city;

	/**
	 * 县/区
	 */
	@ApiModelProperty("县/区")
	private String area;

	/**
	 * 城市等级
	 */
	@ApiModelProperty("城市等级")
	private String cityLevel;
}
