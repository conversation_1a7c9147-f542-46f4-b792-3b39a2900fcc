package com.meicloud.voc.common.exception;

import com.meicloud.voc.common.exception.enums.AbstractBaseExceptionEnum;

import com.meicloud.voc.common.exception.enums.CommonExceptionEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务异常的封装
 *
 * <AUTHOR>
 * @date 2021/05/11
 */
@Setter
@Getter
public class ServiceException extends RuntimeException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer code;

	private String errorMessage;

	private String errorCode;

	public ServiceException(String errorMessage) {
		super(errorMessage);
		this.code = CommonExceptionEnum.UNKNOW_ERROR.getCode();
		this.errorMessage = errorMessage;
	}
	public ServiceException(Integer code, String errorMessage) {
		super(errorMessage);
		this.code = code;
		this.errorMessage = errorMessage;
	}

	public ServiceException(AbstractBaseExceptionEnum exception) {
		super(exception.getMessage());
		this.code = exception.getCode();
		this.errorMessage = exception.getMessage();
	}

	/*
	 * @Override public synchronized Throwable fillInStackTrace() { return null; }
	 */
}
