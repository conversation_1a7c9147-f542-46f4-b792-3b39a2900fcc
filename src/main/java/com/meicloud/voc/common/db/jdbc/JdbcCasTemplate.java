package com.meicloud.voc.common.db.jdbc;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * JDBC数据库操作
 */
@Configuration
public class JdbcCasTemplate {

    @Value("${spring.datasource.dynamic.datasource.cas.driver-class-name}")
    private String driverName;

    @Value("${spring.datasource.dynamic.datasource.cas.url}")
    private String url;

    @Value("${spring.datasource.dynamic.datasource.cas.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.cas.password}")
    private String password;

    private NamedParameterJdbcTemplate jdbcTemplate;

    /**
     * 实例化
     *
     * @return
     */
    public synchronized NamedParameterJdbcTemplate jdbcCasTemplate() {
        if (jdbcTemplate == null) {
            DataSourceBuilder<?> dataSourceBuilder = DataSourceBuilder.create();
            dataSourceBuilder.driverClassName(driverName);
            dataSourceBuilder.url(url);
            dataSourceBuilder.username(username);
            dataSourceBuilder.password(password);
            jdbcTemplate = new NamedParameterJdbcTemplate(dataSourceBuilder.build());
        }
        return jdbcTemplate;
    }

    /**
     * 获取Dao
     *
     * @return
     */
    public NamedParameterJdbcTemplate getDao() {
        if (jdbcTemplate != null) {
            return jdbcTemplate;
        }
        return jdbcCasTemplate();
    }
}
