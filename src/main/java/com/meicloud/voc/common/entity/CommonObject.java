package com.meicloud.voc.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 公共实体基类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("公共实体基类")
@MappedSuperclass
public class CommonObject {
    /**
     * 编号
     */
    @ApiModelProperty("编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 状态，见：Status
     */
    @ApiModelProperty("状态，见：Status")
    private String status;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private Date lastModifyTime;
    /**
     * 应用编号
     */
    @ApiModelProperty("应用编号")
    private String appId;
    /**
     * 租户编号
     */
    @ApiModelProperty("租户编号")
    private Integer tenantId;
}
