package com.meicloud.voc.common.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ExcelDataListener<T> extends AnalysisEventListener<T> {

    private static final Logger logger = LoggerFactory.getLogger(ExcelDataListener.class);
    private List<T> resultData;

    public ExcelDataListener() {
        super();
        resultData = new ArrayList<>();
    }

    public List<T> getList() {
        return resultData;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {

        boolean exist = false;
        String[] fieldName = getFieldName(data);
        for(String string : fieldName){
            Object fieldValue = getFieldValue(data, string);
            if(fieldValue instanceof String){
                if(StringUtils.isNotBlank((String)fieldValue)){
                    exist = true;
                }
            }
            if(!Objects.isNull(fieldValue)){
                exist = true;
            }
        }

        if(!exist){
            logger.warn("该行被忽略,object={}", data);
            return;
        }
        logger.warn("该行被处理,object={}", data);
        resultData.add(data);
    }



    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    /**
     * 获取属性名数组
     * */
    public static String[] getFieldName(Object o){
        Field[] fields=o.getClass().getDeclaredFields();
        List<String> fieldNameList=new ArrayList<>();

        for(int i=0; i<fields.length; i++){
            if(fields[i].isAnnotationPresent(ExcelProperty.class)){
                fieldNameList.add(fields[i].getName());
            }
        }
        return fieldNameList.toArray(new String[fieldNameList.size()]);
    }

    /**
     * 通过属性名获取属性值  忽略大小写
     * @param o
     * @param name
     * @return
     * @throws Exception
     */

    public static Object getFieldValue(Object o,String name){
        try {
            Field[] fields = o.getClass().getDeclaredFields();
            Object object = null;
            for (Field field : fields) {
                // 可以获取到私有属性
                field.setAccessible(true);
                if (field.getName().toUpperCase().equals(name.toUpperCase())) {
                    object = field.get(o);
                    break;
                }
            }
            return object;
        }catch (Exception e) {
            logger.warn("获取值异常,field={}", o, e);
            return null;
        }
    }
}
