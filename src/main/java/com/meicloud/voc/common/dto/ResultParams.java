package com.meicloud.voc.common.dto;

import java.util.List;

import com.meicloud.voc.common.utils.DataUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 接口公共返回参数
 */
@Data
@ApiModel("接口公共返回参数")
public class ResultParams {

    @ApiModelProperty("Id")
    private String dataId;

    /**
     * 关键词对应编码
     */
    @ApiModelProperty("关键词对应编码")
    private String keyCode;
    /**
     * 关键词
     */
    @ApiModelProperty("关键词")
    private String keyWord;
    /**
     * 情感属性
     */
    @ApiModelProperty("情感属性")
    private String emotionAttribute;
    /**
     * 体验值
     */
    @ApiModelProperty("体验值")
    private Double experienceValue;
    /**
     * 体验值环比
     */
    @ApiModelProperty("体验值环比")
    private Double momExperienceValueRate;
    /**
     * 提及量
     */
    @ApiModelProperty("提及量")
    private Long totalMentionValue;
    /**
     * 提及率
     */
    @ApiModelProperty("提及率")
    private Double mentionRate;
    /**
     * 提及量环比变化
     */
    @ApiModelProperty("提及量环比变化")
    private Long momTotalMentionValue;
    /**
     * 提及量环比
     */
    @ApiModelProperty("提及量环比")
    private Double momTotalMentionValueRate;
    /**
     * 负面提及量
     */
    @ApiModelProperty("负面提及量")
    private Long negativeMentionValue;
    /**
     * 负面提及率
     */
    @ApiModelProperty("负面提及率")
    private Double negativeMentionRate;
    /**
     * 负面提及率环比
     */
    @ApiModelProperty("负面提及率环比")
    private Double momNegativeMentionRate;
    /**
     * 中性提及量
     */
    @ApiModelProperty("中性提及量")
    private Long neutralMentionValue;
    /**
     * 中性提及率
     */
    @ApiModelProperty("中性提及率")
    private Double neutralMentionRate;
    /**
     * 中性提及率环比
     */
    @ApiModelProperty("中性提及率环比")
    private Double momNeutralMentionRate;
    /**
     * 正面提及量
     */
    @ApiModelProperty("正面提及量")
    private Long positiveMentionValue;
    /**
     * 正面提及率
     */
    @ApiModelProperty("正面提及率")
    private Double positiveMentionRate;
    /**
     * 正面提及率环比
     */
    @ApiModelProperty("正面提及率环比")
    private Double momPositiveMentionRate;
    /**
     * 1是自有品牌，2是竞争品牌
     */
    @ApiModelProperty("1是自有品牌，2是竞争品牌")
    private String isOwn;
    /**
     * children
     */
    @ApiModelProperty("children")
    private List<ResultParams> children;

    /**
     * addMention
     * @param mention
     */
    public void addMention(Long mention) {
        this.totalMentionValue += mention;
    }

    /**
     * addNegMention
     * @param negMention
     */
    public void addNegMention(Long negMention) {
        this.negativeMentionValue += negMention;
    }

    public void addNeuMention(Long neuMention) {
        this.neutralMentionValue += neuMention;
    }

    public void addPosMention(Long posMention) {
        this.positiveMentionValue = posMention;
    }

    public void updExperience() {
        this.experienceValue = DataUtil.getExperience(positiveMentionValue, neutralMentionValue, negativeMentionValue);
    }

}
