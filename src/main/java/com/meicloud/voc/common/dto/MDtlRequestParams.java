package com.meicloud.voc.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 指标体系管理-原文查询请求参数
 */
@Data
@ApiModel("指标体系管理-原文查询请求参数")
public class MDtlRequestParams extends RequestParams {
    /**
     * 原文
     */
    @ApiModelProperty("原文")
    private String content;
    /**
     * 搜索模式 1： 模糊查询，2：精确查询
     */
    @ApiModelProperty("搜索模式 1： 模糊查询，2：精确查询")
    private Integer searchMode;
    /**
     * 未匹配观点
     */
    @ApiModelProperty("未匹配观点")
    private boolean unMatch;
    /**
     * 导出文件名（导出时使用）
     */
    @ApiModelProperty("导出文件名（导出时使用）")
    private String exportFileName;

}
