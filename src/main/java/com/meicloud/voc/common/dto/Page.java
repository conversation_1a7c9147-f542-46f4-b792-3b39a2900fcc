package com.meicloud.voc.common.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分页信息
 *
 * <AUTHOR>
 */
@ApiModel("分页")
public class Page {
    /**
     * 第几页，默认第一页
     */
    @ApiModelProperty("第几页，默认第一页")
    private Integer current = 1;

    /**
     * 每页显示条数，默认10条
     */
    @ApiModelProperty("每页显示条数，默认10条")
    private Integer size = 10;
    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private Integer total;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private List<PageOrder> orders;
    
    @ApiModelProperty("开始页数")
    private Integer startSize;

    /**
     * getOrders
     *
     * @param direction
     * @param property
     * @return
     */
    public static List<PageOrder> getOrders(String direction, String property) {
        List<PageOrder> orders = new ArrayList<PageOrder>();
        if ("desc".equals(direction)) {
            orders.add(PageOrder.desc(property));
        } else {
            orders.add(PageOrder.asc(property));
        }
        return orders;
    }

    /**
     * getOrders
     *
     * @param pageOrder
     * @return
     */
    public static List<PageOrder> getOrders(PageOrder pageOrder) {
        List<PageOrder> orders = new ArrayList<PageOrder>();
        orders.add(pageOrder);
        return orders;
    }

    /**
     * getCurrent
     *
     * @return
     */
    public Integer getCurrent() {
        if (current == null || current <= 0) {
            current = 1;
        }
        return current;
    }

    /**
     * setCurrent
     *
     * @param current
     */
    public void setCurrent(Integer current) {
        this.current = current;
    }

    /**
     * getSize
     *
     * @return
     */
    public Integer getSize() {
        if (size == null || size <= 0) {
            size = 10;
        }
        return size;
    }

    /**
     * setSize
     *
     * @param size
     */
    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * getTotal
     *
     * @return
     */
    public Integer getTotal() {
        return total;
    }

    /**
     * setTotal
     *
     * @param total
     */
    public void setTotal(Integer total) {
        this.total = total;
    }

    /**
     * getOrders
     *
     * @return
     */
    public List<PageOrder> getOrders() {
        return orders;
    }

    /**
     * setOrders
     *
     * @param orders
     */
    public void setOrders(List<PageOrder> orders) {
        this.orders = orders;
    }

	public Integer getStartSize() {
		return startSize;
	}

	public void setStartSize(Integer startSize) {
		this.startSize = startSize;
	}

    
}
