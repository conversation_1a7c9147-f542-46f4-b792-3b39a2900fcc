package com.meicloud.voc.common.utils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.meicloud.voc.car.entity.DwdEvtWorkOrderDtlEntity;
import com.meicloud.voc.car.entity.DwsKeywordMentionDay;
import com.meicloud.voc.common.dto.ResultParams;
import com.meicloud.voc.common.enums.SortTypeEnum;

/**
 * 集合工具
 */
public final class ListUtil {
    /**
     * 集合排序
     *
     * @param dataList
     */
    public static void sortList(List<ResultParams> dataList) {
        Collections.sort(dataList, new Comparator<ResultParams>() {
            @Override
            public int compare(ResultParams o1, ResultParams o2) {
                return (Long.valueOf(o1.getKeyCode()) > Long.valueOf(o2.getKeyCode()) ? 1 : -1);
            }
        });
    }
    /**
	 * 	对结果列表按指定字段排序
	 * @param dataList
	 * @param sortType
	 */
	public static void sortList(List<ResultParams> dataList, SortTypeEnum sortType) {
		if(sortType == null) {
			return; 
		}
		Collections.sort(dataList, new Comparator<ResultParams>() {
			@Override
			public int compare(ResultParams info1, ResultParams info2) {
				if(sortType == SortTypeEnum.KEYWORD_ASC || sortType == SortTypeEnum.KEYWORD_DESC) {
					return getKeyword(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.MENTION_ASC || sortType == SortTypeEnum.MENTION_DESC) {
					return getTotalMentionValue(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.MENTION_CYCLE_ASC || sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
					return getMomTotalMentionValueRate(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.MENTION_ADD_ASC || sortType == SortTypeEnum.MENTION_ADD_DESC) {
					return getMomTotalMentionValue(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.MENTION_RATE_ASC || sortType == SortTypeEnum.MENTION_RATE_DESC) {
					return getMentionRate(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.INDEX_ID_ASC || sortType == SortTypeEnum.INDEX_ID_DESC
						|| sortType == SortTypeEnum.KEY_WORD_ASC || sortType == SortTypeEnum.KEY_WORD_DESC) {
					return getKeyCode(info1, info2, sortType);
				} else if(sortType == SortTypeEnum.MENTION_CYCLE_ABS_ASC || sortType == SortTypeEnum.MENTION_CYCLE_ABS_DESC) {
					return getMomTotalMentionValueRateAbs(info1, info2, sortType);
				}
				return 0;
			}
		});
	}
	/**
	 * 关键词排序
	 */
	public static int getKeyword(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getKeyCode() == null) {
			return -1;
		}else if(info2.getKeyCode() == null) {
			return 1;
		}
		int value = info1.getKeyCode().compareTo(info2.getKeyCode());
		if(sortType == SortTypeEnum.KEYWORD_DESC) {
			value = value * -1;
		} 
		return value;
		
	}
	/**
	 * 提及量排序
	 */
	public static int getTotalMentionValue(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getTotalMentionValue() == null) {
			return -1;
		}else if(info2.getTotalMentionValue() == null) {
			return 1;
		}
		int value = info1.getTotalMentionValue().compareTo(info2.getTotalMentionValue());
		if(sortType == SortTypeEnum.MENTION_DESC) {
			value = value * -1;
		} 
		return value;
		
	}
	/**
	 * 提及量环比排序
	 */
	public static int getMomTotalMentionValueRate(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getMomTotalMentionValueRate() == null) {
			return -1;
		}else if(info2.getMomTotalMentionValueRate() == null) {
			return 1;
		}
		int value = info1.getMomTotalMentionValueRate().compareTo(info2.getMomTotalMentionValueRate());
		if(sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
			value = value * -1;
		} 
		return value;
		
	}

	/**
	 * 提及量环比排序
	 */
	public static int getMomTotalMentionValueRateAbs(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getMomTotalMentionValueRate() == null) {
			return -1;
		}else if(info2.getMomTotalMentionValueRate() == null) {
			return 1;
		}
		Double abs1 = Math.abs(info1.getMomTotalMentionValueRate().doubleValue());
		Double abs2 = Math.abs(info2.getMomTotalMentionValueRate().doubleValue());
		int value = abs1.compareTo(abs2);
		if(sortType == SortTypeEnum.MENTION_CYCLE_ABS_DESC) {
			value = value * -1;
		}
		return value;

	}
	/**
	 * 提及量环比排序
	 */
	public static int getMomTotalMentionValue(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getMomTotalMentionValue() == null) {
			return -1;
		}else if(info2.getMomTotalMentionValue() == null) {
			return 1;
		}
		int value = info1.getMomTotalMentionValue().compareTo(info2.getMomTotalMentionValue());
		if(sortType == SortTypeEnum.MENTION_ADD_DESC) {
			value = value * -1;
		} 
		return value;
		
	}
	/**
	 * 提及率排序
	 */
	public static int getMentionRate(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getMentionRate() == null) {
			return -1;
		}else if(info2.getMentionRate() == null) {
			return 1;
		}
		int value = info1.getMentionRate().compareTo(info2.getMentionRate());
		if(sortType == SortTypeEnum.MENTION_RATE_DESC) {
			value = value * -1;
		} 
		return value;
		
	}
	/**
	 * 关键词编码
	 */
	public static int getKeyCode(ResultParams info1, ResultParams info2, SortTypeEnum sortType) {
		if(info1.getKeyCode() == null) {
			return -1;
		}else if(info2.getKeyCode() == null) {
			return 1;
		}
		int value = info1.getKeyCode().compareTo(info2.getKeyCode());
		if(sortType == SortTypeEnum.INDEX_ID_DESC || sortType == SortTypeEnum.KEY_WORD_DESC) {
			value = value * -1;
		} 
		return value;
		
	}
	/**
	 * DwsKeywordMentionDay对象排序
	 */
	public static void sortDwsList(List<DwsKeywordMentionDay> dataList, SortTypeEnum sortType) {
		if(sortType == null) {
			return; 
		}
		Collections.sort(dataList, new Comparator<DwsKeywordMentionDay>() {
			@Override
			public int compare(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2) {
				if (sortType == SortTypeEnum.KEYWORD_ASC || sortType == SortTypeEnum.KEYWORD_DESC) {
					return getStandardKeyword(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.MENTION_ASC || sortType == SortTypeEnum.MENTION_DESC) {
                	return getTotalMentionValue(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.MENTION_CYCLE_ASC || sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
                	return getMomTotalMentionValueRate(o1, o2, sortType);
                } else if (sortType == SortTypeEnum.MENTION_ADD_ASC || sortType == SortTypeEnum.MENTION_ADD_DESC) {
                	return getMomTotalMentionValue(o1, o2, sortType);
                } else if (sortType == SortTypeEnum.MENTION_RATE_ASC || sortType == SortTypeEnum.MENTION_RATE_DESC) {
                	return getMentionRate(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.INDEX_ID_ASC || sortType == SortTypeEnum.INDEX_ID_DESC) {
                	return getIndexId(o1, o2, sortType);
                }
                return 0;
			}
		});
	}
	/**
	 * 关键词排序
	 */
	public static int getStandardKeyword(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		if (o1.getStandardKeyword() == null) {
			return -1;
		} else if (o2.getStandardKeyword() == null) {
			return 1;
		}
		// Todo: 后续使用了standardKeywordId需要修改此部分
		int value = o1.getStandardKeyword().compareTo(o2.getStandardKeyword());
		if (sortType == SortTypeEnum.KEYWORD_DESC) {
			value = value * -1;
		}
		return value;

	}
	/**
	 * 提及量排序
	 */
	public static int getTotalMentionValue(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		int value = Long.valueOf(o1.getTotalMentionValue()).compareTo(Long.valueOf(o2.getTotalMentionValue()));
		if (sortType == SortTypeEnum.MENTION_DESC) {
			value = value * -1;
		}
		return value;
		
	}
	/**
	 * 提及量环比排序
	 */
	public static int getMomTotalMentionValueRate(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		int value = Double.valueOf(o1.getMomTotalMentionValueRate()).compareTo(Double.valueOf(o2.getMomTotalMentionValueRate()));
		if (sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
			value = value * -1;
		}
		return value;
		
	}
	/**
	 * 提及量环比变化排序
	 */
	public static int getMomTotalMentionValue(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		int value = Long.valueOf(o1.getMomTotalMentionValue()).compareTo(Long.valueOf(o2.getMomTotalMentionValue()));
		if (sortType == SortTypeEnum.MENTION_ADD_DESC) {
			value = value * -1;
		}
		return value;
		
	}
	/**
	 * 提及率排序
	 */
	public static int getMentionRate(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		int value = Double.valueOf(o1.getMentionRate()).compareTo(Double.valueOf(o2.getMentionRate()));
		if (sortType == SortTypeEnum.MENTION_RATE_DESC) {
			value = value * -1;
		}
		return value;
		
	}
	/**
	 * 指标id排序
	 */
	public static int getIndexId(DwsKeywordMentionDay o1, DwsKeywordMentionDay o2, SortTypeEnum sortType) {
		if(o1.getIndexId() == null) {
			return -1;
		} else if(o2.getIndexId() == null) {
			return 1;
		}
		int value = o1.getIndexId().compareTo(o2.getIndexId());
		if (sortType == SortTypeEnum.INDEX_ID_DESC) {
			value = value * -1;
		}
		return value;
		
	}
	
    /**
     * 对结果列表按指定字段排序
     *
     * @param dataList
     * @param sortType
     */
    public static void sortDwdList(List<DwdEvtWorkOrderDtlEntity> dataList, SortTypeEnum sortType) {
        if (sortType == null) {
            return;
        }
        Collections.sort(dataList, new Comparator<DwdEvtWorkOrderDtlEntity>() {
            @Override
            public int compare(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2) {
            	if (sortType == SortTypeEnum.KEYWORD_ASC || sortType == SortTypeEnum.KEYWORD_DESC) {
            		return getStandardKeyword(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.MENTION_ASC || sortType == SortTypeEnum.MENTION_DESC) {
                	return getTotalMentionValue(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.MENTION_CYCLE_ASC || sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
                	return getMomTotalMentionValueRate(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.MENTION_ADD_ASC || sortType == SortTypeEnum.MENTION_ADD_DESC) {
                	return getMomTotalMentionValue(o1, o2, sortType);
                } else if (sortType == SortTypeEnum.MENTION_RATE_ASC || sortType == SortTypeEnum.MENTION_RATE_DESC) {
                	return getMentionRate(o1, o2, sortType);
                }else if (sortType == SortTypeEnum.INDEX_ID_ASC || sortType == SortTypeEnum.INDEX_ID_DESC) {
                	return getIndexId(o1, o2, sortType);
                }
                return 0;
            }
        });
    }
    /**
     * 关键词排序值
     */
    public static int getStandardKeyword(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
		if(o1.getStandardKeyword() == null) {
            return -1;
        } else if(o2.getStandardKeyword() == null) {
            return 1;
        }
        // Todo: 后续使用了standardKeywordId需要修改此部分
		int value = o1.getStandardKeyword().compareTo(o2.getStandardKeyword());
		if (sortType == SortTypeEnum.KEYWORD_DESC) {
			value = value * -1;
		}
		return value;
		
	}
    /**
     * 提及量排序值
     */
    public static int getTotalMentionValue(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
    	int value = Long.valueOf(o1.getTotalMentionValue()).compareTo(Long.valueOf(o2.getTotalMentionValue()));
    	if (sortType == SortTypeEnum.MENTION_DESC) {
    		value = value * -1;
    	}
    	return value;
    	
    }
    /**
     * 提及量环比排序值
     */
    public static int getMomTotalMentionValueRate(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
    	int value = Double.valueOf(o1.getMomTotalMentionValueRate()).compareTo(Double.valueOf(o2.getMomTotalMentionValueRate()));
    	if (sortType == SortTypeEnum.MENTION_CYCLE_DESC) {
    		value = value * -1;
    	}
    	return value;
    	
    }
    /**
     * 提及量环比变化排序值
     */
    public static int getMomTotalMentionValue(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
    	int value = Long.valueOf(o1.getMomTotalMentionValue()).compareTo(Long.valueOf(o2.getMomTotalMentionValue()));
    	if (sortType == SortTypeEnum.MENTION_ADD_DESC) {
    		value = value * -1;
    	}
    	return value;
    	
    }
    /**
     * 提及率排序值
     */
    public static int getMentionRate(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
    	int value = Double.valueOf(o1.getMentionRate()).compareTo(Double.valueOf(o2.getMentionRate()));
    	if (sortType == SortTypeEnum.MENTION_RATE_DESC) {
    		value = value * -1;
    	}
    	return value;
    	
    }
    /**
     * 提及率排序值
     */
    public static int getIndexId(DwdEvtWorkOrderDtlEntity o1, DwdEvtWorkOrderDtlEntity o2, SortTypeEnum sortType) {
    	if(o1.getIndexId() == null) {
            return -1;
        } else if(o2.getIndexId() == null) {
            return 1;
        }
    	int value = o1.getIndexId().compareTo(o2.getIndexId());
    	if (sortType == SortTypeEnum.INDEX_ID_DESC) {
    		value = value * -1;
    	}
    	return value;
    	
    }
}
