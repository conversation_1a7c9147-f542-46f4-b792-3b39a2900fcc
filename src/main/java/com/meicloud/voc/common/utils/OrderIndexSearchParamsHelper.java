package com.meicloud.voc.common.utils;


import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import com.meicloud.voc.common.dto.RequestParams;
import com.meicloud.voc.common.enums.IndexTypeEnum;


@Slf4j
@ApiModel("工单索引-公共筛选条件")
public class OrderIndexSearchParamsHelper {

    /**
     * 媒体舆情基础搜索条件转换
     */
    public static BoolQueryBuilder convertParams2Builder(RequestParams params) {
        if (log.isDebugEnabled()) {
            log.debug("convertParams2Builder: params={}", params);
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //指标类型
        QueryBuilder nestedQuery = null;
        IndexTypeEnum indexType = IndexTypeEnum.getIndexType(params.getIndexType());
        nestedQuery = setIndexParams(params, boolQuery, nestedQuery, indexType);
        //开始日期和结束日期
        if(StringUtils.isNoneBlank(params.getStartDate()) && StringUtils.isNoneBlank(params.getEndDate())) {
        	boolQuery.must(QueryBuilders.rangeQuery("batchDt").from(params.getStartDate()).to(params.getEndDate()));
        }
        //细分市场names
        if(CollectionUtils.isNotEmpty(params.getMarketNames())) {
        	boolQuery.must(QueryBuilders.termsQuery("marketName", params.getMarketNames()));
        }
        //数据来源
        if(CollectionUtils.isNotEmpty(params.getDataSources())) {
        	boolQuery.must(QueryBuilders.termsQuery("dataSource", params.getDataSources()));
        }
        //情感词属性
        if(StringUtils.isNoneBlank(params.getEmotionAttribute())) {
        	boolQuery.must(QueryBuilders.termQuery("emotionAttribute", params.getEmotionAttribute()));
        }
        //内外部数据
        if(StringUtils.isNoneBlank(params.getIsOuter())) {
        	boolQuery.must(QueryBuilders.termQuery("isOuter", params.getIsOuter()));
        }
        if(nestedQuery != null) {
        	boolQuery.must(nestedQuery);
        }
        return boolQuery;
    }
    /**
     * 设置指标体系
     */
	private static QueryBuilder setIndexParams(RequestParams params, BoolQueryBuilder boolQuery,
			QueryBuilder nestedQuery, IndexTypeEnum indexType) {
		if(indexType != null) {
			String nestedPath = indexType.getNestedPath();
        	BoolQueryBuilder query = QueryBuilders.boolQuery();
        	query.must(QueryBuilders.termQuery(nestedPath+".extractedDomain", indexType.getMessage()));
			//一级指标id
	        if(StringUtils.isNoneBlank(params.getFirstIndexId())) {
	        	query.must(QueryBuilders.termQuery(nestedPath+".extractedDimension", params.getFirstIndexId()));
	        }
	        //二级指标id
	        if(StringUtils.isNoneBlank(params.getSecondIndexId())) {
	        	boolQuery.must(QueryBuilders.termQuery(nestedPath+".extractedAttribute", params.getSecondIndexId()));
	        }
	        //三级指标id
	        if(StringUtils.isNoneBlank(params.getThirdIndexId())) {
	        	boolQuery.must(QueryBuilders.termQuery(nestedPath+".extractedProperty", params.getThirdIndexId()));
	        }
	        //四级指标id
	        if(StringUtils.isNoneBlank(params.getFourIndexId())) {
	        	boolQuery.must(QueryBuilders.termQuery(nestedPath+".extractedSubProperty", params.getFourIndexId()));
	        }
			nestedQuery = QueryBuilders.nestedQuery(nestedPath, query, ScoreMode.None);
        }
		return nestedQuery;
	}
}
