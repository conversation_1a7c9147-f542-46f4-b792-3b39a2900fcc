package com.meicloud.voc.company.dto;

import com.meicloud.voc.user.dto.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 租户列表参数
 *
 * <AUTHOR>
 * @date 2021/05/11
 */
@Data
@ApiModel(description = "租户列表参数")
public class GetCompanyListParam extends PageParam {

    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String searchKey;

}
