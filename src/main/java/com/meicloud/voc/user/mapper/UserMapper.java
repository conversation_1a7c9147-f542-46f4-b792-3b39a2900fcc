package com.meicloud.voc.user.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meicloud.voc.security.api.vo.UserInfoVo;
import com.meicloud.voc.user.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper
 */
public interface UserMapper extends BaseMapper<User> {
    /**
     * 获取用户信息
     *
     * @param userAccount
     * @param authUrl
     * @return
     */
    User getByAccountAndAuthUrl(@Param("userAccount") String userAccount, @Param("authUrl") String authUrl);

    /**
     * 按条件分页搜索
     *
     * @param page
     * @param searchKey
     * @param companyId
     * @param userType
     * @return
     */
    Page<User> selectByCondition(Page<User> page, @Param("searchKey") String searchKey,
                                 @Param("companyId") int companyId, @Param("userType") int userType);

    /**
     * 按角色搜索
     *
     * @param groupId
     * @return
     */
    List<User> selectByGroup(@Param("groupId") int groupId);

    /**
     * 按公司部门搜索
     *
     * @param companyId
     * @param deptCode
     * @return
     */
    List<User> selectByCompanyDept(@Param("companyId") int companyId, @Param("deptCode") String deptCode);

    /**
     * 获取用户列表
     *
     * @param userAccount
     * @param userName
     * @param startRow
     * @param pageSize
     * @return
     */
    List<UserInfoVo> getUserList(@Param("userAccount") String userAccount, @Param("userName") String userName,
                                 @Param("startRow") int startRow, @Param("pageSize") int pageSize);

    /**
     * 获取用户数
     *
     * @param userAccount
     * @param userName
     * @return
     */
    int getUserListTotal(@Param("userAccount") String userAccount, @Param("userName") String userName);
    
    /**
     * 修改voc用户状态
     */
    int updStatus(@Param("userAccount") String userAccount, @Param("status") String status);

    /**
     * 修改星谋云用户状态
     */
    @DS("xmyBase")
    int updXmyStatus(@Param("userAccount") String userAccount, @Param("status") String status);

}
