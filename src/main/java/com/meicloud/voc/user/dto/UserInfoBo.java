package com.meicloud.voc.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2021/05/11
 */
@Data
@ApiModel("用户信息")
public class UserInfoBo {
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private String userId;
    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;
    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private Integer companyId;
    /**
     * 租户名称
     */
    @ApiModelProperty("租户名称")
    private String companyName;
    /**
     * 用户账号
     */
    @ApiModelProperty("用户账号")
    private String userAccount;
    /**
     * 微信昵称
     */
    @ApiModelProperty("微信昵称")
    private String wechatName;
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String deptName;
    /**
     * LOGO
     */
    @ApiModelProperty("LOGO")
    private String logo;
    /**
     * 职务
     */
    @ApiModelProperty("职务")
    private String positionName;
    /**
     * 是否为管理员，0：普通用户，1：普通管理员
     */
    @ApiModelProperty("是否为管理员，0：普通用户，1：普通管理员")
    private Integer isadmin;
    /**
     * 有效标识:1有效，0失效
     */
    @ApiModelProperty("有效标识:1有效，0失效")
    private Integer isEnabled;
    /**
     * 用户登录方式，1密码，2key，3微信
     */
    @ApiModelProperty("用户登录方式，1密码，2key，3微信")
    private String loginType;
    /**
     * 最后有效时间
     */
    @ApiModelProperty("最后有效时间")
    private String validEndDate;
}
