package com.meicloud.voc.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 导出工具
 */
public class ExportUtil {

    private static final String GET_BIG_EXECL_WRITER_FAILED_MSG = "%s获取BigExcelWriter失败";
    /**
     * 写缓存
     */
    private static Map<String, BigExcelWriter> writerMap = new ConcurrentHashMap<>();

    /**
     * 初始化缓存
     *
     * @param savePath
     */
    public static void initWriter(String savePath) {
        writerMap.put(savePath, ExcelUtil.getBigWriter(savePath));
    }

    /**
     * 写标题
     *
     * @param fileName
     * @param titles
     */
    public static void writerTitle(String fileName, Iterable<?> titles) {
        if (!writerMap.containsKey(fileName)) {
            throw new RuntimeException(String.format(GET_BIG_EXECL_WRITER_FAILED_MSG,fileName));
        }
        writerMap.get(fileName).writeRow(titles);
    }

    /**
     * 写行
     *
     * @param fileName
     * @param rowData
     */
    public static void writeRow(String fileName, Map<String, Object> rowData) {
        if (!writerMap.containsKey(fileName)) {
            throw new RuntimeException(String.format(GET_BIG_EXECL_WRITER_FAILED_MSG,fileName));
        }
        writerMap.get(fileName).writeRow(rowData, false);
    }

    /**
     * 写入行
     *
     * @param fileName
     * @param rowData
     * @param isWriteKeyAsHead
     */
    public static void writeRow(String fileName, Map<String, Object> rowData, boolean isWriteKeyAsHead) {
        if (!writerMap.containsKey(fileName)) {
            throw new RuntimeException(String.format(GET_BIG_EXECL_WRITER_FAILED_MSG,fileName));
        }
        writerMap.get(fileName).writeRow(rowData, isWriteKeyAsHead);
    }

    /**
     * 对json数组进行处理并添加到row里面
     * @param array
     * @param row
     * @param k
     */
    private static void handleArrayToRow(JSONArray array,  Map<String, Object> row, String k) {
        boolean isToString = false;
        for (Object o1 : array) {
            if (o1 instanceof String) {
                isToString = true;
                break;
            }
            JSONObject jsonObject1 = (JSONObject) o1;
            jsonObject1.forEach((k1, v1) -> {
                String newKey = k + "." + k1;
                if (row.containsKey(newKey)) {
                    row.put(newKey, row.get(newKey) + "," + v1);
                } else {
                    row.put(newKey, v1);
                }
            });
        }
        if (isToString) {
            row.put(k, array.join(","));
        }
    }

    /**
     * JSON对象转换为Map
     * @param source
     * @return
     */
    public static Map<String, Object> convertToRow(JSONObject source) {
        Map<String, Object> row = new HashMap<>();
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            if (v instanceof JSONArray) {
                JSONArray array = (JSONArray) v;
                handleArrayToRow(array, row, k);
            } else {
                row.put(k, v);
            }
        }
        return row;
    }    /**
     * 写入ES数据
     *
     * @param fileName
     * @param exportData
     * @param fieldMapping
     * @param handler
     */
    public static void writeEsData(String fileName, JSONObject exportData, Map<String, String> fieldMapping, Function<Map<String, Object>, Map<String, Object>> handler) {
        if (isEmpty(exportData)) {
            return;
        }
        writerTitle(fileName, getReultRow(new HashMap<>(), fieldMapping).keySet());
        JSONArray jsonArray = exportData.getJSONObject("hits").getJSONArray("hits");
        for (Object o : jsonArray) {
            try {
                JSONObject hit = (JSONObject) o;
                JSONObject source = hit.getJSONObject("_source");
                Map<String, Object> row = convertToRow(source);
                writerMap.get(fileName).writeRow(getReultRow(handler.apply(row), fieldMapping), false);
            } catch (Exception e) {
                // TODO
            }
        }
    }

    /**
     * 获取结果行
     *
     * @param row
     * @param fieldMapping
     * @return
     */
    private static Map<String, Object> getReultRow(Map<String, Object> row, Map<String, String> fieldMapping) {
        Map<String, Object> result = new HashMap<>();
        fieldMapping.forEach((k, v) -> {
            if (!row.containsKey(k)) {
                result.put(v, "");
            } else if (k.contains("Time")) {
                result.put(v, DateUtil.date((long) row.get(k)).toString("yyyy-MM-dd HH:mm:ss"));
            } else {
                result.put(v, row.get(k));
            }
        });
        return result;
    }

    /**
     * 判断是否为空
     *
     * @param searchResponse
     * @return
     */
    private static boolean isEmpty(JSONObject searchResponse) {
        return searchResponse.getJSONObject("hits").getJSONArray("hits").isEmpty();
    }

    /**
     * 关闭
     *
     * @param fileName
     */
    public static void close(String fileName) {
        writerMap.get(fileName).close();
    }
}
