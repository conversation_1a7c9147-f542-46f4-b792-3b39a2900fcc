package com.meicloud.voc.wechat.entity;


import lombok.Data;

@Data
public class Messge{
	/**
	 * 应用编号"UserID1|UserID2|UserID3"
	 */
	private String touser;
	/**
	 * 应用编号
	 */
	private String toparty;
	/**
	 * 应用编号
	 */
	private String totag;
	/**
	 * 企业应用的id，整型。
	 */
	private Integer agentid;
	/**
	 * 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
	 */
	private int safe = 0;
	/**
	 * 表示是否开启id转译，0表示否，1表示是，默认0。
	 */
	private int enable_id_trans = 0;
	/**
	 * 表示是否开启重复消息检查，0表示否，1表示是，默认0
	 */
	private int enable_duplicate_check = 0;
	/**
	 * 表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
	 */
	private int duplicate_check_interval = 1800;

	
}
