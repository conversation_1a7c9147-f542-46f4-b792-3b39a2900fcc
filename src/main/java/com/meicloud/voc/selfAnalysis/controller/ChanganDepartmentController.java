package com.meicloud.voc.selfAnalysis.controller;


import com.alibaba.fastjson.JSONObject;
import com.meicloud.voc.common.dto.Result;
import com.meicloud.voc.common.utils.ApiUtil;
import com.meicloud.voc.common.utils.ResultUtil;
import com.meicloud.voc.security.api.service.ISystemService;
import com.meicloud.voc.security.api.vo.RoleInfoVo;
import com.meicloud.voc.selfAnalysis.service.IChanganDepartmentService;
import com.meicloud.voc.user.entity.UserGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人员部门信息
 */
@Api(tags = "人员部门信息")
@RestController
@RequestMapping("/changan/department")
@Slf4j
public class ChanganDepartmentController {

    @Autowired
    private IChanganDepartmentService changanDepartmentService;

    @Autowired
    private ISystemService systemService;

    /**
     * 长安集团-获取用户信息
     *
     * @param keyWords
     * @param departmentId
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @ApiOperation("长安集团-获取用户信息")
    @RequestMapping(value = "/getChanganUsers", method = {RequestMethod.GET})
    public Result<JSONObject> getChanganUsers(@RequestParam(required = true) String keyWords,
                                              @RequestParam(required = false) String departmentId,
                                              @RequestParam(required = false, defaultValue = "1") int pageIndex,
                                              @RequestParam(required = false, defaultValue = "50") int pageSize) {
        return ResultUtil.getSuccess(changanDepartmentService.getChanganUsers(keyWords, departmentId, pageIndex, pageSize));
    }

    /**
     * 长安集团-获取所有用户信息
     *
     * @param personCatagray
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @ApiOperation("长安集团-获取所有用户信息")
    @RequestMapping(value = "/getAllUserInfo", method = {RequestMethod.GET})
    public Result<JSONObject> getAllUserInfo(@RequestParam(required = false, defaultValue = "LOCAL") String personCatagray,
                                             @RequestParam(required = false, defaultValue = "1") int pageIndex,
                                             @RequestParam(required = false, defaultValue = "50") int pageSize) {
        return ResultUtil.getSuccess(changanDepartmentService.getAllUserInfo(personCatagray, pageIndex, pageSize));
    }

    /**
     * 长安集团-得到当前部门下的所有用户
     *
     * @param departmentId
     * @param allChild
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @ApiOperation("长安集团-得到当前部门下的所有用户")
    @RequestMapping(value = "/getUserListInDept", method = {RequestMethod.GET})
    public Result<JSONObject> getUserListInDept(@RequestParam(required = false) String departmentId,
                                                @RequestParam(required = false, defaultValue = "true") Boolean allChild,
                                                @RequestParam(required = false, defaultValue = "1") int pageIndex,
                                                @RequestParam(required = false, defaultValue = "50") int pageSize) {
        return ResultUtil.getSuccess(changanDepartmentService.getUserListInDept(departmentId, allChild, pageIndex, pageSize));
    }

    /**
     * 长安集团-获取部门信息
     *
     * @return
     */
    @ApiOperation("长安集团-获取部门信息")
    @RequestMapping(value = "/getDepartmentTree", method = {RequestMethod.GET})
    public Result<JSONObject> getDepartmentTree() {
        return ResultUtil.getSuccess(changanDepartmentService.getDepartmentTree());
    }

    @ApiOperation("长安集团-获取指定用户的用户角色")
    @RequestMapping(value = "/getUserRole", method = {RequestMethod.GET})
    public Result<List<UserGroup>> getUserRole(@RequestParam(required = true) String loginId) {
        return ResultUtil.getSuccess(changanDepartmentService.getUserRole(loginId));
    }

    @ApiOperation("长安集团-获取角色列表")
    @RequestMapping(value = "/getRoleList", method = {RequestMethod.GET})
    public Result<Map<String, List<RoleInfoVo>>> getRoleList(Integer roleId, String roleName) {
        if (log.isDebugEnabled()) {
            log.debug("SystemController.getRoleList()");
        }
        log.info("roleId:{},roleName:{}", roleId, roleName);
        Map<String,List<RoleInfoVo>> dataMap = new HashMap<String, List<RoleInfoVo>>();
        try {
            List<RoleInfoVo> roleList = systemService.getRoleList(roleId, roleName);
            dataMap.put("roles", roleList);
        } catch (Exception e) {
            log.error("查询角色异常", e);
            return ResultUtil.getError("查询角色异常");
        }
        return ResultUtil.getSuccess(dataMap);
    }

    /**
     * 长安集团-根据用户登录名获取用户信息
     *
     * @param loginId
     * @return
     */
    @ApiOperation("长安集团-根据用户登录名获取用户信息")
    @RequestMapping(value = "/getUserByLoginId", method = {RequestMethod.GET})
    public Result<JSONObject> getUserByLoginId(@RequestParam(required = true) String loginId) {
        return ResultUtil.getSuccess(changanDepartmentService.getUserByLoginId(loginId));
    }

    /**
     * 长安集团-根据token获取用户信息
     *
     * @return
     */
    @ApiOperation("长安集团-根据token获取用户信息")
    @RequestMapping(value = "/getUserByToken", method = {RequestMethod.GET})
    public Result<JSONObject> getUserByToken() {
        return ResultUtil.getSuccess(changanDepartmentService.getUserByToken());
    }

}
