package com.meicloud.voc.selfAnalysis.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meicloud.voc.car.entity.IndexSystemKeyword;
import com.meicloud.voc.car.mapper.IndexSystemKeywordMapper;
import com.meicloud.voc.selfAnalysis.service.IIndexSystemKeywordService;

/**
 * 指标体系标准关键词服务实现类
 */
@Service
public class IndexSystemKeywordServiceImpl extends ServiceImpl<IndexSystemKeywordMapper, IndexSystemKeyword> implements IIndexSystemKeywordService {

    /**
     * 通过standardKeywordId 查找对应的关键词
     *
     * @param standardKeywordId
     * @return
     */
    @Override
    public List<IndexSystemKeyword> findByStandardKeywordId(Long standardKeywordId) {
        return this.baseMapper.getIndexSystemKeyword(null, standardKeywordId);
    }
}
