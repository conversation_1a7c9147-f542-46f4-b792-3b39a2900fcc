package com.voc.service.model.basic.carSeriesNames;


import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 同步车系、指标信息表
 */
public class SysDataInfo implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -6102005460407393605L;

    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 父级编码
     */
    private String parentCode;

    /**
     * 数据类型
     * PRODUCT 车系
     * BUSINESS_TAG 标签
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;


    private String brand;

    private String tagType;


    public String getBrand() {
        return brand;
    }


    public void setBrand(String brand) {
        this.brand = brand;
    }


    public String getTagType() {
        return tagType;
    }


    public void setTagType(String tagType) {
        this.tagType = tagType;
    }


    public String getId() {
        return id;
    }

    private String orgId;


    public String getOrgId() {
        return orgId;
    }


    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }


    public void setId(String id) {
        this.id = id;
    }


    public String getName() {
        return name;
    }


    public void setName(String name) {
        this.name = name;
    }


    public String getCode() {
        return code;
    }


    public void setCode(String code) {
        this.code = code;
    }


    public String getParentCode() {
        return parentCode;
    }


    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }


    public String getType() {
        return type;
    }


    public void setType(String type) {
        this.type = type;
    }


    public LocalDateTime getCreateDate() {
        return createDate;
    }


    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }


    public LocalDateTime getUpdateDate() {
        return updateDate;
    }


    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

}
