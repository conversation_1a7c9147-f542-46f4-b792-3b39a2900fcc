package com.voc.service.model.basic.constants;

import java.util.HashSet;
import java.util.Set;

public class RelatedDict {

	
	public static Set<String> saleSet = new HashSet<String>();
	public static Set<String> after_saleSet = new HashSet<String>();
	static {
		saleSet.add("销售");
		saleSet.add("看车");
		saleSet.add("买车");
		saleSet.add("提车");
		saleSet.add("交车");
		saleSet.add("交付");
		saleSet.add("经销商");
		saleSet.add("试驾");
		saleSet.add("试乘");
		saleSet.add("金融人员");
		saleSet.add("金融服务");
		saleSet.add("购车");
		saleSet.add("选装");
		saleSet.add("售车");
		saleSet.add("接待");
		saleSet.add("营销");
		
		saleSet.add("销售");
		saleSet.add("看车");
		saleSet.add("买车");
		saleSet.add("买");
		saleSet.add("卖车");
		saleSet.add("提车");
		saleSet.add("交车");
		saleSet.add("交付");
		saleSet.add("经销商");
		saleSet.add("试驾");
		saleSet.add("试乘");
		saleSet.add("金融人员");
		saleSet.add("金融服务");
		saleSet.add("购车");
		saleSet.add("选装");
		saleSet.add("售车");
		saleSet.add("营销");
		saleSet.add("推销#车");
		saleSet.add("接待");
		saleSet.add("客服");
		saleSet.add("服务态度");
		saleSet.add("介绍#功能");
		saleSet.add("介绍#性能");
		saleSet.add("介绍#车");
		saleSet.add("讲解#功能");
		saleSet.add("讲解#性能");
		saleSet.add("讲解#车");
		saleSet.add("订车");
		saleSet.add("签合同");
		saleSet.add("定车");
		saleSet.add("订金");
		saleSet.add("定金");
		saleSet.add("招待");
		saleSet.add("置换");
		saleSet.add("介绍");
		saleSet.add("加装");
		saleSet.add("办理牌照");
		saleSet.add("上牌");
		saleSet.add("服务人员");
		saleSet.add("业务员");
		saleSet.add("咨询#解答");
		saleSet.add("客户");
		saleSet.add("发票");
		saleSet.add("购车");
		saleSet.add("车型方面");
		saleSet.add("介绍");
		saleSet.add("办手续");
		saleSet.add("推荐车");
		saleSet.add("凭证");
		
		after_saleSet.add("售后");
		after_saleSet.add("维修");
		after_saleSet.add("修车");
		after_saleSet.add("保养");
		after_saleSet.add("首保");
		after_saleSet.add("4s");
		after_saleSet.add("4S");
		after_saleSet.add("技术人员");
		after_saleSet.add("师傅");
		after_saleSet.add("技术");
		after_saleSet.add("养护");
		after_saleSet.add("贴车膜");
		after_saleSet.add("喷漆");
		after_saleSet.add("投诉");
		after_saleSet.add("4儿子");
		after_saleSet.add("经销商");
		after_saleSet.add("质保");
		after_saleSet.add("维修站");
		after_saleSet.add("服务站");
		after_saleSet.add("三包");
		after_saleSet.add("索赔");
		after_saleSet.add("接待");
		
		after_saleSet.add("售后");
		after_saleSet.add("维修");
		after_saleSet.add("修车");
		after_saleSet.add("保养");
		after_saleSet.add("首保");
		after_saleSet.add("4s");
		after_saleSet.add("4S");
		after_saleSet.add("SSSS");
		after_saleSet.add("4儿子");
		after_saleSet.add("技术人员");
		after_saleSet.add("技术经理");
		after_saleSet.add("师傅");
		after_saleSet.add("经销商");
		after_saleSet.add("养护");
		after_saleSet.add("贴车膜");
		after_saleSet.add("喷漆");
		after_saleSet.add("投诉");
		after_saleSet.add("三包");
		after_saleSet.add("索赔");
		after_saleSet.add("维修站");
		after_saleSet.add("质保");
		after_saleSet.add("代理商");
		after_saleSet.add("服务站");
		after_saleSet.add("配件");
		after_saleSet.add("改装");
		after_saleSet.add("技师");
	}
}
