package com.voc.service.model.api.clients;

import com.voc.service.common.response.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/*
  未打标数据处理
 */
@FeignClient(name = "service.ins", url = "${service.ins.v1}/model")
public interface InsModelUnmarkedClient {
    @PostMapping("/findModelCustomerList")
    Result<?> findModelCustomerList ();

}
