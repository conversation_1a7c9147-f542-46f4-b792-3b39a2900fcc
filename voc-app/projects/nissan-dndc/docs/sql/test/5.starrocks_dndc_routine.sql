CREATE ROUTINE LOAD ays_meta_data_kafka_c_dndc ON ays_meta_data
COLUMNS( id, work_id,one_id, `data`, source,  create_time,operator, done,model_type,ext_fields, biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.id\",\"$.workId\",\"$.oneId\",\"$.data\",\"$.source\",\"$.create_time\"
		,\"$.operator\",\"$.done\",\"$.modelType\",\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_metaData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);

SHOW ROUTINE LOAD;
-- SHOW ROUTINE LOAD FOR example_tbl2_ordertest9
-- SHOW ROUTINE LOAD TASK WHERE JobName = "example_tbl2_ordertest9"
-- PAUSE  ROUTINE LOAD FOR  ays_meta_data_analysis_kafka_test1
--   RESUME  ROUTINE LOAD FOR  ays_meta_data_analysis_kafka_c_dndc
-- STOP ROUTINE LOAD FOR ays_meta_data_analysis_kafka_c_dndc



CREATE ROUTINE LOAD ays_post_process_data_valid_kafka_c_dndc ON ays_post_process_data_valid
COLUMNS(new_id, id, work_id, client_id, channel_id, original_id, content_type, input_data_id,
		sample_data_type, original_text_scene, brand_code_name, car_series_name, b_tag, q_tag,
		business_label_type_level_first, business_label_type_level_second, business_label_type_level_three,
		business_label_type_level_four, quality_label_type_level_first, quality_label_type_level_second,
		quality_label_type_level_three, quality_label_type_level_four, scenario, sentiment, intention_type,
		topic, opinion, subject, fault_level, description, sentiment_score, keywords,
		publish_time, create_time, update_time, abandon, hit_rules, done,model_type,ext_fields,old_work_id
	)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.workId\",\"$.clientId\",\"$.channelId\",\"$.originalId\",\"$.contentType\"
		,\"$.inputDataId\",\"$.sampleDataType\",\"$.originalTextScene\",\"$.brandCodeName\",\"$.carSeriesName\",\"$.bTag\",\"$.qTag\"
		,\"$.businessLabelTypeLevelFirst\",\"$.businessLabelTypeLevelSecond\",\"$.businessLabelTypeLevelThree\"
		,\"$.businessLabelTypeLevelFour\",\"$.qualityLabelTypeLevelFirst\",\"$.qualityLabelTypeLevelSecond\"
		,\"$.qualityLabelTypeLevelThree\",\"$.qualityLabelTypeLevelFour\",\"$.scenario\",\"$.sentiment\",\"$.intentionType\"
		,\"$.topic\",\"$.opinion\",\"$.subject\",\"$.faultLevel\",\"$.description\",\"$.sentimentScore\",\"$.keywords\"
		,\"$.publishTime\",\"$.createTime\",\"$.updateTime\",\"$.abandon\",\"$.hitRules\",\"$.done\",\"$.modelType\",\"$.extFields\",\"$.oldWorkId\"]"
 )
FROM KAFKA
(
 "kafka_broker_list" = "**************:9092,**************:9092,**************:9092",
 "kafka_topic" = "VDP_processValidPostRulesData_764547797eb2e192763f5334028d49c9",
 "property.group.id" = "VDP-voc-starrocks-dndc",
 "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);

CREATE ROUTINE LOAD ays_meta_data_analysis_kafka_c_dndc ON ays_meta_data_analysis
COLUMNS(new_id, id, one_id, work_id, client_id, channel_id, content_type, title, content, user_name, `data`, done
                    , data_status,model_type,ext_fields, biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3,publish_time, create_time)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.oneId\",\"$.workId\",\"$.clientId\",\"$.channelId\",\"$.contentType\",\"$.title\",\"$.content\"
        ,\"$.userName\",\"$.data\",\"$.done\",\"$.dataStatus\",\"$.modelType\",\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\"
        ,\"$.publishTime\",\"$.createTime\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_metaDataAnalysisData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD ays_pre_process_data_kafka_c_dndc ON ays_pre_process_data
COLUMNS(new_id, id, one_id, work_id, client_id, channel_id, content_type, `data`, data_md5,
	publish_time, create_time, abandon, done, hit_rules,model_type
    ,ext_fields, biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.oneId\",\"$.workId\",\"$.clientId\",\"$.channelId\",\"$.contentType\"
		,\"$.data\",\"$.dataMd5\",\"$.publishTime\",\"$.createTime\",\"$.abandon\",\"$.done\",\"$.hitRules\",\"$.modelType\"
        ,\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_processPreRulesData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);








-- SHOW ROUTINE LOAD FOR ays_pre_process_data_kafka_test
-- SHOW ROUTINE LOAD TASK WHERE JobName = "ays_pre_process_data_kafka_test"
-- stop   ROUTINE LOAD FOR  ays_pre_process_data_kafka_c_dndc


CREATE ROUTINE LOAD ays_api_reslt_data_kafka_c_dndc ON ays_api_reslt_data
COLUMNS(new_id, id, work_id, one_id, client_id, channel_id, original_id, content_type, `data`, data_md5,
	publish_time, create_time, done,model_type,ext_fields, biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.workId\",\"$.oneId\",\"$.clientId\",\"$.channelId\",\"$.originalId\",\"$.contentType\"
		,\"$.data\",\"$.dataMd5\",\"$.publishTime\",\"$.createTime\",\"$.done\",\"$.modelType\"
        ,\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_modelResltData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);





CREATE ROUTINE LOAD ays_api_reslt_data_analysis_kafka_c_dndc ON ays_api_reslt_data_analysis
COLUMNS(new_id, id, work_id, client_id, channel_id, original_id, content_type, input_data_id,
		sample_data_type, original_text_scene, brand_code_name, car_series_name,label_type,
		label_type_level_first, label_type_level_second,label_type_level_three,
		label_type_level_four, label_type_level_five, scenario, sentiment, intention_type,
		topic, opinion, subject, fault_level, description, sentiment_score, keywords, publish_time, create_time,
		update_time, hit_valid_rules, hit_rules, done,model_type,ext_fields,biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3,one_id
	)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.workId\",\"$.clientId\",\"$.channelId\",\"$.originalId\",\"$.contentType\"
		,\"$.inputDataId\",\"$.sampleDataType\",\"$.originalTextScene\",\"$.brandCodeName\",\"$.carSeriesName\"
		,\"$.labelType\",\"$.labelTypeLevelFirst\",\"$.labelTypeLevelSecond\",\"$.labelTypeLevelThree\"
		,\"$.labelTypeLevelFour\",\"$.labelTypeLevelFive\",\"$.scenario\",\"$.sentiment\",\"$.intentionType\"
		,\"$.topic\",\"$.opinion\",\"$.subject\",\"$.faultLevel\",\"$.description\",\"$.sentimentScore\",\"$.keywords\"
		,\"$.publishTime\",\"$.createTime\",\"$.updateTime\",\"$.hitValidRules\",\"$.hitRules\",\"$.done\",\"$.modelType\",\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\",\"$.oneId\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_modelResltAnalysistData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD ays_api_reslt_data_analysis_miss_kafka_c_dndc ON ays_api_reslt_data_analysis_miss
COLUMNS(new_id, id, work_id, one_id, client_id, channel_id, content_type, input_data_id, brand_code_name,
		car_series_name, opinion, opinion_sentiment, subject, description,
		car_body_label, view_label, create_time, update_time, done,model_type,ext_fields, biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3
	)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.workId\",\"$.oneId\",\"$.clientId\",\"$.channelId\",\"$.contentType\"
		,\"$.inputDataId\",\"$.brandCodeName\",\"$.carSeriesName\",\"$.opinion\",\"$.opinionSentiment\",\"$.subject\"
		,\"$.description\",\"$.carBodyLabel\",\"$.viewLabel\"
		,\"$.createTime\",\"$.updateTime\",\"$.done\",\"$.modelType\",\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_processPostRulesDataMisss_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD ays_error_push_data_kafka_c_dndc ON ays_error_push_data
COLUMNS(id, `table`, `action`, work_id, client_id, `data`, create_time, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.id\",\"$.table\",\"$.action\",\"$.workId\",\"$.clientId\"
		,\"$.data\",\"$.create_time\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_errorPushData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);



CREATE ROUTINE LOAD ays_post_process_data_kafka_c_dndc ON ays_post_process_data
COLUMNS(new_id, id, work_id, client_id, channel_id, original_id, content_type, input_data_id,
		sample_data_type, original_text_scene, brand_code_name, car_series_name, label_type,
		label_type_level_first, label_type_level_second,label_type_level_three,
		label_type_level_four, label_type_level_five,  scenario, sentiment, intention_type,
		topic, opinion, subject, fault_level, description, sentiment_score, keywords,
		publish_time, create_time, update_time, abandon, hit_rules, done,model_type,ext_fields,biz_ext_attrs,biz_ext_attrs2,biz_ext_attrs3,one_id
	)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.newId\",\"$.id\",\"$.workId\",\"$.clientId\",\"$.channelId\",\"$.originalId\",\"$.contentType\"
		,\"$.inputDataId\",\"$.sampleDataType\",\"$.originalTextScene\",\"$.brandCodeName\",\"$.carSeriesName\",\"$.labelType\",\"$.labelTypeLevelFirst\",\"$.labelTypeLevelSecond\",\"$.labelTypeLevelThree\"
		,\"$.labelTypeLevelFour\",\"$.labelTypeLevelFive\",\"$.scenario\",\"$.sentiment\",\"$.intentionType\"
		,\"$.topic\",\"$.opinion\",\"$.subject\",\"$.faultLevel\",\"$.description\",\"$.sentimentScore\",\"$.keywords\"
		,\"$.publishTime\",\"$.createTime\",\"$.updateTime\",\"$.abandon\",\"$.hitRules\",\"$.done\",\"$.modelType\",\"$.extFields\",\"$.bizExtAttrs\",\"$.bizExtAttrs2\",\"$.bizExtAttrs3\",\"$.oneId\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_processPostRulesData_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD ays_batch_push_record_kafka_c_dndc ON ays_batch_push_record
COLUMNS(id, reqeut_id, work_id, `status`, `source`, create_time, update_time,model_type,ext_fields, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.id\",\"$.reqeutId\",\"$.workId\",\"$.status\"
		,\"$.source\",\"$.createTime\",\"$.updateTime\",\"$.modelType\",\"$.extFields\",\"$.tid\"]"
 )
FROM KAFKA
(
    "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_aysBatchPushRecord_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);




CREATE ROUTINE LOAD ods_channel_execution_result_kafka_c_dndc ON ods_channel_execution_result
COLUMNS(id, data_id, work_id, channel_type, retry_count, error_code, error_msg, `data`, create_time, last_exec_time, status, tid)
PROPERTIES
(
	"trim_space"="true",
    "enclose"="\"",
    "escape"="\\",
    "desired_concurrent_number" = "5",
    "format" = "json",
    "jsonpaths" = "[\"$.id\",\"$.dataId\",\"$.workId\",\"$.channelType\",\"$.retryCount\",\"$.errorCode\",\"$.errorMsg\",
            \"$.data\",\"$.createTime\",\"$.lastExecTime\",\"$.status\",\"$.tid\"]"
 )
FROM KAFKA
(
   "kafka_broker_list" = "kafka-project-svc.middleware-test.svc.cluster.local:9092",
    "kafka_topic" = "VDP_channelExecutionResult_764547797eb2e192763f5334028d49c9",
    "property.group.id" = "VDP-voc-starrocks-dndc",
    "property.kafka_default_offsets" = "OFFSET_BEGINNING"
);


