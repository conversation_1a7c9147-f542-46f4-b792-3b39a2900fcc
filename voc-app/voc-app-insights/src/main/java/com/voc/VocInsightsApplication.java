package com.voc;


import cn.hutool.core.util.StrUtil;
import com.voc.service.config.JasyptConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.net.InetAddress;

@Slf4j
@SuppressWarnings("unchecked")
//@ServletComponentScan
@SpringBootApplication
@Import(JasyptConfiguration.class)
public class VocInsightsApplication {

    public static void main(String[] args) throws IOException {

        SpringApplication startupSpringApplication
                = new SpringApplication(VocInsightsApplication.class);
        ConfigurableApplicationContext applicaiton = startupSpringApplication.run(args);
        log.info("rpc_tr_port={}", System.getProperty("rpc_tr_port"));
        Environment env = applicaiton.getEnvironment();
        String appname = env.getProperty("spring.application.name");
        String prefix = env.getProperty("server.servlet.context-path");
        String vhost = env.getProperty("server.vhost");
        String vport = env.getProperty("server.vport");
        String port = StrUtil.isBlank(env.getProperty("server.port")) ? "8080" : env.getProperty("server.port");
        log.info("--->> biz:{} added", VocInsightsApplication.class.getSimpleName());
        log.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:{}{}\n\t" +
                        "External: \thttp://{}:{}\n\t" +
                        "Doc: \thttp://{}:{}/doc.html\n" +
                        "----------------------------------------------------------",
                appname,
                port,
                StrUtil.isBlank(prefix) ? "" : prefix,
                StrUtil.isBlank(vhost) ? InetAddress.getLocalHost().getHostAddress() : vhost,
                StrUtil.isBlank(vport) ? port : vport,
                StrUtil.isBlank(vhost) ? InetAddress.getLocalHost().getHostAddress() : vhost,
                StrUtil.isBlank(vport) ? port : vport
        );

    }
}
